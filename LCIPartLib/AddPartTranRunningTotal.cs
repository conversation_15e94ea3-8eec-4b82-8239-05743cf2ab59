/*
 * =====================================================
 * LCI Part Library - Add Part Transaction Running Total
 * =====================================================
 *
 * Purpose: Takes JSON string with part transaction data and returns a DataSet with running inventory balance per bin
 *
 * Parameters:
 *   - jsonData (string): JSON string containing part transaction data from dataView
 *
 * Returns:
 *   - result (System.Data.DataSet): Modified DataSet with additional RunningTotal column
 *   - errorMessage (string): Error message if processing fails, empty string if successful
 *
 * Process:
 *   1. Parse the JSON string to extract transaction data
 *   2. Create a DataSet with the transaction data structure
 *   3. Query current on-hand quantities per bin from PartBin table
 *   4. Validate plant relationships through PartWhse → Plant chain
 *   5. Sort data in descending chronological order (newest first)
 *   6. Calculate running inventory balance working backwards from current on-hand
 *   7. Return the DataSet with running inventory balance column
 *
 * Note: Running inventory balance calculated per bin, working backwards from current PartBin.OnhandQty
 *       Uses PartTran_ActTranQty sign (+ receipts, - issues) and validates plant relationships
 *
 * Usage Example:
 *   // jsonData = JSON.stringify(trans.dataView('LCI_PartTransHistory').viewData)
 *   // Execute function
 *   // result DataSet will contain transaction data plus RunningTotal column
 */

// Initialize result DataSet and error message
result = new DataSet("PartTransactionsWithRunningTotal");
errorMessage = "";

try
{
    // Validate input JSON
    if (string.IsNullOrEmpty(jsonData))
    {
        errorMessage = "Input JSON data is null or empty";
        return;
    }

    // Parse JSON data using Newtonsoft.Json (available in Epicor)
    dynamic jsonObject = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonData);

    if (jsonObject == null)
    {
        errorMessage = "Failed to parse JSON data";
        return;
    }

    // Create DataTable with all the expected columns
    DataTable resultTable = new DataTable("PartTransactions");

    // Add all the columns based on the structure you provided
    resultTable.Columns.Add("Calculated_EmpID", typeof(string));
    resultTable.Columns.Add("Calculated_EmpName", typeof(string));
    resultTable.Columns.Add("PartTran_ActTranQty", typeof(decimal));
    resultTable.Columns.Add("PartTran_ActTransUOM", typeof(string));
    resultTable.Columns.Add("PartTran_BegMtlBurUnitCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_BegMtlUnitCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_EntryPerson", typeof(string));
    resultTable.Columns.Add("PartTran_ExtCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_JobNum", typeof(string));
    resultTable.Columns.Add("PartTran_LegalNumber", typeof(string));
    resultTable.Columns.Add("PartTran_MtlBurUnitCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_MtlBurdenUnitCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_MtlLabUnitCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_MtlMtlUnitCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_MtlSubUnitCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_MtlUnitCost", typeof(decimal));
    resultTable.Columns.Add("PartTran_OrderNum", typeof(int));
    resultTable.Columns.Add("PartTran_PONum", typeof(int));
    resultTable.Columns.Add("PartTran_PackNum", typeof(int));
    resultTable.Columns.Add("PartTran_PackSlip", typeof(string));
    resultTable.Columns.Add("PartTran_RevisionNum", typeof(string));
    resultTable.Columns.Add("PartTran_SysDate", typeof(DateTime));
    resultTable.Columns.Add("PartTran_SysTime", typeof(int));
    resultTable.Columns.Add("PartTran_TranDate", typeof(DateTime));
    resultTable.Columns.Add("PartTran_TranNum", typeof(int));
    resultTable.Columns.Add("PartTran_TranQty", typeof(decimal));
    resultTable.Columns.Add("PartTran_TranType", typeof(string));
    resultTable.Columns.Add("PartTran_UM", typeof(string));
    resultTable.Columns.Add("Plant_Name", typeof(string));
    resultTable.Columns.Add("Warehse_Description", typeof(string));
    resultTable.Columns.Add("WhseBin_BinNum", typeof(string));

    // Add the running total column
    resultTable.Columns.Add("RunningTotal", typeof(decimal));

    // Populate the DataTable with JSON data
    foreach (var item in jsonObject)
    {
        DataRow row = resultTable.NewRow();

        // Safely populate each column with null checking and inline parsing
        row["Calculated_EmpID"] = item["Calculated_EmpID"]?.ToString() ?? "";
        row["Calculated_EmpName"] = item["Calculated_EmpName"]?.ToString() ?? "";

        // Parse decimal values with fallback to 0
        decimal actTranQty = 0;
        if (item["PartTran_ActTranQty"] != null && decimal.TryParse(item["PartTran_ActTranQty"].ToString(), out actTranQty)) { }
        row["PartTran_ActTranQty"] = actTranQty;

        row["PartTran_ActTransUOM"] = item["PartTran_ActTransUOM"]?.ToString() ?? "";

        decimal begMtlBurUnitCost = 0;
        if (item["PartTran_BegMtlBurUnitCost"] != null && decimal.TryParse(item["PartTran_BegMtlBurUnitCost"].ToString(), out begMtlBurUnitCost)) { }
        row["PartTran_BegMtlBurUnitCost"] = begMtlBurUnitCost;

        decimal begMtlUnitCost = 0;
        if (item["PartTran_BegMtlUnitCost"] != null && decimal.TryParse(item["PartTran_BegMtlUnitCost"].ToString(), out begMtlUnitCost)) { }
        row["PartTran_BegMtlUnitCost"] = begMtlUnitCost;

        row["PartTran_EntryPerson"] = item["PartTran_EntryPerson"]?.ToString() ?? "";

        decimal extCost = 0;
        if (item["PartTran_ExtCost"] != null && decimal.TryParse(item["PartTran_ExtCost"].ToString(), out extCost)) { }
        row["PartTran_ExtCost"] = extCost;

        row["PartTran_JobNum"] = item["PartTran_JobNum"]?.ToString() ?? "";
        row["PartTran_LegalNumber"] = item["PartTran_LegalNumber"]?.ToString() ?? "";

        // Continue with remaining decimal fields
        decimal mtlBurUnitCost = 0;
        if (item["PartTran_MtlBurUnitCost"] != null && decimal.TryParse(item["PartTran_MtlBurUnitCost"].ToString(), out mtlBurUnitCost)) { }
        row["PartTran_MtlBurUnitCost"] = mtlBurUnitCost;

        decimal mtlBurdenUnitCost = 0;
        if (item["PartTran_MtlBurdenUnitCost"] != null && decimal.TryParse(item["PartTran_MtlBurdenUnitCost"].ToString(), out mtlBurdenUnitCost)) { }
        row["PartTran_MtlBurdenUnitCost"] = mtlBurdenUnitCost;

        decimal mtlLabUnitCost = 0;
        if (item["PartTran_MtlLabUnitCost"] != null && decimal.TryParse(item["PartTran_MtlLabUnitCost"].ToString(), out mtlLabUnitCost)) { }
        row["PartTran_MtlLabUnitCost"] = mtlLabUnitCost;

        decimal mtlMtlUnitCost = 0;
        if (item["PartTran_MtlMtlUnitCost"] != null && decimal.TryParse(item["PartTran_MtlMtlUnitCost"].ToString(), out mtlMtlUnitCost)) { }
        row["PartTran_MtlMtlUnitCost"] = mtlMtlUnitCost;

        decimal mtlSubUnitCost = 0;
        if (item["PartTran_MtlSubUnitCost"] != null && decimal.TryParse(item["PartTran_MtlSubUnitCost"].ToString(), out mtlSubUnitCost)) { }
        row["PartTran_MtlSubUnitCost"] = mtlSubUnitCost;

        decimal mtlUnitCost = 0;
        if (item["PartTran_MtlUnitCost"] != null && decimal.TryParse(item["PartTran_MtlUnitCost"].ToString(), out mtlUnitCost)) { }
        row["PartTran_MtlUnitCost"] = mtlUnitCost;

        // Parse integer fields
        int orderNum = 0;
        if (item["PartTran_OrderNum"] != null && int.TryParse(item["PartTran_OrderNum"].ToString(), out orderNum)) { }
        row["PartTran_OrderNum"] = orderNum;

        int poNum = 0;
        if (item["PartTran_PONum"] != null && int.TryParse(item["PartTran_PONum"].ToString(), out poNum)) { }
        row["PartTran_PONum"] = poNum;

        int packNum = 0;
        if (item["PartTran_PackNum"] != null && int.TryParse(item["PartTran_PackNum"].ToString(), out packNum)) { }
        row["PartTran_PackNum"] = packNum;

        row["PartTran_PackSlip"] = item["PartTran_PackSlip"]?.ToString() ?? "";
        row["PartTran_RevisionNum"] = item["PartTran_RevisionNum"]?.ToString() ?? "";

        // Parse DateTime fields
        DateTime sysDate = DateTime.MinValue;
        if (item["PartTran_SysDate"] != null && DateTime.TryParse(item["PartTran_SysDate"].ToString(), out sysDate)) { }
        row["PartTran_SysDate"] = sysDate == DateTime.MinValue ? (object)DBNull.Value : sysDate;

        int sysTime = 0;
        if (item["PartTran_SysTime"] != null && int.TryParse(item["PartTran_SysTime"].ToString(), out sysTime)) { }
        row["PartTran_SysTime"] = sysTime;

        DateTime tranDate = DateTime.MinValue;
        if (item["PartTran_TranDate"] != null && DateTime.TryParse(item["PartTran_TranDate"].ToString(), out tranDate)) { }
        row["PartTran_TranDate"] = tranDate == DateTime.MinValue ? (object)DBNull.Value : tranDate;

        int tranNum = 0;
        if (item["PartTran_TranNum"] != null && int.TryParse(item["PartTran_TranNum"].ToString(), out tranNum)) { }
        row["PartTran_TranNum"] = tranNum;

        decimal tranQty = 0;
        if (item["PartTran_TranQty"] != null && decimal.TryParse(item["PartTran_TranQty"].ToString(), out tranQty)) { }
        row["PartTran_TranQty"] = tranQty;

        row["PartTran_TranType"] = item["PartTran_TranType"]?.ToString() ?? "";
        row["PartTran_UM"] = item["PartTran_UM"]?.ToString() ?? "";
        row["Plant_Name"] = item["Plant_Name"]?.ToString() ?? "";
        row["Warehse_Description"] = item["Warehse_Description"]?.ToString() ?? "";
        row["WhseBin_BinNum"] = item["WhseBin_BinNum"]?.ToString() ?? "";

        resultTable.Rows.Add(row);
    }

    // Get current on-hand quantities per bin and validate plant relationships
    var binOnHandQuantities = new Dictionary<string, decimal>();
    string companyID = "162250";  // LCI company ID

    // Get unique combinations of bins, warehouses, and plants from the transaction data
    var uniqueBinWarehousePlants = resultTable.AsEnumerable()
        .Select(row => new {
            BinNum = row["WhseBin_BinNum"]?.ToString() ?? "",
            WarehouseDesc = row["Warehse_Description"]?.ToString() ?? "",
            PlantName = row["Plant_Name"]?.ToString() ?? ""
        })
        .Where(x => !string.IsNullOrEmpty(x.BinNum) && !string.IsNullOrEmpty(x.WarehouseDesc) && !string.IsNullOrEmpty(x.PlantName))
        .Distinct()
        .ToList();

    foreach (var binInfo in uniqueBinWarehousePlants)
    {
        try
        {
            // Get WarehouseCode from Warehse table using Description
            var warehouse = Db.Warehse
                .FirstOrDefault(w => w.Company == companyID && w.Description == binInfo.WarehouseDesc);

            if (warehouse == null) continue;

            // Validate plant relationship through PartWhse → Plant chain
            var partWhse = Db.PartWhse
                .FirstOrDefault(pw => pw.Company == companyID && pw.WarehouseCode == warehouse.WarehouseCode);

            if (partWhse == null) continue;

            var plant = Db.Plant
                .FirstOrDefault(p => p.Company == companyID && p.Plant1 == partWhse.KBPlant);

            if (plant == null || plant.Name != binInfo.PlantName) continue;

            // Get current on-hand quantity from PartBin
            var partBin = Db.PartBin
                .FirstOrDefault(pb => pb.Company == companyID &&
                                     pb.BinNum == binInfo.BinNum &&
                                     pb.WarehouseCode == warehouse.WarehouseCode);

            if (partBin != null)
            {
                string binKey = $"{binInfo.BinNum}|{binInfo.WarehouseDesc}|{binInfo.PlantName}";
                binOnHandQuantities[binKey] = partBin.OnhandQty;
            }
        }
        catch
        {
            // Skip this bin if any database query fails
            continue;
        }
    }

    // Group transactions by bin and calculate running inventory balance
    var binGroups = resultTable.AsEnumerable()
        .GroupBy(row => new {
            BinNum = row["WhseBin_BinNum"]?.ToString() ?? "",
            WarehouseDesc = row["Warehse_Description"]?.ToString() ?? "",
            PlantName = row["Plant_Name"]?.ToString() ?? ""
        })
        .Where(g => !string.IsNullOrEmpty(g.Key.BinNum));

    foreach (var binGroup in binGroups)
    {
        string binKey = $"{binGroup.Key.BinNum}|{binGroup.Key.WarehouseDesc}|{binGroup.Key.PlantName}";
        decimal currentOnHand = binOnHandQuantities.ContainsKey(binKey) ? binOnHandQuantities[binKey] : 0;

        // Sort transactions in DESCENDING order (newest first) for working backwards
        var sortedTransactions = binGroup.OrderByDescending(row => {
            DateTime tranDate = DateTime.MinValue;
            if (row["PartTran_TranDate"] != null && row["PartTran_TranDate"] != DBNull.Value)
                DateTime.TryParse(row["PartTran_TranDate"].ToString(), out tranDate);
            return tranDate;
        }).ThenByDescending(row => {
            int tranNum = 0;
            if (row["PartTran_TranNum"] != null && row["PartTran_TranNum"] != DBNull.Value)
                int.TryParse(row["PartTran_TranNum"].ToString(), out tranNum);
            return tranNum;
        }).ToList();

        // Calculate running inventory balance working backwards
        decimal runningBalance = currentOnHand;

        foreach (DataRow row in sortedTransactions)
        {
            // Set the running total for this row (before this transaction occurred)
            row["RunningTotal"] = runningBalance;

            // Get the transaction quantity and work backwards
            decimal tranQty = 0;
            if (row["PartTran_ActTranQty"] != null && row["PartTran_ActTranQty"] != DBNull.Value)
            {
                decimal.TryParse(row["PartTran_ActTranQty"].ToString(), out tranQty);
            }

            // Work backwards: Previous balance = Current balance + Transaction quantity
            // (because we're going backwards in time, we reverse the transaction effect)
            runningBalance += tranQty;
        }
    }
    
    // Add the result table to the result DataSet
    result.Tables.Add(resultTable);
}
catch (Exception ex)
{
    // Clear result and set error message
    result = new DataSet("PartTransactionsWithRunningTotal");
    errorMessage = "Error calculating running total for part transactions: " + ex.Message;
}
