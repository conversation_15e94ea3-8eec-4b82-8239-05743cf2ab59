/*
 * =====================================================
 * LCI Part Library - Verify Part Revision
 * =====================================================
 *
 * Purpose: Verifies if a revision number exists for a given part number, is approved, and returns the correctly cased revision
 *
 * Parameters:
 *   - PartNum (string): Part number to search for
 *   - RevisionNum (string): Revision number to verify (case-insensitive input)
 *
 * Returns:
 *   - result (string): Correctly cased RevisionNum if found and approved, error message if not found or not approved
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Query PartRev table with case-insensitive comparison for the revision
 *   2. Verify the revision is approved (Approved = true)
 *   3. Return the actual RevisionNum field with correct casing if record found and approved
 *   4. Return error message if no matching record found or revision not approved
 *
 * Note: Input RevisionNum is case-insensitive, but output preserves database casing
 *       Only approved revisions are considered valid
 *
 * Usage Example:
 *   PartNum = "ABC123";
 *   RevisionNum = "rev01";  // Case doesn't matter for input
 *   // Execute function
 *   // result will be "REV01" (or whatever the correct casing is) or error message
 */

// Initialize variables
string companyID = "162250";  // LCI company ID
result = "ERR";               // Default to error state

try
{
    // First check if the revision exists at all (regardless of approval status)
    var partRevisionExists = Db.PartRev
        .FirstOrDefault(pr => pr.Company == companyID &&
                             pr.PartNum == PartNum &&
                             pr.RevisionNum.ToUpper() == RevisionNum.ToUpper());

    // Query PartRev table for the specific part and revision combination
    // Use case-insensitive comparison for the revision number input
    // Only return approved revisions
    var partRevision = Db.PartRev
        .FirstOrDefault(pr => pr.Company == companyID &&
                             pr.PartNum == PartNum &&
                             pr.RevisionNum.ToUpper() == RevisionNum.ToUpper() &&
                             pr.Approved == true);

    // Check if part revision record was found and approved
    if (partRevision != null)
    {
        // Return the correctly cased revision number from the database
        result = partRevision.RevisionNum ?? "";  // Handle potential null values
    }
    else if (partRevisionExists != null)
    {
        // Revision exists but is not approved
        result = "ERR_NOT_APPROVED";
    }
    else
    {
        // No matching record found
        result = "ERR";
    }
}
catch (Exception ex)
{
    // Database query failed - return error with exception details
    result = "ERR";
}
