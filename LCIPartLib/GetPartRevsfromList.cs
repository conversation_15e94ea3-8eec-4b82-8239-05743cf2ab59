//UNTESTED

/*
 * =====================================================
 * LCI Part Library - Get Part Revisions from List
 * =====================================================
 *
 * Purpose: Takes a DataSet with Part.PartNum data and returns all revisions for those parts
 *
 * Parameters:
 *   - inputSet (System.Data.DataSet): DataSet containing Part.PartNum values
 *
 * Returns:
 *   - result (System.Data.DataSet): DataSet containing all PartRev records for the input parts
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Extract all PartNum values from the input DataSet
 *   2. Query PartRev table for all revisions matching those part numbers
 *   3. Build and return a DataSet containing all revision data
 *   4. Handle cases where parts have multiple revisions
 *
 * Note: Returns all revisions for each part, maintaining PartRev.PartNum = Part.PartNum relationship
 *
 * Usage Example:
 *   // inputSet contains Part table with PartNum column
 *   // Execute function
 *   // result DataSet will contain PartRev table with all revisions for those parts
 */

// Initialize variables
string companyID = "162250";  // LCI company ID
result = new DataSet("PartRevisions");  // Initialize result DataSet

try
{
    // Validate input DataSet
    if (inputSet == null || inputSet.Tables.Count == 0)
    {
        // Return empty DataSet if input is invalid
        return;
    }

    // Extract PartNum values from input DataSet
    var partNumbers = new List<string>();
    
    // Look for Part table or any table containing PartNum column
    DataTable partTable = null;
    foreach (DataTable table in inputSet.Tables)
    {
        if (table.Columns.Contains("PartNum"))
        {
            partTable = table;
            break;
        }
    }
    
    // If no table with PartNum found, return empty result
    if (partTable == null)
    {
        return;
    }
    
    // Extract all unique PartNum values
    foreach (DataRow row in partTable.Rows)
    {
        if (row["PartNum"] != null && row["PartNum"] != DBNull.Value)
        {
            string partNum = row["PartNum"].ToString();
            if (!string.IsNullOrEmpty(partNum) && !partNumbers.Contains(partNum))
            {
                partNumbers.Add(partNum);
            }
        }
    }
    
    // If no part numbers found, return empty result
    if (partNumbers.Count == 0)
    {
        return;
    }
    
    // Query PartRev table for all revisions of the specified parts
    var partRevisions = Db.PartRev
        .Where(pr => pr.Company == companyID && partNumbers.Contains(pr.PartNum))
        .OrderBy(pr => pr.PartNum)
        .ThenBy(pr => pr.RevisionNum)
        .ToList();
    
    // Create result DataTable with PartRev structure
    DataTable revisionTable = new DataTable("PartRev");
    
    // Add columns based on PartRev entity structure
    revisionTable.Columns.Add("Company", typeof(string));
    revisionTable.Columns.Add("PartNum", typeof(string));
    revisionTable.Columns.Add("RevisionNum", typeof(string));
    revisionTable.Columns.Add("RevShortDesc", typeof(string));
    revisionTable.Columns.Add("RevDescription", typeof(string));
    revisionTable.Columns.Add("Approved", typeof(bool));
    revisionTable.Columns.Add("ApprovedDate", typeof(DateTime));
    revisionTable.Columns.Add("ApprovedBy", typeof(string));
    revisionTable.Columns.Add("EffectiveDate", typeof(DateTime));
    
    // Populate the DataTable with revision data
    foreach (var revision in partRevisions)
    {
        DataRow row = revisionTable.NewRow();
        row["Company"] = revision.Company ?? "";
        row["PartNum"] = revision.PartNum ?? "";
        row["RevisionNum"] = revision.RevisionNum ?? "";
        row["RevShortDesc"] = revision.RevShortDesc ?? "";
        row["RevDescription"] = revision.RevDescription ?? "";
        row["Approved"] = revision.Approved;
        row["ApprovedDate"] = revision.ApprovedDate ?? (object)DBNull.Value;
        row["ApprovedBy"] = revision.ApprovedBy ?? "";
        row["EffectiveDate"] = revision.EffectiveDate ?? (object)DBNull.Value;
        
        revisionTable.Rows.Add(row);
    }
    
    // Add the table to the result DataSet
    result.Tables.Add(revisionTable);
}
catch (Exception ex)
{
    // Create error table in case of exception
    DataTable errorTable = new DataTable("Error");
    errorTable.Columns.Add("ErrorMessage", typeof(string));
    errorTable.Columns.Add("ErrorDetails", typeof(string));
    
    DataRow errorRow = errorTable.NewRow();
    errorRow["ErrorMessage"] = "Error retrieving part revisions";
    errorRow["ErrorDetails"] = ex.Message;
    errorTable.Rows.Add(errorRow);
    
    result.Tables.Clear();
    result.Tables.Add(errorTable);
}
