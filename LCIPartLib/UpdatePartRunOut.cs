/*
 * =====================================================
 * LCI Part Library - Update Part RunOut Status
 * =====================================================
 *
 * Purpose: Updates the RunOut flag for a specific part in Epicor
 *
 * Parameters:
 *   - partNum (string): Part number to update
 *   - runOut (bool): true to set Part.RunOut = true, false to set Part.RunOut = false
 *
 * Returns:
 *   - result (string): "OK" if successful, "ERR" followed by error details if failed
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Find the part in the database using partNum
 *   2. Validate part exists and is accessible
 *   3. Update the RunOut field directly in database
 *   4. Save changes via Db.SaveChanges()
 *
 * Notes:
 *   - Uses direct database access for immediate update
 *   - RunOut flag typically indicates part is being discontinued
 *   - Part must exist in the system to be updated
 *   - Updates are immediate and persistent
 *
 * Usage Example:
 *   partNum = "ABC123";
 *   runOut = true;  // Set part as run out
 *   // Execute function
 *   // result will be "OK" or "ERR: error details"
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = "ERR: Unknown error";         // Default to error state

// Validate input parameters
if (string.IsNullOrEmpty(partNum))
{
    result = "ERR: Part number cannot be null or empty";
    return;
}

// Execute part update using Epicor Part service
this.CallService<Erp.Contracts.PartSvcContract>(svc =>
{
    try
    {
        // Get the part by part number
        var ts = svc.GetByID(partNum);

        // Check if part exists
        if (ts.Part == null || ts.Part.Count == 0)
        {
            result = $"ERR: Part '{partNum}' not found";
            return;
        }

        // Get the first part record
        var partRow = ts.Part[0];

        // Update the RunOut field
        partRow.RunOut = runOut;
        partRow.RowMod = "U";  // Mark for update

        // Save the changes
        svc.Update(ref ts);

        // Return success message
        result = "OK";
    }
    catch (Exception ex)
    {
        // Handle any errors and provide detailed error information
        result = $"ERR: {ex.Message}";
    }
});
