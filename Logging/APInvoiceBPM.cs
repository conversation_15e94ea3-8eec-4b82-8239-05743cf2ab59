// =================================================================
// AP Invoice BPM - Comprehensive Change Tracking
// =================================================================
// This BPM tracks changes to AP Invoice Header and Detail records
// and logs them using the backtick-tilde-backtick (`~`) delimiter
// with 1000-character chunking across changesMade variables.
// =================================================================

// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;
InvoiceNumber = "";

// Validate dataset before processing
if (ds.APInvHed == null || ds.APInvHed.Count == 0)
{
    test1 = "Error: No AP invoice data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.APInvHed.Count.ToString()} AP invoice records";

// Track all changes in a list
List<string> changes = new List<string>();

// =================================================================
// APInvHed (Header) Change Tracking
// =================================================================

// Get the modified AP invoice record
var modifiedAPInvHed = ds.APInvHed[0];
invoiceNum = modifiedAPInvHed.InvoiceNum;
vendorNum = modifiedAPInvHed.VendorNum;
InvoiceNumber = invoiceNum.ToString();
test2 = "Debug: Successfully accessed ds.APInvHed[0]";

// Check if the AP invoice row has been deleted (RowMod = "D" in original record)
if (ds.APInvHed[0].RowMod == "D")
{
    changes.Add("APInvHed deleted");
    callFunc = true;
}
else
{
    // Get the original AP invoice record from database
    var originalAPInvHed = (from dbAPInvHed in Db.APInvHed
                           where dbAPInvHed.Company == companyID
                              && dbAPInvHed.VendorNum == vendorNum
                              && dbAPInvHed.InvoiceNum == invoiceNum
                           select dbAPInvHed).FirstOrDefault();

    // Handle new AP invoice creation
    if (originalAPInvHed == null)
    {
        changes.Add($"New AP invoice created: {modifiedAPInvHed.InvoiceNum.ToString()}");
        callFunc = true;
    }
    else
    {
        // Determine which record to use as the modified version
        // Check if there's a second record (modified version)
        if (ds.APInvHed.Count > 1)
        {
            try
            {
                modifiedAPInvHed = ds.APInvHed[1];
                test2 = "Debug: Successfully accessed ds.APInvHed[1]";
            }
            catch (System.Exception ex)
            {
                test2 = $"Error accessing ds.APInvHed[1]: {ex.Message}";
                return;
            }
        }
        else
        {
            test2 = "Debug: Using ds.APInvHed[0] as modified (only 1 record)";
        }

        // =================================================================
        // APInvHed Field Comparisons
        // =================================================================

        // Company
        if (modifiedAPInvHed.Company != originalAPInvHed.Company)
        {
            changes.Add("APInvHed.Company`"
                       + originalAPInvHed.Company.ToString()
                       + "`"
                       + modifiedAPInvHed.Company.ToString().Replace("`", "-"));
        }

        // Open Payable
        if (modifiedAPInvHed.OpenPayable != originalAPInvHed.OpenPayable)
        {
            changes.Add("APInvHed.OpenPayable`"
                       + originalAPInvHed.OpenPayable.ToString()
                       + "`"
                       + modifiedAPInvHed.OpenPayable.ToString().Replace("`", "-"));
        }

        // Vendor Number
        if (modifiedAPInvHed.VendorNum != originalAPInvHed.VendorNum)
        {
            changes.Add("APInvHed.VendorNum`"
                       + originalAPInvHed.VendorNum.ToString()
                       + "`"
                       + modifiedAPInvHed.VendorNum.ToString().Replace("`", "-"));
        }

        // Invoice Number
        if (modifiedAPInvHed.InvoiceNum != originalAPInvHed.InvoiceNum)
        {
            changes.Add("APInvHed.InvoiceNum`"
                       + originalAPInvHed.InvoiceNum.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceNum.ToString().Replace("`", "-"));
        }

        // Debit Memo
        if (modifiedAPInvHed.DebitMemo != originalAPInvHed.DebitMemo)
        {
            changes.Add("APInvHed.DebitMemo`"
                       + originalAPInvHed.DebitMemo.ToString()
                       + "`"
                       + modifiedAPInvHed.DebitMemo.ToString().Replace("`", "-"));
        }

        // Invoice Date
        if (modifiedAPInvHed.InvoiceDate != originalAPInvHed.InvoiceDate)
        {
            changes.Add("APInvHed.InvoiceDate`"
                       + originalAPInvHed.InvoiceDate.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceDate.ToString().Replace("`", "-"));
        }

        // Terms Code
        if (modifiedAPInvHed.TermsCode != originalAPInvHed.TermsCode)
        {
            changes.Add("APInvHed.TermsCode`"
                       + originalAPInvHed.TermsCode.ToString()
                       + "`"
                       + modifiedAPInvHed.TermsCode.ToString().Replace("`", "-"));
        }

        // Tax Amount
        if (modifiedAPInvHed.TaxAmt != originalAPInvHed.TaxAmt)
        {
            changes.Add("APInvHed.TaxAmt`"
                       + originalAPInvHed.TaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxAmt.ToString().Replace("`", "-"));
        }

        // Document Tax Amount
        if (modifiedAPInvHed.DocTaxAmt != originalAPInvHed.DocTaxAmt)
        {
            changes.Add("APInvHed.DocTaxAmt`"
                       + originalAPInvHed.DocTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocTaxAmt.ToString().Replace("`", "-"));
        }

        // Discount Date
        if (modifiedAPInvHed.DiscountDate != originalAPInvHed.DiscountDate)
        {
            changes.Add("APInvHed.DiscountDate`"
                       + originalAPInvHed.DiscountDate.ToString()
                       + "`"
                       + modifiedAPInvHed.DiscountDate.ToString().Replace("`", "-"));
        }

        // Discount Amount
        if (modifiedAPInvHed.DiscountAmt != originalAPInvHed.DiscountAmt)
        {
            changes.Add("APInvHed.DiscountAmt`"
                       + originalAPInvHed.DiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DiscountAmt.ToString().Replace("`", "-"));
        }

        // Document Discount Amount
        if (modifiedAPInvHed.DocDiscountAmt != originalAPInvHed.DocDiscountAmt)
        {
            changes.Add("APInvHed.DocDiscountAmt`"
                       + originalAPInvHed.DocDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocDiscountAmt.ToString().Replace("`", "-"));
        }

        // Due Date
        if (modifiedAPInvHed.DueDate != originalAPInvHed.DueDate)
        {
            changes.Add("APInvHed.DueDate`"
                       + originalAPInvHed.DueDate.ToString()
                       + "`"
                       + modifiedAPInvHed.DueDate.ToString().Replace("`", "-"));
        }

        // Pay Dates
        if (modifiedAPInvHed.PayDates != originalAPInvHed.PayDates)
        {
            changes.Add("APInvHed.PayDates`"
                       + originalAPInvHed.PayDates.ToString()
                       + "`"
                       + modifiedAPInvHed.PayDates.ToString().Replace("`", "-"));
        }

        // Pay Amounts
        if (modifiedAPInvHed.PayAmounts != originalAPInvHed.PayAmounts)
        {
            changes.Add("APInvHed.PayAmounts`"
                       + originalAPInvHed.PayAmounts.ToString()
                       + "`"
                       + modifiedAPInvHed.PayAmounts.ToString().Replace("`", "-"));
        }

        // Document Pay Amounts
        if (modifiedAPInvHed.DocPayAmounts != originalAPInvHed.DocPayAmounts)
        {
            changes.Add("APInvHed.DocPayAmounts`"
                       + originalAPInvHed.DocPayAmounts.ToString()
                       + "`"
                       + modifiedAPInvHed.DocPayAmounts.ToString().Replace("`", "-"));
        }

        // GL Posted
        if (modifiedAPInvHed.GLPosted != originalAPInvHed.GLPosted)
        {
            changes.Add("APInvHed.GLPosted`"
                       + originalAPInvHed.GLPosted.ToString()
                       + "`"
                       + modifiedAPInvHed.GLPosted.ToString().Replace("`", "-"));
        }

        // Group ID
        if (modifiedAPInvHed.GroupID != originalAPInvHed.GroupID)
        {
            changes.Add("APInvHed.GroupID`"
                       + originalAPInvHed.GroupID.ToString()
                       + "`"
                       + modifiedAPInvHed.GroupID.ToString().Replace("`", "-"));
        }

        // Posted
        if (modifiedAPInvHed.Posted != originalAPInvHed.Posted)
        {
            changes.Add("APInvHed.Posted`"
                       + originalAPInvHed.Posted.ToString()
                       + "`"
                       + modifiedAPInvHed.Posted.ToString().Replace("`", "-"));
        }

        // Fiscal Year
        if (modifiedAPInvHed.FiscalYear != originalAPInvHed.FiscalYear)
        {
            changes.Add("APInvHed.FiscalYear`"
                       + originalAPInvHed.FiscalYear.ToString()
                       + "`"
                       + modifiedAPInvHed.FiscalYear.ToString().Replace("`", "-"));
        }

        // Fiscal Period
        if (modifiedAPInvHed.FiscalPeriod != originalAPInvHed.FiscalPeriod)
        {
            changes.Add("APInvHed.FiscalPeriod`"
                       + originalAPInvHed.FiscalPeriod.ToString()
                       + "`"
                       + modifiedAPInvHed.FiscalPeriod.ToString().Replace("`", "-"));
        }

        // Start Up
        if (modifiedAPInvHed.StartUp != originalAPInvHed.StartUp)
        {
            changes.Add("APInvHed.StartUp`"
                       + originalAPInvHed.StartUp.ToString()
                       + "`"
                       + modifiedAPInvHed.StartUp.ToString().Replace("`", "-"));
        }

        // Invoice Reference
        if (modifiedAPInvHed.InvoiceRef != originalAPInvHed.InvoiceRef)
        {
            changes.Add("APInvHed.InvoiceRef`"
                       + originalAPInvHed.InvoiceRef.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceRef.ToString().Replace("`", "-"));
        }

        // Entry Person
        if (modifiedAPInvHed.EntryPerson != originalAPInvHed.EntryPerson)
        {
            changes.Add("APInvHed.EntryPerson`"
                       + originalAPInvHed.EntryPerson.ToString()
                       + "`"
                       + modifiedAPInvHed.EntryPerson.ToString().Replace("`", "-"));
        }

        // Invoice Comment
        if (modifiedAPInvHed.InvoiceComment != originalAPInvHed.InvoiceComment)
        {
            changes.Add("APInvHed.InvoiceComment`"
                       + originalAPInvHed.InvoiceComment.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceComment.ToString().Replace("`", "-"));
        }

        // Invoice Amount
        if (modifiedAPInvHed.InvoiceAmt != originalAPInvHed.InvoiceAmt)
        {
            changes.Add("APInvHed.InvoiceAmt`"
                       + originalAPInvHed.InvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Document Invoice Amount
        if (modifiedAPInvHed.DocInvoiceAmt != originalAPInvHed.DocInvoiceAmt)
        {
            changes.Add("APInvHed.DocInvoiceAmt`"
                       + originalAPInvHed.DocInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Document Invoice Vendor Amount
        if (modifiedAPInvHed.DocInvoiceVendorAmt != originalAPInvHed.DocInvoiceVendorAmt)
        {
            changes.Add("APInvHed.DocInvoiceVendorAmt`"
                       + originalAPInvHed.DocInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Invoice Balance
        if (modifiedAPInvHed.InvoiceBal != originalAPInvHed.InvoiceBal)
        {
            changes.Add("APInvHed.InvoiceBal`"
                       + originalAPInvHed.InvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceBal.ToString().Replace("`", "-"));
        }

        // Document Invoice Balance
        if (modifiedAPInvHed.DocInvoiceBal != originalAPInvHed.DocInvoiceBal)
        {
            changes.Add("APInvHed.DocInvoiceBal`"
                       + originalAPInvHed.DocInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.DocInvoiceBal.ToString().Replace("`", "-"));
        }

        // Unposted Balance
        if (modifiedAPInvHed.UnpostedBal != originalAPInvHed.UnpostedBal)
        {
            changes.Add("APInvHed.UnpostedBal`"
                       + originalAPInvHed.UnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.UnpostedBal.ToString().Replace("`", "-"));
        }

        // Document Unposted Balance
        if (modifiedAPInvHed.DocUnpostedBal != originalAPInvHed.DocUnpostedBal)
        {
            changes.Add("APInvHed.DocUnpostedBal`"
                       + originalAPInvHed.DocUnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.DocUnpostedBal.ToString().Replace("`", "-"));
        }

        // Invoice Held
        if (modifiedAPInvHed.InvoiceHeld != originalAPInvHed.InvoiceHeld)
        {
            changes.Add("APInvHed.InvoiceHeld`"
                       + originalAPInvHed.InvoiceHeld.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceHeld.ToString().Replace("`", "-"));
        }

        // Pay Hold
        if (modifiedAPInvHed.PayHold != originalAPInvHed.PayHold)
        {
            changes.Add("APInvHed.PayHold`"
                       + originalAPInvHed.PayHold.ToString()
                       + "`"
                       + modifiedAPInvHed.PayHold.ToString().Replace("`", "-"));
        }

        // Description
        if (modifiedAPInvHed.Description != originalAPInvHed.Description)
        {
            changes.Add("APInvHed.Description`"
                       + originalAPInvHed.Description.ToString()
                       + "`"
                       + modifiedAPInvHed.Description.ToString().Replace("`", "-"));
        }

        // Currency Code
        if (modifiedAPInvHed.CurrencyCode != originalAPInvHed.CurrencyCode)
        {
            changes.Add("APInvHed.CurrencyCode`"
                       + originalAPInvHed.CurrencyCode.ToString()
                       + "`"
                       + modifiedAPInvHed.CurrencyCode.ToString().Replace("`", "-"));
        }

        // Exchange Rate
        if (modifiedAPInvHed.ExchangeRate != originalAPInvHed.ExchangeRate)
        {
            changes.Add("APInvHed.ExchangeRate`"
                       + originalAPInvHed.ExchangeRate.ToString()
                       + "`"
                       + modifiedAPInvHed.ExchangeRate.ToString().Replace("`", "-"));
        }

        // Lock Rate
        if (modifiedAPInvHed.LockRate != originalAPInvHed.LockRate)
        {
            changes.Add("APInvHed.LockRate`"
                       + originalAPInvHed.LockRate.ToString()
                       + "`"
                       + modifiedAPInvHed.LockRate.ToString().Replace("`", "-"));
        }

        // REF PO Number
        if (modifiedAPInvHed.REFPONum != originalAPInvHed.REFPONum)
        {
            changes.Add("APInvHed.REFPONum`"
                       + originalAPInvHed.REFPONum.ToString()
                       + "`"
                       + modifiedAPInvHed.REFPONum.ToString().Replace("`", "-"));
        }

        // Tax Region Code
        if (modifiedAPInvHed.TaxRegionCode != originalAPInvHed.TaxRegionCode)
        {
            changes.Add("APInvHed.TaxRegionCode`"
                       + originalAPInvHed.TaxRegionCode.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxRegionCode.ToString().Replace("`", "-"));
        }

        // Journal Number
        if (modifiedAPInvHed.JournalNum != originalAPInvHed.JournalNum)
        {
            changes.Add("APInvHed.JournalNum`"
                       + originalAPInvHed.JournalNum.ToString()
                       + "`"
                       + modifiedAPInvHed.JournalNum.ToString().Replace("`", "-"));
        }

        // Journal Code
        if (modifiedAPInvHed.JournalCode != originalAPInvHed.JournalCode)
        {
            changes.Add("APInvHed.JournalCode`"
                       + originalAPInvHed.JournalCode.ToString()
                       + "`"
                       + modifiedAPInvHed.JournalCode.ToString().Replace("`", "-"));
        }

        // Update Tax
        if (modifiedAPInvHed.UpdateTax != originalAPInvHed.UpdateTax)
        {
            changes.Add("APInvHed.UpdateTax`"
                       + originalAPInvHed.UpdateTax.ToString()
                       + "`"
                       + modifiedAPInvHed.UpdateTax.ToString().Replace("`", "-"));
        }

        // Invoice Vendor Amount
        if (modifiedAPInvHed.InvoiceVendorAmt != originalAPInvHed.InvoiceVendorAmt)
        {
            changes.Add("APInvHed.InvoiceVendorAmt`"
                       + originalAPInvHed.InvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Legal Number
        if (modifiedAPInvHed.LegalNumber != originalAPInvHed.LegalNumber)
        {
            changes.Add("APInvHed.LegalNumber`"
                       + originalAPInvHed.LegalNumber.ToString()
                       + "`"
                       + modifiedAPInvHed.LegalNumber.ToString().Replace("`", "-"));
        }

        // External ID
        if (modifiedAPInvHed.ExternalID != originalAPInvHed.ExternalID)
        {
            changes.Add("APInvHed.ExternalID`"
                       + originalAPInvHed.ExternalID.ToString()
                       + "`"
                       + modifiedAPInvHed.ExternalID.ToString().Replace("`", "-"));
        }

        // Fixed Amount
        if (modifiedAPInvHed.FixedAmt != originalAPInvHed.FixedAmt)
        {
            changes.Add("APInvHed.FixedAmt`"
                       + originalAPInvHed.FixedAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.FixedAmt.ToString().Replace("`", "-"));
        }

        // Cross Reference Invoice Number
        if (modifiedAPInvHed.XRefInvoiceNum != originalAPInvHed.XRefInvoiceNum)
        {
            changes.Add("APInvHed.XRefInvoiceNum`"
                       + originalAPInvHed.XRefInvoiceNum.ToString()
                       + "`"
                       + modifiedAPInvHed.XRefInvoiceNum.ToString().Replace("`", "-"));
        }

        // Global Company
        if (modifiedAPInvHed.GlbCompany != originalAPInvHed.GlbCompany)
        {
            changes.Add("APInvHed.GlbCompany`"
                       + originalAPInvHed.GlbCompany.ToString()
                       + "`"
                       + modifiedAPInvHed.GlbCompany.ToString().Replace("`", "-"));
        }

        // Global Vendor Number
        if (modifiedAPInvHed.GlbVendorNum != originalAPInvHed.GlbVendorNum)
        {
            changes.Add("APInvHed.GlbVendorNum`"
                       + originalAPInvHed.GlbVendorNum.ToString()
                       + "`"
                       + modifiedAPInvHed.GlbVendorNum.ToString().Replace("`", "-"));
        }

        // Global Invoice Number
        if (modifiedAPInvHed.GlbInvoiceNum != originalAPInvHed.GlbInvoiceNum)
        {
            changes.Add("APInvHed.GlbInvoiceNum`"
                       + originalAPInvHed.GlbInvoiceNum.ToString()
                       + "`"
                       + modifiedAPInvHed.GlbInvoiceNum.ToString().Replace("`", "-"));
        }

        // Deposit Gain Loss
        if (modifiedAPInvHed.DepGainLoss != originalAPInvHed.DepGainLoss)
        {
            changes.Add("APInvHed.DepGainLoss`"
                       + originalAPInvHed.DepGainLoss.ToString()
                       + "`"
                       + modifiedAPInvHed.DepGainLoss.ToString().Replace("`", "-"));
        }

        // CPay
        if (modifiedAPInvHed.CPay != originalAPInvHed.CPay)
        {
            changes.Add("APInvHed.CPay`"
                       + originalAPInvHed.CPay.ToString()
                       + "`"
                       + modifiedAPInvHed.CPay.ToString().Replace("`", "-"));
        }

        // CPay Linked
        if (modifiedAPInvHed.CPayLinked != originalAPInvHed.CPayLinked)
        {
            changes.Add("APInvHed.CPayLinked`"
                       + originalAPInvHed.CPayLinked.ToString()
                       + "`"
                       + modifiedAPInvHed.CPayLinked.ToString().Replace("`", "-"));
        }

        // CPay Legal Number
        if (modifiedAPInvHed.CPayLegalNumber != originalAPInvHed.CPayLegalNumber)
        {
            changes.Add("APInvHed.CPayLegalNumber`"
                       + originalAPInvHed.CPayLegalNumber.ToString()
                       + "`"
                       + modifiedAPInvHed.CPayLegalNumber.ToString().Replace("`", "-"));
        }

        // CPay Check Number
        if (modifiedAPInvHed.CPayCheckNum != originalAPInvHed.CPayCheckNum)
        {
            changes.Add("APInvHed.CPayCheckNum`"
                       + originalAPInvHed.CPayCheckNum.ToString()
                       + "`"
                       + modifiedAPInvHed.CPayCheckNum.ToString().Replace("`", "-"));
        }

        // CPay Check Date
        if (modifiedAPInvHed.CPayCheckDate != originalAPInvHed.CPayCheckDate)
        {
            changes.Add("APInvHed.CPayCheckDate`"
                       + originalAPInvHed.CPayCheckDate.ToString()
                       + "`"
                       + modifiedAPInvHed.CPayCheckDate.ToString().Replace("`", "-"));
        }

        // CPay Invoice Balance
        if (modifiedAPInvHed.CPayInvoiceBal != originalAPInvHed.CPayInvoiceBal)
        {
            changes.Add("APInvHed.CPayInvoiceBal`"
                       + originalAPInvHed.CPayInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.CPayInvoiceBal.ToString().Replace("`", "-"));
        }

        // CPay Document Invoice Balance
        if (modifiedAPInvHed.CPayDocInvoiceBal != originalAPInvHed.CPayDocInvoiceBal)
        {
            changes.Add("APInvHed.CPayDocInvoiceBal`"
                       + originalAPInvHed.CPayDocInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.CPayDocInvoiceBal.ToString().Replace("`", "-"));
        }

        // Rounding
        if (modifiedAPInvHed.Rounding != originalAPInvHed.Rounding)
        {
            changes.Add("APInvHed.Rounding`"
                       + originalAPInvHed.Rounding.ToString()
                       + "`"
                       + modifiedAPInvHed.Rounding.ToString().Replace("`", "-"));
        }

        // GL Control Type
        if (modifiedAPInvHed.GLControlType != originalAPInvHed.GLControlType)
        {
            changes.Add("APInvHed.GLControlType`"
                       + originalAPInvHed.GLControlType.ToString()
                       + "`"
                       + modifiedAPInvHed.GLControlType.ToString().Replace("`", "-"));
        }

        // Document Rounding
        if (modifiedAPInvHed.DocRounding != originalAPInvHed.DocRounding)
        {
            changes.Add("APInvHed.DocRounding`"
                       + originalAPInvHed.DocRounding.ToString()
                       + "`"
                       + modifiedAPInvHed.DocRounding.ToString().Replace("`", "-"));
        }

        // GL Control Code
        if (modifiedAPInvHed.GLControlCode != originalAPInvHed.GLControlCode)
        {
            changes.Add("APInvHed.GLControlCode`"
                       + originalAPInvHed.GLControlCode.ToString()
                       + "`"
                       + modifiedAPInvHed.GLControlCode.ToString().Replace("`", "-"));
        }

        // Rate Group Code
        if (modifiedAPInvHed.RateGrpCode != originalAPInvHed.RateGrpCode)
        {
            changes.Add("APInvHed.RateGrpCode`"
                       + originalAPInvHed.RateGrpCode.ToString()
                       + "`"
                       + modifiedAPInvHed.RateGrpCode.ToString().Replace("`", "-"));
        }

        // Apply Date
        if (modifiedAPInvHed.ApplyDate != originalAPInvHed.ApplyDate)
        {
            changes.Add("APInvHed.ApplyDate`"
                       + originalAPInvHed.ApplyDate.ToString()
                       + "`"
                       + modifiedAPInvHed.ApplyDate.ToString().Replace("`", "-"));
        }

        // Fiscal Year Suffix
        if (modifiedAPInvHed.FiscalYearSuffix != originalAPInvHed.FiscalYearSuffix)
        {
            changes.Add("APInvHed.FiscalYearSuffix`"
                       + originalAPInvHed.FiscalYearSuffix.ToString()
                       + "`"
                       + modifiedAPInvHed.FiscalYearSuffix.ToString().Replace("`", "-"));
        }

        // Fiscal Calendar ID
        if (modifiedAPInvHed.FiscalCalendarID != originalAPInvHed.FiscalCalendarID)
        {
            changes.Add("APInvHed.FiscalCalendarID`"
                       + originalAPInvHed.FiscalCalendarID.ToString()
                       + "`"
                       + modifiedAPInvHed.FiscalCalendarID.ToString().Replace("`", "-"));
        }

        // Tax Point
        if (modifiedAPInvHed.TaxPoint != originalAPInvHed.TaxPoint)
        {
            changes.Add("APInvHed.TaxPoint`"
                       + originalAPInvHed.TaxPoint.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxPoint.ToString().Replace("`", "-"));
        }

        // Tax Rate Date
        if (modifiedAPInvHed.TaxRateDate != originalAPInvHed.TaxRateDate)
        {
            changes.Add("APInvHed.TaxRateDate`"
                       + originalAPInvHed.TaxRateDate.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxRateDate.ToString().Replace("`", "-"));
        }

        // Ready To Calc
        if (modifiedAPInvHed.ReadyToCalc != originalAPInvHed.ReadyToCalc)
        {
            changes.Add("APInvHed.ReadyToCalc`"
                       + originalAPInvHed.ReadyToCalc.ToString()
                       + "`"
                       + modifiedAPInvHed.ReadyToCalc.ToString().Replace("`", "-"));
        }

        // Recalc Before Post
        if (modifiedAPInvHed.RecalcBeforePost != originalAPInvHed.RecalcBeforePost)
        {
            changes.Add("APInvHed.RecalcBeforePost`"
                       + originalAPInvHed.RecalcBeforePost.ToString()
                       + "`"
                       + modifiedAPInvHed.RecalcBeforePost.ToString().Replace("`", "-"));
        }

        // Get Default Tax IDs
        if (modifiedAPInvHed.GetDfltTaxIds != originalAPInvHed.GetDfltTaxIds)
        {
            changes.Add("APInvHed.GetDfltTaxIds`"
                       + originalAPInvHed.GetDfltTaxIds.ToString()
                       + "`"
                       + modifiedAPInvHed.GetDfltTaxIds.ToString().Replace("`", "-"));
        }

        // PMUID
        if (modifiedAPInvHed.PMUID != originalAPInvHed.PMUID)
        {
            changes.Add("APInvHed.PMUID`"
                       + originalAPInvHed.PMUID.ToString()
                       + "`"
                       + modifiedAPInvHed.PMUID.ToString().Replace("`", "-"));
        }

        // Pay Discount Days
        if (modifiedAPInvHed.PayDiscDays != originalAPInvHed.PayDiscDays)
        {
            changes.Add("APInvHed.PayDiscDays`"
                       + originalAPInvHed.PayDiscDays.ToString()
                       + "`"
                       + modifiedAPInvHed.PayDiscDays.ToString().Replace("`", "-"));
        }

        // Pay Discount Percentage
        if (modifiedAPInvHed.PayDiscPer != originalAPInvHed.PayDiscPer)
        {
            changes.Add("APInvHed.PayDiscPer`"
                       + originalAPInvHed.PayDiscPer.ToString()
                       + "`"
                       + modifiedAPInvHed.PayDiscPer.ToString().Replace("`", "-"));
        }

        // Withhold Amount
        if (modifiedAPInvHed.WithholdAmt != originalAPInvHed.WithholdAmt)
        {
            changes.Add("APInvHed.WithholdAmt`"
                       + originalAPInvHed.WithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.WithholdAmt.ToString().Replace("`", "-"));
        }

        // Document Withhold Amount
        if (modifiedAPInvHed.DocWithholdAmt != originalAPInvHed.DocWithholdAmt)
        {
            changes.Add("APInvHed.DocWithholdAmt`"
                       + originalAPInvHed.DocWithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocWithholdAmt.ToString().Replace("`", "-"));
        }

        // Pay Discount Partial Pay
        if (modifiedAPInvHed.PayDiscPartPay != originalAPInvHed.PayDiscPartPay)
        {
            changes.Add("APInvHed.PayDiscPartPay`"
                       + originalAPInvHed.PayDiscPartPay.ToString()
                       + "`"
                       + modifiedAPInvHed.PayDiscPartPay.ToString().Replace("`", "-"));
        }

        // PI Payment
        if (modifiedAPInvHed.PIPayment != originalAPInvHed.PIPayment)
        {
            changes.Add("APInvHed.PIPayment`"
                       + originalAPInvHed.PIPayment.ToString()
                       + "`"
                       + modifiedAPInvHed.PIPayment.ToString().Replace("`", "-"));
        }

        // Correction Invoice
        if (modifiedAPInvHed.CorrectionInv != originalAPInvHed.CorrectionInv)
        {
            changes.Add("APInvHed.CorrectionInv`"
                       + originalAPInvHed.CorrectionInv.ToString()
                       + "`"
                       + modifiedAPInvHed.CorrectionInv.ToString().Replace("`", "-"));
        }

        // Tax Rate Group Code
        if (modifiedAPInvHed.TaxRateGrpCode != originalAPInvHed.TaxRateGrpCode)
        {
            changes.Add("APInvHed.TaxRateGrpCode`"
                       + originalAPInvHed.TaxRateGrpCode.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxRateGrpCode.ToString().Replace("`", "-"));
        }

        // Lock Tax Rate
        if (modifiedAPInvHed.LockTaxRate != originalAPInvHed.LockTaxRate)
        {
            changes.Add("APInvHed.LockTaxRate`"
                       + originalAPInvHed.LockTaxRate.ToString()
                       + "`"
                       + modifiedAPInvHed.LockTaxRate.ToString().Replace("`", "-"));
        }

        // Plant
        if (modifiedAPInvHed.Plant != originalAPInvHed.Plant)
        {
            changes.Add("APInvHed.Plant`"
                       + originalAPInvHed.Plant.ToString()
                       + "`"
                       + modifiedAPInvHed.Plant.ToString().Replace("`", "-"));
        }

        // Rpt1 Invoice Amount
        if (modifiedAPInvHed.Rpt1InvoiceAmt != originalAPInvHed.Rpt1InvoiceAmt)
        {
            changes.Add("APInvHed.Rpt1InvoiceAmt`"
                       + originalAPInvHed.Rpt1InvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Invoice Amount
        if (modifiedAPInvHed.Rpt2InvoiceAmt != originalAPInvHed.Rpt2InvoiceAmt)
        {
            changes.Add("APInvHed.Rpt2InvoiceAmt`"
                       + originalAPInvHed.Rpt2InvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Invoice Amount
        if (modifiedAPInvHed.Rpt3InvoiceAmt != originalAPInvHed.Rpt3InvoiceAmt)
        {
            changes.Add("APInvHed.Rpt3InvoiceAmt`"
                       + originalAPInvHed.Rpt3InvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt1InvoiceVendorAmt != originalAPInvHed.Rpt1InvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt1InvoiceVendorAmt`"
                       + originalAPInvHed.Rpt1InvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1InvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt2InvoiceVendorAmt != originalAPInvHed.Rpt2InvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt2InvoiceVendorAmt`"
                       + originalAPInvHed.Rpt2InvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2InvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt3InvoiceVendorAmt != originalAPInvHed.Rpt3InvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt3InvoiceVendorAmt`"
                       + originalAPInvHed.Rpt3InvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3InvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Invoice Balance
        if (modifiedAPInvHed.Rpt1InvoiceBal != originalAPInvHed.Rpt1InvoiceBal)
        {
            changes.Add("APInvHed.Rpt1InvoiceBal`"
                       + originalAPInvHed.Rpt1InvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1InvoiceBal.ToString().Replace("`", "-"));
        }

        // Rpt2 Invoice Balance
        if (modifiedAPInvHed.Rpt2InvoiceBal != originalAPInvHed.Rpt2InvoiceBal)
        {
            changes.Add("APInvHed.Rpt2InvoiceBal`"
                       + originalAPInvHed.Rpt2InvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2InvoiceBal.ToString().Replace("`", "-"));
        }

        // Rpt3 Invoice Balance
        if (modifiedAPInvHed.Rpt3InvoiceBal != originalAPInvHed.Rpt3InvoiceBal)
        {
            changes.Add("APInvHed.Rpt3InvoiceBal`"
                       + originalAPInvHed.Rpt3InvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3InvoiceBal.ToString().Replace("`", "-"));
        }

        // Rpt1 Discount Amount
        if (modifiedAPInvHed.Rpt1DiscountAmt != originalAPInvHed.Rpt1DiscountAmt)
        {
            changes.Add("APInvHed.Rpt1DiscountAmt`"
                       + originalAPInvHed.Rpt1DiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1DiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Discount Amount
        if (modifiedAPInvHed.Rpt2DiscountAmt != originalAPInvHed.Rpt2DiscountAmt)
        {
            changes.Add("APInvHed.Rpt2DiscountAmt`"
                       + originalAPInvHed.Rpt2DiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2DiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Discount Amount
        if (modifiedAPInvHed.Rpt3DiscountAmt != originalAPInvHed.Rpt3DiscountAmt)
        {
            changes.Add("APInvHed.Rpt3DiscountAmt`"
                       + originalAPInvHed.Rpt3DiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3DiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Tax Amount
        if (modifiedAPInvHed.Rpt1TaxAmt != originalAPInvHed.Rpt1TaxAmt)
        {
            changes.Add("APInvHed.Rpt1TaxAmt`"
                       + originalAPInvHed.Rpt1TaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1TaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Tax Amount
        if (modifiedAPInvHed.Rpt2TaxAmt != originalAPInvHed.Rpt2TaxAmt)
        {
            changes.Add("APInvHed.Rpt2TaxAmt`"
                       + originalAPInvHed.Rpt2TaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2TaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Tax Amount
        if (modifiedAPInvHed.Rpt3TaxAmt != originalAPInvHed.Rpt3TaxAmt)
        {
            changes.Add("APInvHed.Rpt3TaxAmt`"
                       + originalAPInvHed.Rpt3TaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3TaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Unposted Balance
        if (modifiedAPInvHed.Rpt1UnpostedBal != originalAPInvHed.Rpt1UnpostedBal)
        {
            changes.Add("APInvHed.Rpt1UnpostedBal`"
                       + originalAPInvHed.Rpt1UnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1UnpostedBal.ToString().Replace("`", "-"));
        }

        // Rpt2 Unposted Balance
        if (modifiedAPInvHed.Rpt2UnpostedBal != originalAPInvHed.Rpt2UnpostedBal)
        {
            changes.Add("APInvHed.Rpt2UnpostedBal`"
                       + originalAPInvHed.Rpt2UnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2UnpostedBal.ToString().Replace("`", "-"));
        }

        // Rpt3 Unposted Balance
        if (modifiedAPInvHed.Rpt3UnpostedBal != originalAPInvHed.Rpt3UnpostedBal)
        {
            changes.Add("APInvHed.Rpt3UnpostedBal`"
                       + originalAPInvHed.Rpt3UnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3UnpostedBal.ToString().Replace("`", "-"));
        }

        // Rpt1 CPay Invoice Balance
        if (modifiedAPInvHed.Rpt1CPayInvoiceBal != originalAPInvHed.Rpt1CPayInvoiceBal)
        {
            changes.Add("APInvHed.Rpt1CPayInvoiceBal`"
                       + originalAPInvHed.Rpt1CPayInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1CPayInvoiceBal.ToString().Replace("`", "-"));
        }

        // Rpt2 CPay Invoice Balance
        if (modifiedAPInvHed.Rpt2CPayInvoiceBal != originalAPInvHed.Rpt2CPayInvoiceBal)
        {
            changes.Add("APInvHed.Rpt2CPayInvoiceBal`"
                       + originalAPInvHed.Rpt2CPayInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2CPayInvoiceBal.ToString().Replace("`", "-"));
        }

        // Rpt3 CPay Invoice Balance
        if (modifiedAPInvHed.Rpt3CPayInvoiceBal != originalAPInvHed.Rpt3CPayInvoiceBal)
        {
            changes.Add("APInvHed.Rpt3CPayInvoiceBal`"
                       + originalAPInvHed.Rpt3CPayInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3CPayInvoiceBal.ToString().Replace("`", "-"));
        }

        // Allow Override LI
        if (modifiedAPInvHed.AllowOverrideLI != originalAPInvHed.AllowOverrideLI)
        {
            changes.Add("APInvHed.AllowOverrideLI`"
                       + originalAPInvHed.AllowOverrideLI.ToString()
                       + "`"
                       + modifiedAPInvHed.AllowOverrideLI.ToString().Replace("`", "-"));
        }

        // Matched From LI
        if (modifiedAPInvHed.MatchedFromLI != originalAPInvHed.MatchedFromLI)
        {
            changes.Add("APInvHed.MatchedFromLI`"
                       + originalAPInvHed.MatchedFromLI.ToString()
                       + "`"
                       + modifiedAPInvHed.MatchedFromLI.ToString().Replace("`", "-"));
        }

        // Rpt1 Withhold Amount
        if (modifiedAPInvHed.Rpt1WithholdAmt != originalAPInvHed.Rpt1WithholdAmt)
        {
            changes.Add("APInvHed.Rpt1WithholdAmt`"
                       + originalAPInvHed.Rpt1WithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1WithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Withhold Amount
        if (modifiedAPInvHed.Rpt2WithholdAmt != originalAPInvHed.Rpt2WithholdAmt)
        {
            changes.Add("APInvHed.Rpt2WithholdAmt`"
                       + originalAPInvHed.Rpt2WithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2WithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Withhold Amount
        if (modifiedAPInvHed.Rpt3WithholdAmt != originalAPInvHed.Rpt3WithholdAmt)
        {
            changes.Add("APInvHed.Rpt3WithholdAmt`"
                       + originalAPInvHed.Rpt3WithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3WithholdAmt.ToString().Replace("`", "-"));
        }

        // SE Bank Reference
        if (modifiedAPInvHed.SEBankRef != originalAPInvHed.SEBankRef)
        {
            changes.Add("APInvHed.SEBankRef`"
                       + originalAPInvHed.SEBankRef.ToString()
                       + "`"
                       + modifiedAPInvHed.SEBankRef.ToString().Replace("`", "-"));
        }

        // SE Pay Code
        if (modifiedAPInvHed.SEPayCode != originalAPInvHed.SEPayCode)
        {
            changes.Add("APInvHed.SEPayCode`"
                       + originalAPInvHed.SEPayCode.ToString()
                       + "`"
                       + modifiedAPInvHed.SEPayCode.ToString().Replace("`", "-"));
        }

        // GUI Format Code
        if (modifiedAPInvHed.GUIFormatCode != originalAPInvHed.GUIFormatCode)
        {
            changes.Add("APInvHed.GUIFormatCode`"
                       + originalAPInvHed.GUIFormatCode.ToString()
                       + "`"
                       + modifiedAPInvHed.GUIFormatCode.ToString().Replace("`", "-"));
        }

        // GUI Tax Type Code
        if (modifiedAPInvHed.GUITaxTypeCode != originalAPInvHed.GUITaxTypeCode)
        {
            changes.Add("APInvHed.GUITaxTypeCode`"
                       + originalAPInvHed.GUITaxTypeCode.ToString()
                       + "`"
                       + modifiedAPInvHed.GUITaxTypeCode.ToString().Replace("`", "-"));
        }

        // GUI Deduct Code
        if (modifiedAPInvHed.GUIDeductCode != originalAPInvHed.GUIDeductCode)
        {
            changes.Add("APInvHed.GUIDeductCode`"
                       + originalAPInvHed.GUIDeductCode.ToString()
                       + "`"
                       + modifiedAPInvHed.GUIDeductCode.ToString().Replace("`", "-"));
        }

        // Pre Payment
        if (modifiedAPInvHed.PrePayment != originalAPInvHed.PrePayment)
        {
            changes.Add("APInvHed.PrePayment`"
                       + originalAPInvHed.PrePayment.ToString()
                       + "`"
                       + modifiedAPInvHed.PrePayment.ToString().Replace("`", "-"));
        }

        // AP LOC ID
        if (modifiedAPInvHed.APLOCID != originalAPInvHed.APLOCID)
        {
            changes.Add("APInvHed.APLOCID`"
                       + originalAPInvHed.APLOCID.ToString()
                       + "`"
                       + modifiedAPInvHed.APLOCID.ToString().Replace("`", "-"));
        }

        // GUI Import Tax Basis
        if (modifiedAPInvHed.GUIImportTaxBasis != originalAPInvHed.GUIImportTaxBasis)
        {
            changes.Add("APInvHed.GUIImportTaxBasis`"
                       + originalAPInvHed.GUIImportTaxBasis.ToString()
                       + "`"
                       + modifiedAPInvHed.GUIImportTaxBasis.ToString().Replace("`", "-"));
        }

        // Document GUI Import Tax Basis
        if (modifiedAPInvHed.DocGUIImportTaxBasis != originalAPInvHed.DocGUIImportTaxBasis)
        {
            changes.Add("APInvHed.DocGUIImportTaxBasis`"
                       + originalAPInvHed.DocGUIImportTaxBasis.ToString()
                       + "`"
                       + modifiedAPInvHed.DocGUIImportTaxBasis.ToString().Replace("`", "-"));
        }

        // Rpt1 GUI Import Tax Basis
        if (modifiedAPInvHed.Rpt1GUIImportTaxBasis != originalAPInvHed.Rpt1GUIImportTaxBasis)
        {
            changes.Add("APInvHed.Rpt1GUIImportTaxBasis`"
                       + originalAPInvHed.Rpt1GUIImportTaxBasis.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1GUIImportTaxBasis.ToString().Replace("`", "-"));
        }

        // Rpt2 GUI Import Tax Basis
        if (modifiedAPInvHed.Rpt2GUIImportTaxBasis != originalAPInvHed.Rpt2GUIImportTaxBasis)
        {
            changes.Add("APInvHed.Rpt2GUIImportTaxBasis`"
                       + originalAPInvHed.Rpt2GUIImportTaxBasis.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2GUIImportTaxBasis.ToString().Replace("`", "-"));
        }

        // Rpt3 GUI Import Tax Basis
        if (modifiedAPInvHed.Rpt3GUIImportTaxBasis != originalAPInvHed.Rpt3GUIImportTaxBasis)
        {
            changes.Add("APInvHed.Rpt3GUIImportTaxBasis`"
                       + originalAPInvHed.Rpt3GUIImportTaxBasis.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3GUIImportTaxBasis.ToString().Replace("`", "-"));
        }

        // Override Default Tax Date
        if (modifiedAPInvHed.OvrDefTaxDate != originalAPInvHed.OvrDefTaxDate)
        {
            changes.Add("APInvHed.OvrDefTaxDate`"
                       + originalAPInvHed.OvrDefTaxDate.ToString()
                       + "`"
                       + modifiedAPInvHed.OvrDefTaxDate.ToString().Replace("`", "-"));
        }

        // Linked
        if (modifiedAPInvHed.Linked != originalAPInvHed.Linked)
        {
            changes.Add("APInvHed.Linked`"
                       + originalAPInvHed.Linked.ToString()
                       + "`"
                       + modifiedAPInvHed.Linked.ToString().Replace("`", "-"));
        }

        // Claim Reference
        if (modifiedAPInvHed.ClaimRef != originalAPInvHed.ClaimRef)
        {
            changes.Add("APInvHed.ClaimRef`"
                       + originalAPInvHed.ClaimRef.ToString()
                       + "`"
                       + modifiedAPInvHed.ClaimRef.ToString().Replace("`", "-"));
        }

        // Employee ID
        if (modifiedAPInvHed.EmpID != originalAPInvHed.EmpID)
        {
            changes.Add("APInvHed.EmpID`"
                       + originalAPInvHed.EmpID.ToString()
                       + "`"
                       + modifiedAPInvHed.EmpID.ToString().Replace("`", "-"));
        }

        // In Bank File
        if (modifiedAPInvHed.InBankFile != originalAPInvHed.InBankFile)
        {
            changes.Add("APInvHed.InBankFile`"
                       + originalAPInvHed.InBankFile.ToString()
                       + "`"
                       + modifiedAPInvHed.InBankFile.ToString().Replace("`", "-"));
        }

        // CN Confirm Date
        if (modifiedAPInvHed.CNConfirmDate != originalAPInvHed.CNConfirmDate)
        {
            changes.Add("APInvHed.CNConfirmDate`"
                       + originalAPInvHed.CNConfirmDate.ToString()
                       + "`"
                       + modifiedAPInvHed.CNConfirmDate.ToString().Replace("`", "-"));
        }

        // Bank ID
        if (modifiedAPInvHed.BankID != originalAPInvHed.BankID)
        {
            changes.Add("APInvHed.BankID`"
                       + originalAPInvHed.BankID.ToString()
                       + "`"
                       + modifiedAPInvHed.BankID.ToString().Replace("`", "-"));
        }

        // Self Transaction Document Type ID
        if (modifiedAPInvHed.SelfTranDocTypeID != originalAPInvHed.SelfTranDocTypeID)
        {
            changes.Add("APInvHed.SelfTranDocTypeID`"
                       + originalAPInvHed.SelfTranDocTypeID.ToString()
                       + "`"
                       + modifiedAPInvHed.SelfTranDocTypeID.ToString().Replace("`", "-"));
        }

        // Main Site
        if (modifiedAPInvHed.MainSite != originalAPInvHed.MainSite)
        {
            changes.Add("APInvHed.MainSite`"
                       + originalAPInvHed.MainSite.ToString()
                       + "`"
                       + modifiedAPInvHed.MainSite.ToString().Replace("`", "-"));
        }

        // Card Code
        if (modifiedAPInvHed.CardCode != originalAPInvHed.CardCode)
        {
            changes.Add("APInvHed.CardCode`"
                       + originalAPInvHed.CardCode.ToString()
                       + "`"
                       + modifiedAPInvHed.CardCode.ToString().Replace("`", "-"));
        }

        // Site Code
        if (modifiedAPInvHed.SiteCode != originalAPInvHed.SiteCode)
        {
            changes.Add("APInvHed.SiteCode`"
                       + originalAPInvHed.SiteCode.ToString()
                       + "`"
                       + modifiedAPInvHed.SiteCode.ToString().Replace("`", "-"));
        }

        // Bank Giro Account Number
        if (modifiedAPInvHed.BankGiroAcctNbr != originalAPInvHed.BankGiroAcctNbr)
        {
            changes.Add("APInvHed.BankGiroAcctNbr`"
                       + originalAPInvHed.BankGiroAcctNbr.ToString()
                       + "`"
                       + modifiedAPInvHed.BankGiroAcctNbr.ToString().Replace("`", "-"));
        }

        // Branch ID
        if (modifiedAPInvHed.BranchID != originalAPInvHed.BranchID)
        {
            changes.Add("APInvHed.BranchID`"
                       + originalAPInvHed.BranchID.ToString()
                       + "`"
                       + modifiedAPInvHed.BranchID.ToString().Replace("`", "-"));
        }

        // Supplier Agent Name
        if (modifiedAPInvHed.SupAgentName != originalAPInvHed.SupAgentName)
        {
            changes.Add("APInvHed.SupAgentName`"
                       + originalAPInvHed.SupAgentName.ToString()
                       + "`"
                       + modifiedAPInvHed.SupAgentName.ToString().Replace("`", "-"));
        }

        // Supplier Agent Tax Registration Number
        if (modifiedAPInvHed.SupAgentTaxRegNo != originalAPInvHed.SupAgentTaxRegNo)
        {
            changes.Add("APInvHed.SupAgentTaxRegNo`"
                       + originalAPInvHed.SupAgentTaxRegNo.ToString()
                       + "`"
                       + modifiedAPInvHed.SupAgentTaxRegNo.ToString().Replace("`", "-"));
        }

        // Non Deduct Code
        if (modifiedAPInvHed.NonDeductCode != originalAPInvHed.NonDeductCode)
        {
            changes.Add("APInvHed.NonDeductCode`"
                       + originalAPInvHed.NonDeductCode.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductCode.ToString().Replace("`", "-"));
        }

        // Asset Type Code
        if (modifiedAPInvHed.AssetTypeCode != originalAPInvHed.AssetTypeCode)
        {
            changes.Add("APInvHed.AssetTypeCode`"
                       + originalAPInvHed.AssetTypeCode.ToString()
                       + "`"
                       + modifiedAPInvHed.AssetTypeCode.ToString().Replace("`", "-"));
        }

        // Cash
        if (modifiedAPInvHed.Cash != originalAPInvHed.Cash)
        {
            changes.Add("APInvHed.Cash`"
                       + originalAPInvHed.Cash.ToString()
                       + "`"
                       + modifiedAPInvHed.Cash.ToString().Replace("`", "-"));
        }

        // Credit Card
        if (modifiedAPInvHed.CreditCard != originalAPInvHed.CreditCard)
        {
            changes.Add("APInvHed.CreditCard`"
                       + originalAPInvHed.CreditCard.ToString()
                       + "`"
                       + modifiedAPInvHed.CreditCard.ToString().Replace("`", "-"));
        }

        // Normal
        if (modifiedAPInvHed.Normal != originalAPInvHed.Normal)
        {
            changes.Add("APInvHed.Normal`"
                       + originalAPInvHed.Normal.ToString()
                       + "`"
                       + modifiedAPInvHed.Normal.ToString().Replace("`", "-"));
        }

        // Card ID
        if (modifiedAPInvHed.CardID != originalAPInvHed.CardID)
        {
            changes.Add("APInvHed.CardID`"
                       + originalAPInvHed.CardID.ToString()
                       + "`"
                       + modifiedAPInvHed.CardID.ToString().Replace("`", "-"));
        }

        // Card Holder Tax ID
        if (modifiedAPInvHed.CardHolderTaxID != originalAPInvHed.CardHolderTaxID)
        {
            changes.Add("APInvHed.CardHolderTaxID`"
                       + originalAPInvHed.CardHolderTaxID.ToString()
                       + "`"
                       + modifiedAPInvHed.CardHolderTaxID.ToString().Replace("`", "-"));
        }

        // Card Member Name
        if (modifiedAPInvHed.CardMemberName != originalAPInvHed.CardMemberName)
        {
            changes.Add("APInvHed.CardMemberName`"
                       + originalAPInvHed.CardMemberName.ToString()
                       + "`"
                       + modifiedAPInvHed.CardMemberName.ToString().Replace("`", "-"));
        }

        // Excluded
        if (modifiedAPInvHed.Excluded != originalAPInvHed.Excluded)
        {
            changes.Add("APInvHed.Excluded`"
                       + originalAPInvHed.Excluded.ToString()
                       + "`"
                       + modifiedAPInvHed.Excluded.ToString().Replace("`", "-"));
        }

        // Deferred
        if (modifiedAPInvHed.Deferred != originalAPInvHed.Deferred)
        {
            changes.Add("APInvHed.Deferred`"
                       + originalAPInvHed.Deferred.ToString()
                       + "`"
                       + modifiedAPInvHed.Deferred.ToString().Replace("`", "-"));
        }

        // Non Deduct Amount
        if (modifiedAPInvHed.NonDeductAmt != originalAPInvHed.NonDeductAmt)
        {
            changes.Add("APInvHed.NonDeductAmt`"
                       + originalAPInvHed.NonDeductAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductAmt.ToString().Replace("`", "-"));
        }

        // Non Deduct Document Amount
        if (modifiedAPInvHed.NonDeductDocAmt != originalAPInvHed.NonDeductDocAmt)
        {
            changes.Add("APInvHed.NonDeductDocAmt`"
                       + originalAPInvHed.NonDeductDocAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductDocAmt.ToString().Replace("`", "-"));
        }

        // Non Deduct Rpt1 Amount
        if (modifiedAPInvHed.NonDeductRpt1Amt != originalAPInvHed.NonDeductRpt1Amt)
        {
            changes.Add("APInvHed.NonDeductRpt1Amt`"
                       + originalAPInvHed.NonDeductRpt1Amt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductRpt1Amt.ToString().Replace("`", "-"));
        }

        // Non Deduct Rpt2 Amount
        if (modifiedAPInvHed.NonDeductRpt2Amt != originalAPInvHed.NonDeductRpt2Amt)
        {
            changes.Add("APInvHed.NonDeductRpt2Amt`"
                       + originalAPInvHed.NonDeductRpt2Amt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductRpt2Amt.ToString().Replace("`", "-"));
        }

        // Non Deduct Rpt3 Amount
        if (modifiedAPInvHed.NonDeductRpt3Amt != originalAPInvHed.NonDeductRpt3Amt)
        {
            changes.Add("APInvHed.NonDeductRpt3Amt`"
                       + originalAPInvHed.NonDeductRpt3Amt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductRpt3Amt.ToString().Replace("`", "-"));
        }

        // Non Deduct VAT Amount
        if (modifiedAPInvHed.NonDeductVATAmt != originalAPInvHed.NonDeductVATAmt)
        {
            changes.Add("APInvHed.NonDeductVATAmt`"
                       + originalAPInvHed.NonDeductVATAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductVATAmt.ToString().Replace("`", "-"));
        }

        // Non Deduct VAT Document Amount
        if (modifiedAPInvHed.NonDeductVATDocAmt != originalAPInvHed.NonDeductVATDocAmt)
        {
            changes.Add("APInvHed.NonDeductVATDocAmt`"
                       + originalAPInvHed.NonDeductVATDocAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductVATDocAmt.ToString().Replace("`", "-"));
        }

        // Non Deduct VAT Rpt1 Amount
        if (modifiedAPInvHed.NonDeductVATRpt1Amt != originalAPInvHed.NonDeductVATRpt1Amt)
        {
            changes.Add("APInvHed.NonDeductVATRpt1Amt`"
                       + originalAPInvHed.NonDeductVATRpt1Amt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductVATRpt1Amt.ToString().Replace("`", "-"));
        }

        // Non Deduct VAT Rpt2 Amount
        if (modifiedAPInvHed.NonDeductVATRpt2Amt != originalAPInvHed.NonDeductVATRpt2Amt)
        {
            changes.Add("APInvHed.NonDeductVATRpt2Amt`"
                       + originalAPInvHed.NonDeductVATRpt2Amt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductVATRpt2Amt.ToString().Replace("`", "-"));
        }

        // Non Deduct VAT Rpt3 Amount
        if (modifiedAPInvHed.NonDeductVATRpt3Amt != originalAPInvHed.NonDeductVATRpt3Amt)
        {
            changes.Add("APInvHed.NonDeductVATRpt3Amt`"
                       + originalAPInvHed.NonDeductVATRpt3Amt.ToString()
                       + "`"
                       + modifiedAPInvHed.NonDeductVATRpt3Amt.ToString().Replace("`", "-"));
        }

        // Import Number
        if (modifiedAPInvHed.ImportNum != originalAPInvHed.ImportNum)
        {
            changes.Add("APInvHed.ImportNum`"
                       + originalAPInvHed.ImportNum.ToString()
                       + "`"
                       + modifiedAPInvHed.ImportNum.ToString().Replace("`", "-"));
        }

        // Imported From
        if (modifiedAPInvHed.ImportedFrom != originalAPInvHed.ImportedFrom)
        {
            changes.Add("APInvHed.ImportedFrom`"
                       + originalAPInvHed.ImportedFrom.ToString()
                       + "`"
                       + modifiedAPInvHed.ImportedFrom.ToString().Replace("`", "-"));
        }

        // Imported Date
        if (modifiedAPInvHed.ImportedDate != originalAPInvHed.ImportedDate)
        {
            changes.Add("APInvHed.ImportedDate`"
                       + originalAPInvHed.ImportedDate.ToString()
                       + "`"
                       + modifiedAPInvHed.ImportedDate.ToString().Replace("`", "-"));
        }

        // Advance Tax Invoice
        if (modifiedAPInvHed.AdvTaxInv != originalAPInvHed.AdvTaxInv)
        {
            changes.Add("APInvHed.AdvTaxInv`"
                       + originalAPInvHed.AdvTaxInv.ToString()
                       + "`"
                       + modifiedAPInvHed.AdvTaxInv.ToString().Replace("`", "-"));
        }

        // In Price
        if (modifiedAPInvHed.InPrice != originalAPInvHed.InPrice)
        {
            changes.Add("APInvHed.InPrice`"
                       + originalAPInvHed.InPrice.ToString()
                       + "`"
                       + modifiedAPInvHed.InPrice.ToString().Replace("`", "-"));
        }

        // Transaction Document Type ID
        if (modifiedAPInvHed.TranDocTypeID != originalAPInvHed.TranDocTypeID)
        {
            changes.Add("APInvHed.TranDocTypeID`"
                       + originalAPInvHed.TranDocTypeID.ToString()
                       + "`"
                       + modifiedAPInvHed.TranDocTypeID.ToString().Replace("`", "-"));
        }

        // Dev Int1
        if (modifiedAPInvHed.DevInt1 != originalAPInvHed.DevInt1)
        {
            changes.Add("APInvHed.DevInt1`"
                       + originalAPInvHed.DevInt1.ToString()
                       + "`"
                       + modifiedAPInvHed.DevInt1.ToString().Replace("`", "-"));
        }

        // Dev Int2
        if (modifiedAPInvHed.DevInt2 != originalAPInvHed.DevInt2)
        {
            changes.Add("APInvHed.DevInt2`"
                       + originalAPInvHed.DevInt2.ToString()
                       + "`"
                       + modifiedAPInvHed.DevInt2.ToString().Replace("`", "-"));
        }

        // Dev Dec2
        if (modifiedAPInvHed.DevDec2 != originalAPInvHed.DevDec2)
        {
            changes.Add("APInvHed.DevDec2`"
                       + originalAPInvHed.DevDec2.ToString()
                       + "`"
                       + modifiedAPInvHed.DevDec2.ToString().Replace("`", "-"));
        }

        // Dev Dec3
        if (modifiedAPInvHed.DevDec3 != originalAPInvHed.DevDec3)
        {
            changes.Add("APInvHed.DevDec3`"
                       + originalAPInvHed.DevDec3.ToString()
                       + "`"
                       + modifiedAPInvHed.DevDec3.ToString().Replace("`", "-"));
        }

        // Dev Dec4
        if (modifiedAPInvHed.DevDec4 != originalAPInvHed.DevDec4)
        {
            changes.Add("APInvHed.DevDec4`"
                       + originalAPInvHed.DevDec4.ToString()
                       + "`"
                       + modifiedAPInvHed.DevDec4.ToString().Replace("`", "-"));
        }

        // Dev Log1
        if (modifiedAPInvHed.DevLog1 != originalAPInvHed.DevLog1)
        {
            changes.Add("APInvHed.DevLog1`"
                       + originalAPInvHed.DevLog1.ToString()
                       + "`"
                       + modifiedAPInvHed.DevLog1.ToString().Replace("`", "-"));
        }

        // Dev Log2
        if (modifiedAPInvHed.DevLog2 != originalAPInvHed.DevLog2)
        {
            changes.Add("APInvHed.DevLog2`"
                       + originalAPInvHed.DevLog2.ToString()
                       + "`"
                       + modifiedAPInvHed.DevLog2.ToString().Replace("`", "-"));
        }

        // Dev Char1
        if (modifiedAPInvHed.DevChar1 != originalAPInvHed.DevChar1)
        {
            changes.Add("APInvHed.DevChar1`"
                       + originalAPInvHed.DevChar1.ToString()
                       + "`"
                       + modifiedAPInvHed.DevChar1.ToString().Replace("`", "-"));
        }

        // Dev Char2
        if (modifiedAPInvHed.DevChar2 != originalAPInvHed.DevChar2)
        {
            changes.Add("APInvHed.DevChar2`"
                       + originalAPInvHed.DevChar2.ToString()
                       + "`"
                       + modifiedAPInvHed.DevChar2.ToString().Replace("`", "-"));
        }

        // Dev Date1
        if (modifiedAPInvHed.DevDate1 != originalAPInvHed.DevDate1)
        {
            changes.Add("APInvHed.DevDate1`"
                       + originalAPInvHed.DevDate1.ToString()
                       + "`"
                       + modifiedAPInvHed.DevDate1.ToString().Replace("`", "-"));
        }

        // Dev Date2
        if (modifiedAPInvHed.DevDate2 != originalAPInvHed.DevDate2)
        {
            changes.Add("APInvHed.DevDate2`"
                       + originalAPInvHed.DevDate2.ToString()
                       + "`"
                       + modifiedAPInvHed.DevDate2.ToString().Replace("`", "-"));
        }

        // Payment Number
        if (modifiedAPInvHed.PaymentNumber != originalAPInvHed.PaymentNumber)
        {
            changes.Add("APInvHed.PaymentNumber`"
                       + originalAPInvHed.PaymentNumber.ToString()
                       + "`"
                       + modifiedAPInvHed.PaymentNumber.ToString().Replace("`", "-"));
        }

        // Cycle Code
        if (modifiedAPInvHed.CycleCode != originalAPInvHed.CycleCode)
        {
            changes.Add("APInvHed.CycleCode`"
                       + originalAPInvHed.CycleCode.ToString()
                       + "`"
                       + modifiedAPInvHed.CycleCode.ToString().Replace("`", "-"));
        }

        // Duration
        if (modifiedAPInvHed.Duration != originalAPInvHed.Duration)
        {
            changes.Add("APInvHed.Duration`"
                       + originalAPInvHed.Duration.ToString()
                       + "`"
                       + modifiedAPInvHed.Duration.ToString().Replace("`", "-"));
        }

        // End Date
        if (modifiedAPInvHed.EndDate != originalAPInvHed.EndDate)
        {
            changes.Add("APInvHed.EndDate`"
                       + originalAPInvHed.EndDate.ToString()
                       + "`"
                       + modifiedAPInvHed.EndDate.ToString().Replace("`", "-"));
        }

        // Max Value Amount
        if (modifiedAPInvHed.MaxValueAmt != originalAPInvHed.MaxValueAmt)
        {
            changes.Add("APInvHed.MaxValueAmt`"
                       + originalAPInvHed.MaxValueAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.MaxValueAmt.ToString().Replace("`", "-"));
        }

        // Document Max Value Amount
        if (modifiedAPInvHed.DocMaxValueAmt != originalAPInvHed.DocMaxValueAmt)
        {
            changes.Add("APInvHed.DocMaxValueAmt`"
                       + originalAPInvHed.DocMaxValueAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocMaxValueAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Max Value Amount
        if (modifiedAPInvHed.Rpt1MaxValueAmt != originalAPInvHed.Rpt1MaxValueAmt)
        {
            changes.Add("APInvHed.Rpt1MaxValueAmt`"
                       + originalAPInvHed.Rpt1MaxValueAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1MaxValueAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Max Value Amount
        if (modifiedAPInvHed.Rpt2MaxValueAmt != originalAPInvHed.Rpt2MaxValueAmt)
        {
            changes.Add("APInvHed.Rpt2MaxValueAmt`"
                       + originalAPInvHed.Rpt2MaxValueAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2MaxValueAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Max Value Amount
        if (modifiedAPInvHed.Rpt3MaxValueAmt != originalAPInvHed.Rpt3MaxValueAmt)
        {
            changes.Add("APInvHed.Rpt3MaxValueAmt`"
                       + originalAPInvHed.Rpt3MaxValueAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3MaxValueAmt.ToString().Replace("`", "-"));
        }

        // Hold Invoice
        if (modifiedAPInvHed.HoldInvoice != originalAPInvHed.HoldInvoice)
        {
            changes.Add("APInvHed.HoldInvoice`"
                       + originalAPInvHed.HoldInvoice.ToString()
                       + "`"
                       + modifiedAPInvHed.HoldInvoice.ToString().Replace("`", "-"));
        }

        // Copy Latest Invoice
        if (modifiedAPInvHed.CopyLatestInvoice != originalAPInvHed.CopyLatestInvoice)
        {
            changes.Add("APInvHed.CopyLatestInvoice`"
                       + originalAPInvHed.CopyLatestInvoice.ToString()
                       + "`"
                       + modifiedAPInvHed.CopyLatestInvoice.ToString().Replace("`", "-"));
        }

        // Override End Date
        if (modifiedAPInvHed.OverrideEndDate != originalAPInvHed.OverrideEndDate)
        {
            changes.Add("APInvHed.OverrideEndDate`"
                       + originalAPInvHed.OverrideEndDate.ToString()
                       + "`"
                       + modifiedAPInvHed.OverrideEndDate.ToString().Replace("`", "-"));
        }

        // Cycle Inactive
        if (modifiedAPInvHed.CycleInactive != originalAPInvHed.CycleInactive)
        {
            changes.Add("APInvHed.CycleInactive`"
                       + originalAPInvHed.CycleInactive.ToString()
                       + "`"
                       + modifiedAPInvHed.CycleInactive.ToString().Replace("`", "-"));
        }

        // Recur Source
        if (modifiedAPInvHed.RecurSource != originalAPInvHed.RecurSource)
        {
            changes.Add("APInvHed.RecurSource`"
                       + originalAPInvHed.RecurSource.ToString()
                       + "`"
                       + modifiedAPInvHed.RecurSource.ToString().Replace("`", "-"));
        }

        // Instance Number
        if (modifiedAPInvHed.InstanceNum != originalAPInvHed.InstanceNum)
        {
            changes.Add("APInvHed.InstanceNum`"
                       + originalAPInvHed.InstanceNum.ToString()
                       + "`"
                       + modifiedAPInvHed.InstanceNum.ToString().Replace("`", "-"));
        }

        // Recur Balance
        if (modifiedAPInvHed.RecurBalance != originalAPInvHed.RecurBalance)
        {
            changes.Add("APInvHed.RecurBalance`"
                       + originalAPInvHed.RecurBalance.ToString()
                       + "`"
                       + modifiedAPInvHed.RecurBalance.ToString().Replace("`", "-"));
        }

        // Document Recur Balance
        if (modifiedAPInvHed.DocRecurBalance != originalAPInvHed.DocRecurBalance)
        {
            changes.Add("APInvHed.DocRecurBalance`"
                       + originalAPInvHed.DocRecurBalance.ToString()
                       + "`"
                       + modifiedAPInvHed.DocRecurBalance.ToString().Replace("`", "-"));
        }

        // Rpt1 Recur Balance
        if (modifiedAPInvHed.Rpt1RecurBalance != originalAPInvHed.Rpt1RecurBalance)
        {
            changes.Add("APInvHed.Rpt1RecurBalance`"
                       + originalAPInvHed.Rpt1RecurBalance.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1RecurBalance.ToString().Replace("`", "-"));
        }

        // Rpt2 Recur Balance
        if (modifiedAPInvHed.Rpt2RecurBalance != originalAPInvHed.Rpt2RecurBalance)
        {
            changes.Add("APInvHed.Rpt2RecurBalance`"
                       + originalAPInvHed.Rpt2RecurBalance.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2RecurBalance.ToString().Replace("`", "-"));
        }

        // Rpt3 Recur Balance
        if (modifiedAPInvHed.Rpt3RecurBalance != originalAPInvHed.Rpt3RecurBalance)
        {
            changes.Add("APInvHed.Rpt3RecurBalance`"
                       + originalAPInvHed.Rpt3RecurBalance.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3RecurBalance.ToString().Replace("`", "-"));
        }

        // Last Date
        if (modifiedAPInvHed.LastDate != originalAPInvHed.LastDate)
        {
            changes.Add("APInvHed.LastDate`"
                       + originalAPInvHed.LastDate.ToString()
                       + "`"
                       + modifiedAPInvHed.LastDate.ToString().Replace("`", "-"));
        }

        // Is Recurring
        if (modifiedAPInvHed.IsRecurring != originalAPInvHed.IsRecurring)
        {
            changes.Add("APInvHed.IsRecurring`"
                       + originalAPInvHed.IsRecurring.ToString()
                       + "`"
                       + modifiedAPInvHed.IsRecurring.ToString().Replace("`", "-"));
        }

        // Invoice Number List
        if (modifiedAPInvHed.InvoiceNumList != originalAPInvHed.InvoiceNumList)
        {
            changes.Add("APInvHed.InvoiceNumList`"
                       + originalAPInvHed.InvoiceNumList.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceNumList.ToString().Replace("`", "-"));
        }

        // Is Max Value
        if (modifiedAPInvHed.IsMaxValue != originalAPInvHed.IsMaxValue)
        {
            changes.Add("APInvHed.IsMaxValue`"
                       + originalAPInvHed.IsMaxValue.ToString()
                       + "`"
                       + modifiedAPInvHed.IsMaxValue.ToString().Replace("`", "-"));
        }

        // CHISR Code Line
        if (modifiedAPInvHed.CHISRCodeLine != originalAPInvHed.CHISRCodeLine)
        {
            changes.Add("APInvHed.CHISRCodeLine`"
                       + originalAPInvHed.CHISRCodeLine.ToString()
                       + "`"
                       + modifiedAPInvHed.CHISRCodeLine.ToString().Replace("`", "-"));
        }

        // DM Reason
        if (modifiedAPInvHed.DMReason != originalAPInvHed.DMReason)
        {
            changes.Add("APInvHed.DMReason`"
                       + originalAPInvHed.DMReason.ToString()
                       + "`"
                       + modifiedAPInvHed.DMReason.ToString().Replace("`", "-"));
        }

        // Urgent Payment
        if (modifiedAPInvHed.UrgentPayment != originalAPInvHed.UrgentPayment)
        {
            changes.Add("APInvHed.UrgentPayment`"
                       + originalAPInvHed.UrgentPayment.ToString()
                       + "`"
                       + modifiedAPInvHed.UrgentPayment.ToString().Replace("`", "-"));
        }

        // AG Document Page Number
        if (modifiedAPInvHed.AGDocPageNum != originalAPInvHed.AGDocPageNum)
        {
            changes.Add("APInvHed.AGDocPageNum`"
                       + originalAPInvHed.AGDocPageNum.ToString()
                       + "`"
                       + modifiedAPInvHed.AGDocPageNum.ToString().Replace("`", "-"));
        }

        // AG CAI CAE Mark
        if (modifiedAPInvHed.AGCAICAEMark != originalAPInvHed.AGCAICAEMark)
        {
            changes.Add("APInvHed.AGCAICAEMark`"
                       + originalAPInvHed.AGCAICAEMark.ToString()
                       + "`"
                       + modifiedAPInvHed.AGCAICAEMark.ToString().Replace("`", "-"));
        }

        // AG CAI CAE Number
        if (modifiedAPInvHed.AGCAICAENum != originalAPInvHed.AGCAICAENum)
        {
            changes.Add("APInvHed.AGCAICAENum`"
                       + originalAPInvHed.AGCAICAENum.ToString()
                       + "`"
                       + modifiedAPInvHed.AGCAICAENum.ToString().Replace("`", "-"));
        }

        // AG CAI CAE Expiration Date
        if (modifiedAPInvHed.AGCAICAEExpirationDate != originalAPInvHed.AGCAICAEExpirationDate)
        {
            changes.Add("APInvHed.AGCAICAEExpirationDate`"
                       + originalAPInvHed.AGCAICAEExpirationDate.ToString()
                       + "`"
                       + modifiedAPInvHed.AGCAICAEExpirationDate.ToString().Replace("`", "-"));
        }

        // AG Available Tax Credit Flag
        if (modifiedAPInvHed.AGAvTaxCreditFlag != originalAPInvHed.AGAvTaxCreditFlag)
        {
            changes.Add("APInvHed.AGAvTaxCreditFlag`"
                       + originalAPInvHed.AGAvTaxCreditFlag.ToString()
                       + "`"
                       + modifiedAPInvHed.AGAvTaxCreditFlag.ToString().Replace("`", "-"));
        }

        // AG Use Good Default Mark
        if (modifiedAPInvHed.AGUseGoodDefaultMark != originalAPInvHed.AGUseGoodDefaultMark)
        {
            changes.Add("APInvHed.AGUseGoodDefaultMark`"
                       + originalAPInvHed.AGUseGoodDefaultMark.ToString()
                       + "`"
                       + modifiedAPInvHed.AGUseGoodDefaultMark.ToString().Replace("`", "-"));
        }

        // AG Customs Clearance Number
        if (modifiedAPInvHed.AGCustomsClearanceNum != originalAPInvHed.AGCustomsClearanceNum)
        {
            changes.Add("APInvHed.AGCustomsClearanceNum`"
                       + originalAPInvHed.AGCustomsClearanceNum.ToString()
                       + "`"
                       + modifiedAPInvHed.AGCustomsClearanceNum.ToString().Replace("`", "-"));
        }

        // AG Customs Code
        if (modifiedAPInvHed.AGCustomsCode != originalAPInvHed.AGCustomsCode)
        {
            changes.Add("APInvHed.AGCustomsCode`"
                       + originalAPInvHed.AGCustomsCode.ToString()
                       + "`"
                       + modifiedAPInvHed.AGCustomsCode.ToString().Replace("`", "-"));
        }

        // AG Destination Code
        if (modifiedAPInvHed.AGDestinationCode != originalAPInvHed.AGDestinationCode)
        {
            changes.Add("APInvHed.AGDestinationCode`"
                       + originalAPInvHed.AGDestinationCode.ToString()
                       + "`"
                       + modifiedAPInvHed.AGDestinationCode.ToString().Replace("`", "-"));
        }

        // Head Number
        if (modifiedAPInvHed.HeadNum != originalAPInvHed.HeadNum)
        {
            changes.Add("APInvHed.HeadNum`"
                       + originalAPInvHed.HeadNum.ToString()
                       + "`"
                       + modifiedAPInvHed.HeadNum.ToString().Replace("`", "-"));
        }

        // Transaction Type
        if (modifiedAPInvHed.TranType != originalAPInvHed.TranType)
        {
            changes.Add("APInvHed.TranType`"
                       + originalAPInvHed.TranType.ToString()
                       + "`"
                       + modifiedAPInvHed.TranType.ToString().Replace("`", "-"));
        }

        // Tax Service ID
        if (modifiedAPInvHed.TaxSvcID != originalAPInvHed.TaxSvcID)
        {
            changes.Add("APInvHed.TaxSvcID`"
                       + originalAPInvHed.TaxSvcID.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxSvcID.ToString().Replace("`", "-"));
        }

        // TW Declare Year
        if (modifiedAPInvHed.TWDeclareYear != originalAPInvHed.TWDeclareYear)
        {
            changes.Add("APInvHed.TWDeclareYear`"
                       + originalAPInvHed.TWDeclareYear.ToString()
                       + "`"
                       + modifiedAPInvHed.TWDeclareYear.ToString().Replace("`", "-"));
        }

        // TW Declare Period
        if (modifiedAPInvHed.TWDeclarePeriod != originalAPInvHed.TWDeclarePeriod)
        {
            changes.Add("APInvHed.TWDeclarePeriod`"
                       + originalAPInvHed.TWDeclarePeriod.ToString()
                       + "`"
                       + modifiedAPInvHed.TWDeclarePeriod.ToString().Replace("`", "-"));
        }

        // AP Check Group ID
        if (modifiedAPInvHed.APChkGrpID != originalAPInvHed.APChkGrpID)
        {
            changes.Add("APInvHed.APChkGrpID`"
                       + originalAPInvHed.APChkGrpID.ToString()
                       + "`"
                       + modifiedAPInvHed.APChkGrpID.ToString().Replace("`", "-"));
        }

        // Invoice Type
        if (modifiedAPInvHed.InvoiceType != originalAPInvHed.InvoiceType)
        {
            changes.Add("APInvHed.InvoiceType`"
                       + originalAPInvHed.InvoiceType.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceType.ToString().Replace("`", "-"));
        }

        // PE Computational Cost
        if (modifiedAPInvHed.PEComputationalCost != originalAPInvHed.PEComputationalCost)
        {
            changes.Add("APInvHed.PEComputationalCost`"
                       + originalAPInvHed.PEComputationalCost.ToString()
                       + "`"
                       + modifiedAPInvHed.PEComputationalCost.ToString().Replace("`", "-"));
        }

        // Referenced By BOE
        if (modifiedAPInvHed.ReferencedByBOE != originalAPInvHed.ReferencedByBOE)
        {
            changes.Add("APInvHed.ReferencedByBOE`"
                       + originalAPInvHed.ReferencedByBOE.ToString()
                       + "`"
                       + modifiedAPInvHed.ReferencedByBOE.ToString().Replace("`", "-"));
        }



        // Customs Number
        if (modifiedAPInvHed.CustomsNumber != originalAPInvHed.CustomsNumber)
        {
            changes.Add("APInvHed.CustomsNumber`"
                       + originalAPInvHed.CustomsNumber.ToString()
                       + "`"
                       + modifiedAPInvHed.CustomsNumber.ToString().Replace("`", "-"));
        }

        // Received Date
        if (modifiedAPInvHed.ReceivedDate != originalAPInvHed.ReceivedDate)
        {
            changes.Add("APInvHed.ReceivedDate`"
                       + originalAPInvHed.ReceivedDate.ToString()
                       + "`"
                       + modifiedAPInvHed.ReceivedDate.ToString().Replace("`", "-"));
        }

        // Customer Override
        if (modifiedAPInvHed.CustOverride != originalAPInvHed.CustOverride)
        {
            changes.Add("APInvHed.CustOverride`"
                       + originalAPInvHed.CustOverride.ToString()
                       + "`"
                       + modifiedAPInvHed.CustOverride.ToString().Replace("`", "-"));
        }

        // Pre Payment Number
        if (modifiedAPInvHed.PrePaymentNum != originalAPInvHed.PrePaymentNum)
        {
            changes.Add("APInvHed.PrePaymentNum`"
                       + originalAPInvHed.PrePaymentNum.ToString()
                       + "`"
                       + modifiedAPInvHed.PrePaymentNum.ToString().Replace("`", "-"));
        }

        // Pre Payment Amount
        if (modifiedAPInvHed.PrePaymentAmt != originalAPInvHed.PrePaymentAmt)
        {
            changes.Add("APInvHed.PrePaymentAmt`"
                       + originalAPInvHed.PrePaymentAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.PrePaymentAmt.ToString().Replace("`", "-"));
        }

        // Document Pre Payment Amount
        if (modifiedAPInvHed.DocPrePaymentAmt != originalAPInvHed.DocPrePaymentAmt)
        {
            changes.Add("APInvHed.DocPrePaymentAmt`"
                       + originalAPInvHed.DocPrePaymentAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocPrePaymentAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Pre Payment Amount
        if (modifiedAPInvHed.Rpt1PrePaymentAmt != originalAPInvHed.Rpt1PrePaymentAmt)
        {
            changes.Add("APInvHed.Rpt1PrePaymentAmt`"
                       + originalAPInvHed.Rpt1PrePaymentAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1PrePaymentAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Pre Payment Amount
        if (modifiedAPInvHed.Rpt2PrePaymentAmt != originalAPInvHed.Rpt2PrePaymentAmt)
        {
            changes.Add("APInvHed.Rpt2PrePaymentAmt`"
                       + originalAPInvHed.Rpt2PrePaymentAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2PrePaymentAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Pre Payment Amount
        if (modifiedAPInvHed.Rpt3PrePaymentAmt != originalAPInvHed.Rpt3PrePaymentAmt)
        {
            changes.Add("APInvHed.Rpt3PrePaymentAmt`"
                       + originalAPInvHed.Rpt3PrePaymentAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3PrePaymentAmt.ToString().Replace("`", "-"));
        }

        // PE AP Pay Number
        if (modifiedAPInvHed.PEAPPayNum != originalAPInvHed.PEAPPayNum)
        {
            changes.Add("APInvHed.PEAPPayNum`"
                       + originalAPInvHed.PEAPPayNum.ToString()
                       + "`"
                       + modifiedAPInvHed.PEAPPayNum.ToString().Replace("`", "-"));
        }

        // PE Det Tax Amount
        if (modifiedAPInvHed.PEDetTaxAmt != originalAPInvHed.PEDetTaxAmt)
        {
            changes.Add("APInvHed.PEDetTaxAmt`"
                       + originalAPInvHed.PEDetTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.PEDetTaxAmt.ToString().Replace("`", "-"));
        }

        // PE Det Tax Currency Code
        if (modifiedAPInvHed.PEDetTaxCurrencyCode != originalAPInvHed.PEDetTaxCurrencyCode)
        {
            changes.Add("APInvHed.PEDetTaxCurrencyCode`"
                       + originalAPInvHed.PEDetTaxCurrencyCode.ToString()
                       + "`"
                       + modifiedAPInvHed.PEDetTaxCurrencyCode.ToString().Replace("`", "-"));
        }

        // PE SUNAT Dep Amount
        if (modifiedAPInvHed.PESUNATDepAmt != originalAPInvHed.PESUNATDepAmt)
        {
            changes.Add("APInvHed.PESUNATDepAmt`"
                       + originalAPInvHed.PESUNATDepAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.PESUNATDepAmt.ToString().Replace("`", "-"));
        }

        // Document PE SUNAT Dep Amount
        if (modifiedAPInvHed.DocPESUNATDepAmt != originalAPInvHed.DocPESUNATDepAmt)
        {
            changes.Add("APInvHed.DocPESUNATDepAmt`"
                       + originalAPInvHed.DocPESUNATDepAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocPESUNATDepAmt.ToString().Replace("`", "-"));
        }

        // PE SUNAT Dep Date
        if (modifiedAPInvHed.PESUNATDepDate != originalAPInvHed.PESUNATDepDate)
        {
            changes.Add("APInvHed.PESUNATDepDate`"
                       + originalAPInvHed.PESUNATDepDate.ToString()
                       + "`"
                       + modifiedAPInvHed.PESUNATDepDate.ToString().Replace("`", "-"));
        }

        // PE SUNAT Dep Number
        if (modifiedAPInvHed.PESUNATDepNum != originalAPInvHed.PESUNATDepNum)
        {
            changes.Add("APInvHed.PESUNATDepNum`"
                       + originalAPInvHed.PESUNATDepNum.ToString()
                       + "`"
                       + modifiedAPInvHed.PESUNATDepNum.ToString().Replace("`", "-"));
        }

        // PE SUNAT Number
        if (modifiedAPInvHed.PESUNATNum != originalAPInvHed.PESUNATNum)
        {
            changes.Add("APInvHed.PESUNATNum`"
                       + originalAPInvHed.PESUNATNum.ToString()
                       + "`"
                       + modifiedAPInvHed.PESUNATNum.ToString().Replace("`", "-"));
        }

        // Document PE Det Tax Amount
        if (modifiedAPInvHed.DocPEDetTaxAmt != originalAPInvHed.DocPEDetTaxAmt)
        {
            changes.Add("APInvHed.DocPEDetTaxAmt`"
                       + originalAPInvHed.DocPEDetTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocPEDetTaxAmt.ToString().Replace("`", "-"));
        }

        // MX Fiscal Folio
        if (modifiedAPInvHed.MXFiscalFolio != originalAPInvHed.MXFiscalFolio)
        {
            changes.Add("APInvHed.MXFiscalFolio`"
                       + originalAPInvHed.MXFiscalFolio.ToString()
                       + "`"
                       + modifiedAPInvHed.MXFiscalFolio.ToString().Replace("`", "-"));
        }



        // Changed By
        if (modifiedAPInvHed.ChangedBy != originalAPInvHed.ChangedBy)
        {
            changes.Add("APInvHed.ChangedBy`"
                       + originalAPInvHed.ChangedBy.ToString()
                       + "`"
                       + modifiedAPInvHed.ChangedBy.ToString().Replace("`", "-"));
        }

        // Change Date
        if (modifiedAPInvHed.ChangeDate != originalAPInvHed.ChangeDate)
        {
            changes.Add("APInvHed.ChangeDate`"
                       + originalAPInvHed.ChangeDate.ToString()
                       + "`"
                       + modifiedAPInvHed.ChangeDate.ToString().Replace("`", "-"));
        }

        // Pre Pay Head Number
        if (modifiedAPInvHed.PrePayHeadNum != originalAPInvHed.PrePayHeadNum)
        {
            changes.Add("APInvHed.PrePayHeadNum`"
                       + originalAPInvHed.PrePayHeadNum.ToString()
                       + "`"
                       + modifiedAPInvHed.PrePayHeadNum.ToString().Replace("`", "-"));
        }

        // MX Retention Code
        if (modifiedAPInvHed.MXRetentionCode != originalAPInvHed.MXRetentionCode)
        {
            changes.Add("APInvHed.MXRetentionCode`"
                       + originalAPInvHed.MXRetentionCode.ToString()
                       + "`"
                       + modifiedAPInvHed.MXRetentionCode.ToString().Replace("`", "-"));
        }



        // MX Tax Registration ID
        if (modifiedAPInvHed.MXTaxRegID != originalAPInvHed.MXTaxRegID)
        {
            changes.Add("APInvHed.MXTaxRegID`"
                       + originalAPInvHed.MXTaxRegID.ToString()
                       + "`"
                       + modifiedAPInvHed.MXTaxRegID.ToString().Replace("`", "-"));
        }

        // MX Tax Receipt Type
        if (modifiedAPInvHed.MXTaxReceiptType != originalAPInvHed.MXTaxReceiptType)
        {
            changes.Add("APInvHed.MXTaxReceiptType`"
                       + originalAPInvHed.MXTaxReceiptType.ToString()
                       + "`"
                       + modifiedAPInvHed.MXTaxReceiptType.ToString().Replace("`", "-"));
        }

        // MX ABC Code
        if (modifiedAPInvHed.MXABCCode != originalAPInvHed.MXABCCode)
        {
            changes.Add("APInvHed.MXABCCode`"
                       + originalAPInvHed.MXABCCode.ToString()
                       + "`"
                       + modifiedAPInvHed.MXABCCode.ToString().Replace("`", "-"));
        }

        // MX Movement Type
        if (modifiedAPInvHed.MXMovementType != originalAPInvHed.MXMovementType)
        {
            changes.Add("APInvHed.MXMovementType`"
                       + originalAPInvHed.MXMovementType.ToString()
                       + "`"
                       + modifiedAPInvHed.MXMovementType.ToString().Replace("`", "-"));
        }

        // MX SAT Certificate
        if (modifiedAPInvHed.MXSATCertificate != originalAPInvHed.MXSATCertificate)
        {
            changes.Add("APInvHed.MXSATCertificate`"
                       + originalAPInvHed.MXSATCertificate.ToString()
                       + "`"
                       + modifiedAPInvHed.MXSATCertificate.ToString().Replace("`", "-"));
        }

        // MX SAT Certificate Date
        if (modifiedAPInvHed.MXSATCertificateDate != originalAPInvHed.MXSATCertificateDate)
        {
            changes.Add("APInvHed.MXSATCertificateDate`"
                       + originalAPInvHed.MXSATCertificateDate.ToString()
                       + "`"
                       + modifiedAPInvHed.MXSATCertificateDate.ToString().Replace("`", "-"));
        }

        // MX SAT Seal
        if (modifiedAPInvHed.MXSATSeal != originalAPInvHed.MXSATSeal)
        {
            changes.Add("APInvHed.MXSATSeal`"
                       + originalAPInvHed.MXSATSeal.ToString()
                       + "`"
                       + modifiedAPInvHed.MXSATSeal.ToString().Replace("`", "-"));
        }





































        // Screen Discount Amount
        if (modifiedAPInvHed.ScrDiscountAmt != originalAPInvHed.ScrDiscountAmt)
        {
            changes.Add("APInvHed.ScrDiscountAmt`"
                       + originalAPInvHed.ScrDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDiscountAmt.ToString().Replace("`", "-"));
        }

        // Document Screen Discount Amount
        if (modifiedAPInvHed.DocScrDiscountAmt != originalAPInvHed.DocScrDiscountAmt)
        {
            changes.Add("APInvHed.DocScrDiscountAmt`"
                       + originalAPInvHed.DocScrDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocScrDiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Discount Amount
        if (modifiedAPInvHed.Rpt1ScrDiscountAmt != originalAPInvHed.Rpt1ScrDiscountAmt)
        {
            changes.Add("APInvHed.Rpt1ScrDiscountAmt`"
                       + originalAPInvHed.Rpt1ScrDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrDiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Discount Amount
        if (modifiedAPInvHed.Rpt2ScrDiscountAmt != originalAPInvHed.Rpt2ScrDiscountAmt)
        {
            changes.Add("APInvHed.Rpt2ScrDiscountAmt`"
                       + originalAPInvHed.Rpt2ScrDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrDiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Discount Amount
        if (modifiedAPInvHed.Rpt3ScrDiscountAmt != originalAPInvHed.Rpt3ScrDiscountAmt)
        {
            changes.Add("APInvHed.Rpt3ScrDiscountAmt`"
                       + originalAPInvHed.Rpt3ScrDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrDiscountAmt.ToString().Replace("`", "-"));
        }

        // Screen Tax Amount
        if (modifiedAPInvHed.ScrTaxAmt != originalAPInvHed.ScrTaxAmt)
        {
            changes.Add("APInvHed.ScrTaxAmt`"
                       + originalAPInvHed.ScrTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTaxAmt.ToString().Replace("`", "-"));
        }

        // Document Screen Tax Amount
        if (modifiedAPInvHed.DocScrTaxAmt != originalAPInvHed.DocScrTaxAmt)
        {
            changes.Add("APInvHed.DocScrTaxAmt`"
                       + originalAPInvHed.DocScrTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocScrTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Tax Amount
        if (modifiedAPInvHed.Rpt1ScrTaxAmt != originalAPInvHed.Rpt1ScrTaxAmt)
        {
            changes.Add("APInvHed.Rpt1ScrTaxAmt`"
                       + originalAPInvHed.Rpt1ScrTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Tax Amount
        if (modifiedAPInvHed.Rpt2ScrTaxAmt != originalAPInvHed.Rpt2ScrTaxAmt)
        {
            changes.Add("APInvHed.Rpt2ScrTaxAmt`"
                       + originalAPInvHed.Rpt2ScrTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Tax Amount
        if (modifiedAPInvHed.Rpt3ScrTaxAmt != originalAPInvHed.Rpt3ScrTaxAmt)
        {
            changes.Add("APInvHed.Rpt3ScrTaxAmt`"
                       + originalAPInvHed.Rpt3ScrTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTaxAmt.ToString().Replace("`", "-"));
        }

        // Screen Total Miscellaneous Charge
        if (modifiedAPInvHed.ScrTotalMiscChrg != originalAPInvHed.ScrTotalMiscChrg)
        {
            changes.Add("APInvHed.ScrTotalMiscChrg`"
                       + originalAPInvHed.ScrTotalMiscChrg.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Document Screen Total Miscellaneous Charge
        if (modifiedAPInvHed.DocScrTotalMiscChrg != originalAPInvHed.DocScrTotalMiscChrg)
        {
            changes.Add("APInvHed.DocScrTotalMiscChrg`"
                       + originalAPInvHed.DocScrTotalMiscChrg.ToString()
                       + "`"
                       + modifiedAPInvHed.DocScrTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Total Miscellaneous Charge
        if (modifiedAPInvHed.Rpt1ScrTotalMiscChrg != originalAPInvHed.Rpt1ScrTotalMiscChrg)
        {
            changes.Add("APInvHed.Rpt1ScrTotalMiscChrg`"
                       + originalAPInvHed.Rpt1ScrTotalMiscChrg.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Total Miscellaneous Charge
        if (modifiedAPInvHed.Rpt2ScrTotalMiscChrg != originalAPInvHed.Rpt2ScrTotalMiscChrg)
        {
            changes.Add("APInvHed.Rpt2ScrTotalMiscChrg`"
                       + originalAPInvHed.Rpt2ScrTotalMiscChrg.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Total Miscellaneous Charge
        if (modifiedAPInvHed.Rpt3ScrTotalMiscChrg != originalAPInvHed.Rpt3ScrTotalMiscChrg)
        {
            changes.Add("APInvHed.Rpt3ScrTotalMiscChrg`"
                       + originalAPInvHed.Rpt3ScrTotalMiscChrg.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Screen Withhold Amount
        if (modifiedAPInvHed.ScrWithholdAmt != originalAPInvHed.ScrWithholdAmt)
        {
            changes.Add("APInvHed.ScrWithholdAmt`"
                       + originalAPInvHed.ScrWithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrWithholdAmt.ToString().Replace("`", "-"));
        }

        // Document Screen Withhold Amount
        if (modifiedAPInvHed.DocScrWithholdAmt != originalAPInvHed.DocScrWithholdAmt)
        {
            changes.Add("APInvHed.DocScrWithholdAmt`"
                       + originalAPInvHed.DocScrWithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocScrWithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Withhold Amount
        if (modifiedAPInvHed.Rpt1ScrWithholdAmt != originalAPInvHed.Rpt1ScrWithholdAmt)
        {
            changes.Add("APInvHed.Rpt1ScrWithholdAmt`"
                       + originalAPInvHed.Rpt1ScrWithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrWithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Withhold Amount
        if (modifiedAPInvHed.Rpt2ScrWithholdAmt != originalAPInvHed.Rpt2ScrWithholdAmt)
        {
            changes.Add("APInvHed.Rpt2ScrWithholdAmt`"
                       + originalAPInvHed.Rpt2ScrWithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrWithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Withhold Amount
        if (modifiedAPInvHed.Rpt3ScrWithholdAmt != originalAPInvHed.Rpt3ScrWithholdAmt)
        {
            changes.Add("APInvHed.Rpt3ScrWithholdAmt`"
                       + originalAPInvHed.Rpt3ScrWithholdAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrWithholdAmt.ToString().Replace("`", "-"));
        }

        // Screen Invoice Amount
        if (modifiedAPInvHed.ScrInvoiceAmt != originalAPInvHed.ScrInvoiceAmt)
        {
            changes.Add("APInvHed.ScrInvoiceAmt`"
                       + originalAPInvHed.ScrInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Document Screen Invoice Amount
        if (modifiedAPInvHed.DocScrInvoiceAmt != originalAPInvHed.DocScrInvoiceAmt)
        {
            changes.Add("APInvHed.DocScrInvoiceAmt`"
                       + originalAPInvHed.DocScrInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocScrInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Invoice Amount
        if (modifiedAPInvHed.Rpt1ScrInvoiceAmt != originalAPInvHed.Rpt1ScrInvoiceAmt)
        {
            changes.Add("APInvHed.Rpt1ScrInvoiceAmt`"
                       + originalAPInvHed.Rpt1ScrInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Invoice Amount
        if (modifiedAPInvHed.Rpt2ScrInvoiceAmt != originalAPInvHed.Rpt2ScrInvoiceAmt)
        {
            changes.Add("APInvHed.Rpt2ScrInvoiceAmt`"
                       + originalAPInvHed.Rpt2ScrInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Invoice Amount
        if (modifiedAPInvHed.Rpt3ScrInvoiceAmt != originalAPInvHed.Rpt3ScrInvoiceAmt)
        {
            changes.Add("APInvHed.Rpt3ScrInvoiceAmt`"
                       + originalAPInvHed.Rpt3ScrInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Screen Invoice Vendor Amount
        if (modifiedAPInvHed.ScrInvoiceVendorAmt != originalAPInvHed.ScrInvoiceVendorAmt)
        {
            changes.Add("APInvHed.ScrInvoiceVendorAmt`"
                       + originalAPInvHed.ScrInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Document Screen Invoice Vendor Amount
        if (modifiedAPInvHed.DocScrInvoiceVendorAmt != originalAPInvHed.DocScrInvoiceVendorAmt)
        {
            changes.Add("APInvHed.DocScrInvoiceVendorAmt`"
                       + originalAPInvHed.DocScrInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocScrInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt1ScrInvoiceVendorAmt != originalAPInvHed.Rpt1ScrInvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt1ScrInvoiceVendorAmt`"
                       + originalAPInvHed.Rpt1ScrInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt2ScrInvoiceVendorAmt != originalAPInvHed.Rpt2ScrInvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt2ScrInvoiceVendorAmt`"
                       + originalAPInvHed.Rpt2ScrInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt3ScrInvoiceVendorAmt != originalAPInvHed.Rpt3ScrInvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt3ScrInvoiceVendorAmt`"
                       + originalAPInvHed.Rpt3ScrInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrInvoiceVendorAmt.ToString().Replace("`", "-"));
        }



        // System Transaction Type
        if (modifiedAPInvHed.SysTransType != originalAPInvHed.SysTransType)
        {
            changes.Add("APInvHed.SysTransType`"
                       + originalAPInvHed.SysTransType.ToString()
                       + "`"
                       + modifiedAPInvHed.SysTransType.ToString().Replace("`", "-"));
        }

        // System Row ID
        if (modifiedAPInvHed.SysRowID != originalAPInvHed.SysRowID)
        {
            changes.Add("APInvHed.SysRowID`"
                       + originalAPInvHed.SysRowID.ToString()
                       + "`"
                       + modifiedAPInvHed.SysRowID.ToString().Replace("`", "-"));
        }

        // System Revision ID
        if (modifiedAPInvHed.SysRevID != originalAPInvHed.SysRevID)
        {
            changes.Add("APInvHed.SysRevID`"
                       + originalAPInvHed.SysRevID.ToString()
                       + "`"
                       + modifiedAPInvHed.SysRevID.ToString().Replace("`", "-"));
        }

        // Created By
        if (modifiedAPInvHed.CreatedBy != originalAPInvHed.CreatedBy)
        {
            changes.Add("APInvHed.CreatedBy`"
                       + originalAPInvHed.CreatedBy.ToString()
                       + "`"
                       + modifiedAPInvHed.CreatedBy.ToString().Replace("`", "-"));
        }

        // Created On
        if (modifiedAPInvHed.CreatedOn != originalAPInvHed.CreatedOn)
        {
            changes.Add("APInvHed.CreatedOn`"
                       + originalAPInvHed.CreatedOn.ToString()
                       + "`"
                       + modifiedAPInvHed.CreatedOn.ToString().Replace("`", "-"));
        }

        // Updated By
        if (modifiedAPInvHed.UpdatedBy != originalAPInvHed.UpdatedBy)
        {
            changes.Add("APInvHed.UpdatedBy`"
                       + originalAPInvHed.UpdatedBy.ToString()
                       + "`"
                       + modifiedAPInvHed.UpdatedBy.ToString().Replace("`", "-"));
        }

        // Updated On
        if (modifiedAPInvHed.UpdatedOn != originalAPInvHed.UpdatedOn)
        {
            changes.Add("APInvHed.UpdatedOn`"
                       + originalAPInvHed.UpdatedOn.ToString()
                       + "`"
                       + modifiedAPInvHed.UpdatedOn.ToString().Replace("`", "-"));
        }

        // Tax Liability
        if (modifiedAPInvHed.TaxLiability != originalAPInvHed.TaxLiability)
        {
            changes.Add("APInvHed.TaxLiability`"
                       + originalAPInvHed.TaxLiability.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxLiability.ToString().Replace("`", "-"));
        }

        // Tax Type Code
        if (modifiedAPInvHed.TaxTypeCode != originalAPInvHed.TaxTypeCode)
        {
            changes.Add("APInvHed.TaxTypeCode`"
                       + originalAPInvHed.TaxTypeCode.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxTypeCode.ToString().Replace("`", "-"));
        }

        // Day Limit
        if (modifiedAPInvHed.DayLimit != originalAPInvHed.DayLimit)
        {
            changes.Add("APInvHed.DayLimit`"
                       + originalAPInvHed.DayLimit.ToString()
                       + "`"
                       + modifiedAPInvHed.DayLimit.ToString().Replace("`", "-"));
        }

        // Month Limit
        if (modifiedAPInvHed.MonthLimit != originalAPInvHed.MonthLimit)
        {
            changes.Add("APInvHed.MonthLimit`"
                       + originalAPInvHed.MonthLimit.ToString()
                       + "`"
                       + modifiedAPInvHed.MonthLimit.ToString().Replace("`", "-"));
        }

        // Cash Discount Taken
        if (modifiedAPInvHed.CashDiscTaken != originalAPInvHed.CashDiscTaken)
        {
            changes.Add("APInvHed.CashDiscTaken`"
                       + originalAPInvHed.CashDiscTaken.ToString()
                       + "`"
                       + modifiedAPInvHed.CashDiscTaken.ToString().Replace("`", "-"));
        }

        // Document Cash Discount Taken
        if (modifiedAPInvHed.DocCashDiscTaken != originalAPInvHed.DocCashDiscTaken)
        {
            changes.Add("APInvHed.DocCashDiscTaken`"
                       + originalAPInvHed.DocCashDiscTaken.ToString()
                       + "`"
                       + modifiedAPInvHed.DocCashDiscTaken.ToString().Replace("`", "-"));
        }

        // Rpt1 Cash Discount Taken
        if (modifiedAPInvHed.Rpt1CashDiscTaken != originalAPInvHed.Rpt1CashDiscTaken)
        {
            changes.Add("APInvHed.Rpt1CashDiscTaken`"
                       + originalAPInvHed.Rpt1CashDiscTaken.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1CashDiscTaken.ToString().Replace("`", "-"));
        }

        // Rpt2 Cash Discount Taken
        if (modifiedAPInvHed.Rpt2CashDiscTaken != originalAPInvHed.Rpt2CashDiscTaken)
        {
            changes.Add("APInvHed.Rpt2CashDiscTaken`"
                       + originalAPInvHed.Rpt2CashDiscTaken.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2CashDiscTaken.ToString().Replace("`", "-"));
        }

        // Rpt3 Cash Discount Taken
        if (modifiedAPInvHed.Rpt3CashDiscTaken != originalAPInvHed.Rpt3CashDiscTaken)
        {
            changes.Add("APInvHed.Rpt3CashDiscTaken`"
                       + originalAPInvHed.Rpt3CashDiscTaken.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3CashDiscTaken.ToString().Replace("`", "-"));
        }

        // Tax Connect Status
        if (modifiedAPInvHed.TaxConnectStatus != originalAPInvHed.TaxConnectStatus)
        {
            changes.Add("APInvHed.TaxConnectStatus`"
                       + originalAPInvHed.TaxConnectStatus.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxConnectStatus.ToString().Replace("`", "-"));
        }

        // Tax Connect Last Update
        if (modifiedAPInvHed.TaxConnectLastUpdate != originalAPInvHed.TaxConnectLastUpdate)
        {
            changes.Add("APInvHed.TaxConnectLastUpdate`"
                       + originalAPInvHed.TaxConnectLastUpdate.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxConnectLastUpdate.ToString().Replace("`", "-"));
        }

        // Tax Connect Last Error
        if (modifiedAPInvHed.TaxConnectLastError != originalAPInvHed.TaxConnectLastError)
        {
            changes.Add("APInvHed.TaxConnectLastError`"
                       + originalAPInvHed.TaxConnectLastError.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxConnectLastError.ToString().Replace("`", "-"));
        }

        // Supplier Validation Status
        if (modifiedAPInvHed.SupplierValidationStatus != originalAPInvHed.SupplierValidationStatus)
        {
            changes.Add("APInvHed.SupplierValidationStatus`"
                       + originalAPInvHed.SupplierValidationStatus.ToString()
                       + "`"
                       + modifiedAPInvHed.SupplierValidationStatus.ToString().Replace("`", "-"));
        }

        // Supplier Validation Date
        if (modifiedAPInvHed.SupplierValidationDate != originalAPInvHed.SupplierValidationDate)
        {
            changes.Add("APInvHed.SupplierValidationDate`"
                       + originalAPInvHed.SupplierValidationDate.ToString()
                       + "`"
                       + modifiedAPInvHed.SupplierValidationDate.ToString().Replace("`", "-"));
        }

        // Supplier Validation Error
        if (modifiedAPInvHed.SupplierValidationError != originalAPInvHed.SupplierValidationError)
        {
            changes.Add("APInvHed.SupplierValidationError`"
                       + originalAPInvHed.SupplierValidationError.ToString()
                       + "`"
                       + modifiedAPInvHed.SupplierValidationError.ToString().Replace("`", "-"));
        }

        // Base Currency Code
        if (modifiedAPInvHed.BaseCurrCode != originalAPInvHed.BaseCurrCode)
        {
            changes.Add("APInvHed.BaseCurrCode`"
                       + originalAPInvHed.BaseCurrCode.ToString()
                       + "`"
                       + modifiedAPInvHed.BaseCurrCode.ToString().Replace("`", "-"));
        }

        // Base Currency Symbol
        if (modifiedAPInvHed.BaseCurrSymbol != originalAPInvHed.BaseCurrSymbol)
        {
            changes.Add("APInvHed.BaseCurrSymbol`"
                       + originalAPInvHed.BaseCurrSymbol.ToString()
                       + "`"
                       + modifiedAPInvHed.BaseCurrSymbol.ToString().Replace("`", "-"));
        }

        // Document Currency Symbol
        if (modifiedAPInvHed.DocCurrSymbol != originalAPInvHed.DocCurrSymbol)
        {
            changes.Add("APInvHed.DocCurrSymbol`"
                       + originalAPInvHed.DocCurrSymbol.ToString()
                       + "`"
                       + modifiedAPInvHed.DocCurrSymbol.ToString().Replace("`", "-"));
        }

        // Reporting Currency 1 Code
        if (modifiedAPInvHed.Rpt1CurrCode != originalAPInvHed.Rpt1CurrCode)
        {
            changes.Add("APInvHed.Rpt1CurrCode`"
                       + originalAPInvHed.Rpt1CurrCode.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1CurrCode.ToString().Replace("`", "-"));
        }

        // Reporting Currency 1 Symbol
        if (modifiedAPInvHed.Rpt1CurrSymbol != originalAPInvHed.Rpt1CurrSymbol)
        {
            changes.Add("APInvHed.Rpt1CurrSymbol`"
                       + originalAPInvHed.Rpt1CurrSymbol.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1CurrSymbol.ToString().Replace("`", "-"));
        }

        // Reporting Currency 2 Code
        if (modifiedAPInvHed.Rpt2CurrCode != originalAPInvHed.Rpt2CurrCode)
        {
            changes.Add("APInvHed.Rpt2CurrCode`"
                       + originalAPInvHed.Rpt2CurrCode.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2CurrCode.ToString().Replace("`", "-"));
        }

        // Reporting Currency 2 Symbol
        if (modifiedAPInvHed.Rpt2CurrSymbol != originalAPInvHed.Rpt2CurrSymbol)
        {
            changes.Add("APInvHed.Rpt2CurrSymbol`"
                       + originalAPInvHed.Rpt2CurrSymbol.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2CurrSymbol.ToString().Replace("`", "-"));
        }

        // Reporting Currency 3 Code
        if (modifiedAPInvHed.Rpt3CurrCode != originalAPInvHed.Rpt3CurrCode)
        {
            changes.Add("APInvHed.Rpt3CurrCode`"
                       + originalAPInvHed.Rpt3CurrCode.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3CurrCode.ToString().Replace("`", "-"));
        }

        // Reporting Currency 3 Symbol
        if (modifiedAPInvHed.Rpt3CurrSymbol != originalAPInvHed.Rpt3CurrSymbol)
        {
            changes.Add("APInvHed.Rpt3CurrSymbol`"
                       + originalAPInvHed.Rpt3CurrSymbol.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3CurrSymbol.ToString().Replace("`", "-"));
        }

        // Screen Document Discount Amount
        if (modifiedAPInvHed.ScrDocDiscountAmt != originalAPInvHed.ScrDocDiscountAmt)
        {
            changes.Add("APInvHed.ScrDocDiscountAmt`"
                       + originalAPInvHed.ScrDocDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocDiscountAmt.ToString().Replace("`", "-"));
        }

        // Screen Document Header Miscellaneous Charge Total
        if (modifiedAPInvHed.ScrDocHdrMiscChrgTotal != originalAPInvHed.ScrDocHdrMiscChrgTotal)
        {
            changes.Add("APInvHed.ScrDocHdrMiscChrgTotal`"
                       + originalAPInvHed.ScrDocHdrMiscChrgTotal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocHdrMiscChrgTotal.ToString().Replace("`", "-"));
        }

        // Screen Document Invoice Line Total
        if (modifiedAPInvHed.ScrDocInvLineTotal != originalAPInvHed.ScrDocInvLineTotal)
        {
            changes.Add("APInvHed.ScrDocInvLineTotal`"
                       + originalAPInvHed.ScrDocInvLineTotal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocInvLineTotal.ToString().Replace("`", "-"));
        }

        // Screen Document Invoice Balance
        if (modifiedAPInvHed.ScrDocInvoiceBal != originalAPInvHed.ScrDocInvoiceBal)
        {
            changes.Add("APInvHed.ScrDocInvoiceBal`"
                       + originalAPInvHed.ScrDocInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocInvoiceBal.ToString().Replace("`", "-"));
        }

        // Screen Document Rounding
        if (modifiedAPInvHed.ScrDocRounding != originalAPInvHed.ScrDocRounding)
        {
            changes.Add("APInvHed.ScrDocRounding`"
                       + originalAPInvHed.ScrDocRounding.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocRounding.ToString().Replace("`", "-"));
        }

        // Screen Document Tax Amount
        if (modifiedAPInvHed.ScrDocTaxAmt != originalAPInvHed.ScrDocTaxAmt)
        {
            changes.Add("APInvHed.ScrDocTaxAmt`"
                       + originalAPInvHed.ScrDocTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocTaxAmt.ToString().Replace("`", "-"));
        }

        // Screen Document Total BOE Withholding
        if (modifiedAPInvHed.ScrDocTotBOEWithholding != originalAPInvHed.ScrDocTotBOEWithholding)
        {
            changes.Add("APInvHed.ScrDocTotBOEWithholding`"
                       + originalAPInvHed.ScrDocTotBOEWithholding.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocTotBOEWithholding.ToString().Replace("`", "-"));
        }

        // Screen Document Total Deductible Tax Amount
        if (modifiedAPInvHed.ScrDocTotDedTaxAmt != originalAPInvHed.ScrDocTotDedTaxAmt)
        {
            changes.Add("APInvHed.ScrDocTotDedTaxAmt`"
                       + originalAPInvHed.ScrDocTotDedTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocTotDedTaxAmt.ToString().Replace("`", "-"));
        }

        // Screen Document Total Invoice Amount
        if (modifiedAPInvHed.ScrDocTotInvoiceAmt != originalAPInvHed.ScrDocTotInvoiceAmt)
        {
            changes.Add("APInvHed.ScrDocTotInvoiceAmt`"
                       + originalAPInvHed.ScrDocTotInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocTotInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Screen Document Total Reportable Amount
        if (modifiedAPInvHed.ScrDocTotReportableAmt != originalAPInvHed.ScrDocTotReportableAmt)
        {
            changes.Add("APInvHed.ScrDocTotReportableAmt`"
                       + originalAPInvHed.ScrDocTotReportableAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocTotReportableAmt.ToString().Replace("`", "-"));
        }

        // Screen Document Total Self Amount
        if (modifiedAPInvHed.ScrDocTotSelfAmt != originalAPInvHed.ScrDocTotSelfAmt)
        {
            changes.Add("APInvHed.ScrDocTotSelfAmt`"
                       + originalAPInvHed.ScrDocTotSelfAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocTotSelfAmt.ToString().Replace("`", "-"));
        }

        // Screen Document Total Taxable Amount
        if (modifiedAPInvHed.ScrDocTotTaxableAmt != originalAPInvHed.ScrDocTotTaxableAmt)
        {
            changes.Add("APInvHed.ScrDocTotTaxableAmt`"
                       + originalAPInvHed.ScrDocTotTaxableAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocTotTaxableAmt.ToString().Replace("`", "-"));
        }

        // Screen Document Total Withholding Amount
        if (modifiedAPInvHed.ScrDocTotWithholdingAmt != originalAPInvHed.ScrDocTotWithholdingAmt)
        {
            changes.Add("APInvHed.ScrDocTotWithholdingAmt`"
                       + originalAPInvHed.ScrDocTotWithholdingAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocTotWithholdingAmt.ToString().Replace("`", "-"));
        }

        // Screen Document Unposted Balance
        if (modifiedAPInvHed.ScrDocUnpostedBal != originalAPInvHed.ScrDocUnpostedBal)
        {
            changes.Add("APInvHed.ScrDocUnpostedBal`"
                       + originalAPInvHed.ScrDocUnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrDocUnpostedBal.ToString().Replace("`", "-"));
        }

        // Screen Header Expense Total
        if (modifiedAPInvHed.ScrHdrExpTotal != originalAPInvHed.ScrHdrExpTotal)
        {
            changes.Add("APInvHed.ScrHdrExpTotal`"
                       + originalAPInvHed.ScrHdrExpTotal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrHdrExpTotal.ToString().Replace("`", "-"));
        }

        // Screen Header Miscellaneous Charge Total
        if (modifiedAPInvHed.ScrHdrMiscChrgTotal != originalAPInvHed.ScrHdrMiscChrgTotal)
        {
            changes.Add("APInvHed.ScrHdrMiscChrgTotal`"
                       + originalAPInvHed.ScrHdrMiscChrgTotal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrHdrMiscChrgTotal.ToString().Replace("`", "-"));
        }

        // Screen Invoice Line Total
        if (modifiedAPInvHed.ScrInvLineTotal != originalAPInvHed.ScrInvLineTotal)
        {
            changes.Add("APInvHed.ScrInvLineTotal`"
                       + originalAPInvHed.ScrInvLineTotal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrInvLineTotal.ToString().Replace("`", "-"));
        }

        // Screen Invoice Balance
        if (modifiedAPInvHed.ScrInvoiceBal != originalAPInvHed.ScrInvoiceBal)
        {
            changes.Add("APInvHed.ScrInvoiceBal`"
                       + originalAPInvHed.ScrInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrInvoiceBal.ToString().Replace("`", "-"));
        }

        // Screen Invoice Reference
        if (modifiedAPInvHed.ScrInvoiceRef != originalAPInvHed.ScrInvoiceRef)
        {
            changes.Add("APInvHed.ScrInvoiceRef`"
                       + originalAPInvHed.ScrInvoiceRef.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrInvoiceRef.ToString().Replace("`", "-"));
        }

        // Screen LAC Document Tax Amount
        if (modifiedAPInvHed.ScrLACDocTaxAmt != originalAPInvHed.ScrLACDocTaxAmt)
        {
            changes.Add("APInvHed.ScrLACDocTaxAmt`"
                       + originalAPInvHed.ScrLACDocTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrLACDocTaxAmt.ToString().Replace("`", "-"));
        }

        // Screen LAC Tax Amount
        if (modifiedAPInvHed.ScrLACTaxAmt != originalAPInvHed.ScrLACTaxAmt)
        {
            changes.Add("APInvHed.ScrLACTaxAmt`"
                       + originalAPInvHed.ScrLACTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrLACTaxAmt.ToString().Replace("`", "-"));
        }

        // Screen Rounding
        if (modifiedAPInvHed.ScrRounding != originalAPInvHed.ScrRounding)
        {
            changes.Add("APInvHed.ScrRounding`"
                       + originalAPInvHed.ScrRounding.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrRounding.ToString().Replace("`", "-"));
        }

        // Screen Total BOE Withholding
        if (modifiedAPInvHed.ScrTotBOEWithholding != originalAPInvHed.ScrTotBOEWithholding)
        {
            changes.Add("APInvHed.ScrTotBOEWithholding`"
                       + originalAPInvHed.ScrTotBOEWithholding.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTotBOEWithholding.ToString().Replace("`", "-"));
        }

        // Screen Total Deductible Tax Amount
        if (modifiedAPInvHed.ScrTotDedTaxAmt != originalAPInvHed.ScrTotDedTaxAmt)
        {
            changes.Add("APInvHed.ScrTotDedTaxAmt`"
                       + originalAPInvHed.ScrTotDedTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTotDedTaxAmt.ToString().Replace("`", "-"));
        }

        // Screen Total Invoice Amount
        if (modifiedAPInvHed.ScrTotInvoiceAmt != originalAPInvHed.ScrTotInvoiceAmt)
        {
            changes.Add("APInvHed.ScrTotInvoiceAmt`"
                       + originalAPInvHed.ScrTotInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTotInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Screen Total Reportable Amount
        if (modifiedAPInvHed.ScrTotReportableAmt != originalAPInvHed.ScrTotReportableAmt)
        {
            changes.Add("APInvHed.ScrTotReportableAmt`"
                       + originalAPInvHed.ScrTotReportableAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTotReportableAmt.ToString().Replace("`", "-"));
        }

        // Screen Total Self Amount
        if (modifiedAPInvHed.ScrTotSelfAmt != originalAPInvHed.ScrTotSelfAmt)
        {
            changes.Add("APInvHed.ScrTotSelfAmt`"
                       + originalAPInvHed.ScrTotSelfAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTotSelfAmt.ToString().Replace("`", "-"));
        }

        // Screen Total Taxable Amount
        if (modifiedAPInvHed.ScrTotTaxableAmt != originalAPInvHed.ScrTotTaxableAmt)
        {
            changes.Add("APInvHed.ScrTotTaxableAmt`"
                       + originalAPInvHed.ScrTotTaxableAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTotTaxableAmt.ToString().Replace("`", "-"));
        }

        // Screen Total Withholding Amount
        if (modifiedAPInvHed.ScrTotWithholdingAmt != originalAPInvHed.ScrTotWithholdingAmt)
        {
            changes.Add("APInvHed.ScrTotWithholdingAmt`"
                       + originalAPInvHed.ScrTotWithholdingAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrTotWithholdingAmt.ToString().Replace("`", "-"));
        }

        // Screen Unposted Balance
        if (modifiedAPInvHed.ScrUnpostedBal != originalAPInvHed.ScrUnpostedBal)
        {
            changes.Add("APInvHed.ScrUnpostedBal`"
                       + originalAPInvHed.ScrUnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.ScrUnpostedBal.ToString().Replace("`", "-"));
        }

        // Skip Recurring
        if (modifiedAPInvHed.SkipRecurring != originalAPInvHed.SkipRecurring)
        {
            changes.Add("APInvHed.SkipRecurring`"
                       + originalAPInvHed.SkipRecurring.ToString()
                       + "`"
                       + modifiedAPInvHed.SkipRecurring.ToString().Replace("`", "-"));
        }

        // Source Invoice Number
        if (modifiedAPInvHed.SourceInvoiceNum != originalAPInvHed.SourceInvoiceNum)
        {
            changes.Add("APInvHed.SourceInvoiceNum`"
                       + originalAPInvHed.SourceInvoiceNum.ToString()
                       + "`"
                       + modifiedAPInvHed.SourceInvoiceNum.ToString().Replace("`", "-"));
        }

        // Source Last Date
        if (modifiedAPInvHed.SourceLastDate != originalAPInvHed.SourceLastDate)
        {
            changes.Add("APInvHed.SourceLastDate`"
                       + originalAPInvHed.SourceLastDate.ToString()
                       + "`"
                       + modifiedAPInvHed.SourceLastDate.ToString().Replace("`", "-"));
        }

        // Source Recurring Balance
        if (modifiedAPInvHed.SourceRecurBalance != originalAPInvHed.SourceRecurBalance)
        {
            changes.Add("APInvHed.SourceRecurBalance`"
                       + originalAPInvHed.SourceRecurBalance.ToString()
                       + "`"
                       + modifiedAPInvHed.SourceRecurBalance.ToString().Replace("`", "-"));
        }

        // Swift Code
        if (modifiedAPInvHed.SwiftCode != originalAPInvHed.SwiftCode)
        {
            changes.Add("APInvHed.SwiftCode`"
                       + originalAPInvHed.SwiftCode.ToString()
                       + "`"
                       + modifiedAPInvHed.SwiftCode.ToString().Replace("`", "-"));
        }

        // System Transaction Type
        if (modifiedAPInvHed.SystemTranType != originalAPInvHed.SystemTranType)
        {
            changes.Add("APInvHed.SystemTranType`"
                       + originalAPInvHed.SystemTranType.ToString()
                       + "`"
                       + modifiedAPInvHed.SystemTranType.ToString().Replace("`", "-"));
        }

        // Tax Exchange Rate
        if (modifiedAPInvHed.TaxExchangeRate != originalAPInvHed.TaxExchangeRate)
        {
            changes.Add("APInvHed.TaxExchangeRate`"
                       + originalAPInvHed.TaxExchangeRate.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxExchangeRate.ToString().Replace("`", "-"));
        }

        // Tax Lines Exist
        if (modifiedAPInvHed.TaxLinesExist != originalAPInvHed.TaxLinesExist)
        {
            changes.Add("APInvHed.TaxLinesExist`"
                       + originalAPInvHed.TaxLinesExist.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxLinesExist.ToString().Replace("`", "-"));
        }

        // Tax Rate Lines Exist
        if (modifiedAPInvHed.TaxRateLinesExist != originalAPInvHed.TaxRateLinesExist)
        {
            changes.Add("APInvHed.TaxRateLinesExist`"
                       + originalAPInvHed.TaxRateLinesExist.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxRateLinesExist.ToString().Replace("`", "-"));
        }

        // Total Instance Number
        if (modifiedAPInvHed.TotalInstanceNum != originalAPInvHed.TotalInstanceNum)
        {
            changes.Add("APInvHed.TotalInstanceNum`"
                       + originalAPInvHed.TotalInstanceNum.ToString()
                       + "`"
                       + modifiedAPInvHed.TotalInstanceNum.ToString().Replace("`", "-"));
        }

        // Transaction Document Type Description
        if (modifiedAPInvHed.TranDocTypeDescription != originalAPInvHed.TranDocTypeDescription)
        {
            changes.Add("APInvHed.TranDocTypeDescription`"
                       + originalAPInvHed.TranDocTypeDescription.ToString()
                       + "`"
                       + modifiedAPInvHed.TranDocTypeDescription.ToString().Replace("`", "-"));
        }

        // Transaction Apply Date
        if (modifiedAPInvHed.TransApplyDate != originalAPInvHed.TransApplyDate)
        {
            changes.Add("APInvHed.TransApplyDate`"
                       + originalAPInvHed.TransApplyDate.ToString()
                       + "`"
                       + modifiedAPInvHed.TransApplyDate.ToString().Replace("`", "-"));
        }

        // Use Tax Rate
        if (modifiedAPInvHed.UseTaxRate != originalAPInvHed.UseTaxRate)
        {
            changes.Add("APInvHed.UseTaxRate`"
                       + originalAPInvHed.UseTaxRate.ToString()
                       + "`"
                       + modifiedAPInvHed.UseTaxRate.ToString().Replace("`", "-"));
        }

        // Vendor Inactive
        if (modifiedAPInvHed.VendorInactive != originalAPInvHed.VendorInactive)
        {
            changes.Add("APInvHed.VendorInactive`"
                       + originalAPInvHed.VendorInactive.ToString()
                       + "`"
                       + modifiedAPInvHed.VendorInactive.ToString().Replace("`", "-"));
        }

        // Vendor Pay Hold
        if (modifiedAPInvHed.VendorPayHold != originalAPInvHed.VendorPayHold)
        {
            changes.Add("APInvHed.VendorPayHold`"
                       + originalAPInvHed.VendorPayHold.ToString()
                       + "`"
                       + modifiedAPInvHed.VendorPayHold.ToString().Replace("`", "-"));
        }

        // VN Date Received
        if (modifiedAPInvHed.VNDateReceived != originalAPInvHed.VNDateReceived)
        {
            changes.Add("APInvHed.VNDateReceived`"
                       + originalAPInvHed.VNDateReceived.ToString()
                       + "`"
                       + modifiedAPInvHed.VNDateReceived.ToString().Replace("`", "-"));
        }

        // VN Invoice Type
        if (modifiedAPInvHed.VNInvoiceType != originalAPInvHed.VNInvoiceType)
        {
            changes.Add("APInvHed.VNInvoiceType`"
                       + originalAPInvHed.VNInvoiceType.ToString()
                       + "`"
                       + modifiedAPInvHed.VNInvoiceType.ToString().Replace("`", "-"));
        }

        // Exchange Rate Label
        if (modifiedAPInvHed.XRateLabel != originalAPInvHed.XRateLabel)
        {
            changes.Add("APInvHed.XRateLabel`"
                       + originalAPInvHed.XRateLabel.ToString()
                       + "`"
                       + modifiedAPInvHed.XRateLabel.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Document Discount Amount
        if (modifiedAPInvHed.Rpt1ScrDocDiscountAmt != originalAPInvHed.Rpt1ScrDocDiscountAmt)
        {
            changes.Add("APInvHed.Rpt1ScrDocDiscountAmt`"
                       + originalAPInvHed.Rpt1ScrDocDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrDocDiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Document Discount Amount
        if (modifiedAPInvHed.Rpt2ScrDocDiscountAmt != originalAPInvHed.Rpt2ScrDocDiscountAmt)
        {
            changes.Add("APInvHed.Rpt2ScrDocDiscountAmt`"
                       + originalAPInvHed.Rpt2ScrDocDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrDocDiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Document Discount Amount
        if (modifiedAPInvHed.Rpt3ScrDocDiscountAmt != originalAPInvHed.Rpt3ScrDocDiscountAmt)
        {
            changes.Add("APInvHed.Rpt3ScrDocDiscountAmt`"
                       + originalAPInvHed.Rpt3ScrDocDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrDocDiscountAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Document Invoice Amount
        if (modifiedAPInvHed.Rpt1ScrDocInvoiceAmt != originalAPInvHed.Rpt1ScrDocInvoiceAmt)
        {
            changes.Add("APInvHed.Rpt1ScrDocInvoiceAmt`"
                       + originalAPInvHed.Rpt1ScrDocInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrDocInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Document Invoice Amount
        if (modifiedAPInvHed.Rpt2ScrDocInvoiceAmt != originalAPInvHed.Rpt2ScrDocInvoiceAmt)
        {
            changes.Add("APInvHed.Rpt2ScrDocInvoiceAmt`"
                       + originalAPInvHed.Rpt2ScrDocInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrDocInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Document Invoice Amount
        if (modifiedAPInvHed.Rpt3ScrDocInvoiceAmt != originalAPInvHed.Rpt3ScrDocInvoiceAmt)
        {
            changes.Add("APInvHed.Rpt3ScrDocInvoiceAmt`"
                       + originalAPInvHed.Rpt3ScrDocInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrDocInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen Document Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt1ScrDocInvoiceVendorAmt != originalAPInvHed.Rpt1ScrDocInvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt1ScrDocInvoiceVendorAmt`"
                       + originalAPInvHed.Rpt1ScrDocInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrDocInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen Document Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt2ScrDocInvoiceVendorAmt != originalAPInvHed.Rpt2ScrDocInvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt2ScrDocInvoiceVendorAmt`"
                       + originalAPInvHed.Rpt2ScrDocInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrDocInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Document Invoice Vendor Amount
        if (modifiedAPInvHed.Rpt3ScrDocInvoiceVendorAmt != originalAPInvHed.Rpt3ScrDocInvoiceVendorAmt)
        {
            changes.Add("APInvHed.Rpt3ScrDocInvoiceVendorAmt`"
                       + originalAPInvHed.Rpt3ScrDocInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrDocInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Screen LAC Tax Amount
        if (modifiedAPInvHed.Rpt1ScrLACTaxAmt != originalAPInvHed.Rpt1ScrLACTaxAmt)
        {
            changes.Add("APInvHed.Rpt1ScrLACTaxAmt`"
                       + originalAPInvHed.Rpt1ScrLACTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt1ScrLACTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Screen LAC Tax Amount
        if (modifiedAPInvHed.Rpt2ScrLACTaxAmt != originalAPInvHed.Rpt2ScrLACTaxAmt)
        {
            changes.Add("APInvHed.Rpt2ScrLACTaxAmt`"
                       + originalAPInvHed.Rpt2ScrLACTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt2ScrLACTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen LAC Tax Amount
        if (modifiedAPInvHed.Rpt3ScrLACTaxAmt != originalAPInvHed.Rpt3ScrLACTaxAmt)
        {
            changes.Add("APInvHed.Rpt3ScrLACTaxAmt`"
                       + originalAPInvHed.Rpt3ScrLACTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrLACTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Rounding
        if (modifiedAPInvHed.Rpt3ScrRounding != originalAPInvHed.Rpt3ScrRounding)
        {
            changes.Add("APInvHed.Rpt3ScrRounding`"
                       + originalAPInvHed.Rpt3ScrRounding.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrRounding.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Tax Amount
        if (modifiedAPInvHed.Rpt3ScrTaxAmt != originalAPInvHed.Rpt3ScrTaxAmt)
        {
            changes.Add("APInvHed.Rpt3ScrTaxAmt`"
                       + originalAPInvHed.Rpt3ScrTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Total BOE Withholding
        if (modifiedAPInvHed.Rpt3ScrTotBOEWithholding != originalAPInvHed.Rpt3ScrTotBOEWithholding)
        {
            changes.Add("APInvHed.Rpt3ScrTotBOEWithholding`"
                       + originalAPInvHed.Rpt3ScrTotBOEWithholding.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTotBOEWithholding.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Total Deductible Tax Amount
        if (modifiedAPInvHed.Rpt3ScrTotDedTaxAmt != originalAPInvHed.Rpt3ScrTotDedTaxAmt)
        {
            changes.Add("APInvHed.Rpt3ScrTotDedTaxAmt`"
                       + originalAPInvHed.Rpt3ScrTotDedTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTotDedTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Total Invoice Amount
        if (modifiedAPInvHed.Rpt3ScrTotInvoiceAmt != originalAPInvHed.Rpt3ScrTotInvoiceAmt)
        {
            changes.Add("APInvHed.Rpt3ScrTotInvoiceAmt`"
                       + originalAPInvHed.Rpt3ScrTotInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTotInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Total Reportable Amount
        if (modifiedAPInvHed.Rpt3ScrTotReportableAmt != originalAPInvHed.Rpt3ScrTotReportableAmt)
        {
            changes.Add("APInvHed.Rpt3ScrTotReportableAmt`"
                       + originalAPInvHed.Rpt3ScrTotReportableAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTotReportableAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Total Self Amount
        if (modifiedAPInvHed.Rpt3ScrTotSelfAmt != originalAPInvHed.Rpt3ScrTotSelfAmt)
        {
            changes.Add("APInvHed.Rpt3ScrTotSelfAmt`"
                       + originalAPInvHed.Rpt3ScrTotSelfAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTotSelfAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Total Taxable Amount
        if (modifiedAPInvHed.Rpt3ScrTotTaxableAmt != originalAPInvHed.Rpt3ScrTotTaxableAmt)
        {
            changes.Add("APInvHed.Rpt3ScrTotTaxableAmt`"
                       + originalAPInvHed.Rpt3ScrTotTaxableAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTotTaxableAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Screen Total Withholding Amount
        if (modifiedAPInvHed.Rpt3ScrTotWithholdingAmt != originalAPInvHed.Rpt3ScrTotWithholdingAmt)
        {
            changes.Add("APInvHed.Rpt3ScrTotWithholdingAmt`"
                       + originalAPInvHed.Rpt3ScrTotWithholdingAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.Rpt3ScrTotWithholdingAmt.ToString().Replace("`", "-"));
        }


































































    }
}

// =================================================================
// APInvDtl (Detail) Change Tracking
// =================================================================

// Check if APInvDtl data exists
if (ds.APInvDtl != null && ds.APInvDtl.Count > 0)
{
    // Process each APInvDtl record
    for (int i = 0; i < ds.APInvDtl.Count; i++)
    {
        var modifiedDtl = ds.APInvDtl[i];
        string dtlInvoiceNum = modifiedDtl.InvoiceNum;
        int dtlVendorNum = modifiedDtl.VendorNum;
        int invoiceLine = modifiedDtl.InvoiceLine;

        // Check if the detail row has been deleted
        if (modifiedDtl.RowMod == "D")
        {
            changes.Add($"APInvDtl deleted: Line {invoiceLine}");
            callFunc = true;
            continue;
        }

        // Get the original detail record from database
        var originalDtl = (from dbDtl in Db.APInvDtl
                          where dbDtl.Company == companyID
                             && dbDtl.VendorNum == dtlVendorNum
                             && dbDtl.InvoiceNum == dtlInvoiceNum
                             && dbDtl.InvoiceLine == invoiceLine
                          select dbDtl).FirstOrDefault();

        // Handle new detail creation
        if (originalDtl == null)
        {
            changes.Add($"New AP invoice detail created: Line {invoiceLine}");
            callFunc = true;
            continue;
        }

        // =================================================================
        // APInvDtl Field Comparisons
        // =================================================================

        // Company
        if (modifiedDtl.Company != originalDtl.Company)
        {
            changes.Add($"APInvDtl.Company[Line {invoiceLine}]`"
                       + originalDtl.Company.ToString()
                       + "`"
                       + modifiedDtl.Company.ToString().Replace("`", "-"));
        }

        // Vendor Number
        if (modifiedDtl.VendorNum != originalDtl.VendorNum)
        {
            changes.Add($"APInvDtl.VendorNum[Line {invoiceLine}]`"
                       + originalDtl.VendorNum.ToString()
                       + "`"
                       + modifiedDtl.VendorNum.ToString().Replace("`", "-"));
        }

        // Invoice Number
        if (modifiedDtl.InvoiceNum != originalDtl.InvoiceNum)
        {
            changes.Add($"APInvDtl.InvoiceNum[Line {invoiceLine}]`"
                       + originalDtl.InvoiceNum.ToString()
                       + "`"
                       + modifiedDtl.InvoiceNum.ToString().Replace("`", "-"));
        }

        // Invoice Line
        if (modifiedDtl.InvoiceLine != originalDtl.InvoiceLine)
        {
            changes.Add($"APInvDtl.InvoiceLine[Line {invoiceLine}]`"
                       + originalDtl.InvoiceLine.ToString()
                       + "`"
                       + modifiedDtl.InvoiceLine.ToString().Replace("`", "-"));
        }

        // Line Type
        if (modifiedDtl.LineType != originalDtl.LineType)
        {
            changes.Add($"APInvDtl.LineType[Line {invoiceLine}]`"
                       + originalDtl.LineType.ToString()
                       + "`"
                       + modifiedDtl.LineType.ToString().Replace("`", "-"));
        }

        // Unit Cost
        if (modifiedDtl.UnitCost != originalDtl.UnitCost)
        {
            changes.Add($"APInvDtl.UnitCost[Line {invoiceLine}]`"
                       + originalDtl.UnitCost.ToString()
                       + "`"
                       + modifiedDtl.UnitCost.ToString().Replace("`", "-"));
        }

        // Document Unit Cost
        if (modifiedDtl.DocUnitCost != originalDtl.DocUnitCost)
        {
            changes.Add($"APInvDtl.DocUnitCost[Line {invoiceLine}]`"
                       + originalDtl.DocUnitCost.ToString()
                       + "`"
                       + modifiedDtl.DocUnitCost.ToString().Replace("`", "-"));
        }

        // Part Number
        if (modifiedDtl.PartNum != originalDtl.PartNum)
        {
            changes.Add($"APInvDtl.PartNum[Line {invoiceLine}]`"
                       + originalDtl.PartNum.ToString()
                       + "`"
                       + modifiedDtl.PartNum.ToString().Replace("`", "-"));
        }

        // PO Number
        if (modifiedDtl.PONum != originalDtl.PONum)
        {
            changes.Add($"APInvDtl.PONum[Line {invoiceLine}]`"
                       + originalDtl.PONum.ToString()
                       + "`"
                       + modifiedDtl.PONum.ToString().Replace("`", "-"));
        }

        // PO Line
        if (modifiedDtl.POLine != originalDtl.POLine)
        {
            changes.Add($"APInvDtl.POLine[Line {invoiceLine}]`"
                       + originalDtl.POLine.ToString()
                       + "`"
                       + modifiedDtl.POLine.ToString().Replace("`", "-"));
        }

        // PO Release Number
        if (modifiedDtl.PORelNum != originalDtl.PORelNum)
        {
            changes.Add($"APInvDtl.PORelNum[Line {invoiceLine}]`"
                       + originalDtl.PORelNum.ToString()
                       + "`"
                       + modifiedDtl.PORelNum.ToString().Replace("`", "-"));
        }

        // Description
        if (modifiedDtl.Description != originalDtl.Description)
        {
            changes.Add($"APInvDtl.Description[Line {invoiceLine}]`"
                       + originalDtl.Description.ToString()
                       + "`"
                       + modifiedDtl.Description.ToString().Replace("`", "-"));
        }

        // Job Number
        if (modifiedDtl.JobNum != originalDtl.JobNum)
        {
            changes.Add($"APInvDtl.JobNum[Line {invoiceLine}]`"
                       + originalDtl.JobNum.ToString()
                       + "`"
                       + modifiedDtl.JobNum.ToString().Replace("`", "-"));
        }

        // Assembly Sequence
        if (modifiedDtl.AssemblySeq != originalDtl.AssemblySeq)
        {
            changes.Add($"APInvDtl.AssemblySeq[Line {invoiceLine}]`"
                       + originalDtl.AssemblySeq.ToString()
                       + "`"
                       + modifiedDtl.AssemblySeq.ToString().Replace("`", "-"));
        }

        // Job Sequence Type
        if (modifiedDtl.JobSeqType != originalDtl.JobSeqType)
        {
            changes.Add($"APInvDtl.JobSeqType[Line {invoiceLine}]`"
                       + originalDtl.JobSeqType.ToString()
                       + "`"
                       + modifiedDtl.JobSeqType.ToString().Replace("`", "-"));
        }

        // Job Sequence
        if (modifiedDtl.JobSeq != originalDtl.JobSeq)
        {
            changes.Add($"APInvDtl.JobSeq[Line {invoiceLine}]`"
                       + originalDtl.JobSeq.ToString()
                       + "`"
                       + modifiedDtl.JobSeq.ToString().Replace("`", "-"));
        }

        // Purchase Point
        if (modifiedDtl.PurPoint != originalDtl.PurPoint)
        {
            changes.Add($"APInvDtl.PurPoint[Line {invoiceLine}]`"
                       + originalDtl.PurPoint.ToString()
                       + "`"
                       + modifiedDtl.PurPoint.ToString().Replace("`", "-"));
        }

        // Pack Slip
        if (modifiedDtl.PackSlip != originalDtl.PackSlip)
        {
            changes.Add($"APInvDtl.PackSlip[Line {invoiceLine}]`"
                       + originalDtl.PackSlip.ToString()
                       + "`"
                       + modifiedDtl.PackSlip.ToString().Replace("`", "-"));
        }

        // Pack Line
        if (modifiedDtl.PackLine != originalDtl.PackLine)
        {
            changes.Add($"APInvDtl.PackLine[Line {invoiceLine}]`"
                       + originalDtl.PackLine.ToString()
                       + "`"
                       + modifiedDtl.PackLine.ToString().Replace("`", "-"));
        }

        // Vendor Quantity
        if (modifiedDtl.VendorQty != originalDtl.VendorQty)
        {
            changes.Add($"APInvDtl.VendorQty[Line {invoiceLine}]`"
                       + originalDtl.VendorQty.ToString()
                       + "`"
                       + modifiedDtl.VendorQty.ToString().Replace("`", "-"));
        }

        // Purchase Unit of Measure
        if (modifiedDtl.PUM != originalDtl.PUM)
        {
            changes.Add($"APInvDtl.PUM[Line {invoiceLine}]`"
                       + originalDtl.PUM.ToString()
                       + "`"
                       + modifiedDtl.PUM.ToString().Replace("`", "-"));
        }

        // Our Quantity
        if (modifiedDtl.OurQty != originalDtl.OurQty)
        {
            changes.Add($"APInvDtl.OurQty[Line {invoiceLine}]`"
                       + originalDtl.OurQty.ToString()
                       + "`"
                       + modifiedDtl.OurQty.ToString().Replace("`", "-"));
        }

        // Internal Unit of Measure
        if (modifiedDtl.IUM != originalDtl.IUM)
        {
            changes.Add($"APInvDtl.IUM[Line {invoiceLine}]`"
                       + originalDtl.IUM.ToString()
                       + "`"
                       + modifiedDtl.IUM.ToString().Replace("`", "-"));
        }

        // Cost Per Code
        if (modifiedDtl.CostPerCode != originalDtl.CostPerCode)
        {
            changes.Add($"APInvDtl.CostPerCode[Line {invoiceLine}]`"
                       + originalDtl.CostPerCode.ToString()
                       + "`"
                       + modifiedDtl.CostPerCode.ToString().Replace("`", "-"));
        }

        // Vendor Part Number
        if (modifiedDtl.VenPartNum != originalDtl.VenPartNum)
        {
            changes.Add($"APInvDtl.VenPartNum[Line {invoiceLine}]`"
                       + originalDtl.VenPartNum.ToString()
                       + "`"
                       + modifiedDtl.VenPartNum.ToString().Replace("`", "-"));
        }

        // Extended Cost
        if (modifiedDtl.ExtCost != originalDtl.ExtCost)
        {
            changes.Add($"APInvDtl.ExtCost[Line {invoiceLine}]`"
                       + originalDtl.ExtCost.ToString()
                       + "`"
                       + modifiedDtl.ExtCost.ToString().Replace("`", "-"));
        }

        // Document Extended Cost
        if (modifiedDtl.DocExtCost != originalDtl.DocExtCost)
        {
            changes.Add($"APInvDtl.DocExtCost[Line {invoiceLine}]`"
                       + originalDtl.DocExtCost.ToString()
                       + "`"
                       + modifiedDtl.DocExtCost.ToString().Replace("`", "-"));
        }

        // Total Miscellaneous Charge
        if (modifiedDtl.TotalMiscChrg != originalDtl.TotalMiscChrg)
        {
            changes.Add($"APInvDtl.TotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.TotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.TotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Document Total Miscellaneous Charge
        if (modifiedDtl.DocTotalMiscChrg != originalDtl.DocTotalMiscChrg)
        {
            changes.Add($"APInvDtl.DocTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.DocTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.DocTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Line Comment
        if (modifiedDtl.LineComment != originalDtl.LineComment)
        {
            changes.Add($"APInvDtl.LineComment[Line {invoiceLine}]`"
                       + originalDtl.LineComment.ToString()
                       + "`"
                       + modifiedDtl.LineComment.ToString().Replace("`", "-"));
        }

        // Tax Exempt
        if (modifiedDtl.TaxExempt != originalDtl.TaxExempt)
        {
            changes.Add($"APInvDtl.TaxExempt[Line {invoiceLine}]`"
                       + originalDtl.TaxExempt.ToString()
                       + "`"
                       + modifiedDtl.TaxExempt.ToString().Replace("`", "-"));
        }

        // Tax Category ID
        if (modifiedDtl.TaxCatID != originalDtl.TaxCatID)
        {
            changes.Add($"APInvDtl.TaxCatID[Line {invoiceLine}]`"
                       + originalDtl.TaxCatID.ToString()
                       + "`"
                       + modifiedDtl.TaxCatID.ToString().Replace("`", "-"));
        }

        // Match Date
        if (modifiedDtl.MatchDate != originalDtl.MatchDate)
        {
            changes.Add($"APInvDtl.MatchDate[Line {invoiceLine}]`"
                       + originalDtl.MatchDate.ToString()
                       + "`"
                       + modifiedDtl.MatchDate.ToString().Replace("`", "-"));
        }

        // Match Fiscal Year
        if (modifiedDtl.MatchFiscalYear != originalDtl.MatchFiscalYear)
        {
            changes.Add($"APInvDtl.MatchFiscalYear[Line {invoiceLine}]`"
                       + originalDtl.MatchFiscalYear.ToString()
                       + "`"
                       + modifiedDtl.MatchFiscalYear.ToString().Replace("`", "-"));
        }

        // Match Fiscal Period
        if (modifiedDtl.MatchFiscalPeriod != originalDtl.MatchFiscalPeriod)
        {
            changes.Add($"APInvDtl.MatchFiscalPeriod[Line {invoiceLine}]`"
                       + originalDtl.MatchFiscalPeriod.ToString()
                       + "`"
                       + modifiedDtl.MatchFiscalPeriod.ToString().Replace("`", "-"));
        }

        // Advance Pay Amount
        if (modifiedDtl.AdvancePayAmt != originalDtl.AdvancePayAmt)
        {
            changes.Add($"APInvDtl.AdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.AdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.AdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Document Advance Pay Amount
        if (modifiedDtl.DocAdvancePayAmt != originalDtl.DocAdvancePayAmt)
        {
            changes.Add($"APInvDtl.DocAdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.DocAdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.DocAdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Purchase Code
        if (modifiedDtl.PurchCode != originalDtl.PurchCode)
        {
            changes.Add($"APInvDtl.PurchCode[Line {invoiceLine}]`"
                       + originalDtl.PurchCode.ToString()
                       + "`"
                       + modifiedDtl.PurchCode.ToString().Replace("`", "-"));
        }

        // Line Discount Amount
        if (modifiedDtl.LineDiscAmt != originalDtl.LineDiscAmt)
        {
            changes.Add($"APInvDtl.LineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.LineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.LineDiscAmt.ToString().Replace("`", "-"));
        }

        // Document Line Discount Amount
        if (modifiedDtl.DocLineDiscAmt != originalDtl.DocLineDiscAmt)
        {
            changes.Add($"APInvDtl.DocLineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.DocLineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.DocLineDiscAmt.ToString().Replace("`", "-"));
        }

        // Global Company
        if (modifiedDtl.GlbCompany != originalDtl.GlbCompany)
        {
            changes.Add($"APInvDtl.GlbCompany[Line {invoiceLine}]`"
                       + originalDtl.GlbCompany.ToString()
                       + "`"
                       + modifiedDtl.GlbCompany.ToString().Replace("`", "-"));
        }

        // Global Vendor Number
        if (modifiedDtl.GlbVendorNum != originalDtl.GlbVendorNum)
        {
            changes.Add($"APInvDtl.GlbVendorNum[Line {invoiceLine}]`"
                       + originalDtl.GlbVendorNum.ToString()
                       + "`"
                       + modifiedDtl.GlbVendorNum.ToString().Replace("`", "-"));
        }

        // Global Invoice Number
        if (modifiedDtl.GlbInvoiceNum != originalDtl.GlbInvoiceNum)
        {
            changes.Add($"APInvDtl.GlbInvoiceNum[Line {invoiceLine}]`"
                       + originalDtl.GlbInvoiceNum.ToString()
                       + "`"
                       + modifiedDtl.GlbInvoiceNum.ToString().Replace("`", "-"));
        }

        // Global Invoice Line
        if (modifiedDtl.GlbInvoiceLine != originalDtl.GlbInvoiceLine)
        {
            changes.Add($"APInvDtl.GlbInvoiceLine[Line {invoiceLine}]`"
                       + originalDtl.GlbInvoiceLine.ToString()
                       + "`"
                       + modifiedDtl.GlbInvoiceLine.ToString().Replace("`", "-"));
        }

        // Advance Gain Loss
        if (modifiedDtl.AdvGainLoss != originalDtl.AdvGainLoss)
        {
            changes.Add($"APInvDtl.AdvGainLoss[Line {invoiceLine}]`"
                       + originalDtl.AdvGainLoss.ToString()
                       + "`"
                       + modifiedDtl.AdvGainLoss.ToString().Replace("`", "-"));
        }

        // Multi Company
        if (modifiedDtl.MultiCompany != originalDtl.MultiCompany)
        {
            changes.Add($"APInvDtl.MultiCompany[Line {invoiceLine}]`"
                       + originalDtl.MultiCompany.ToString()
                       + "`"
                       + modifiedDtl.MultiCompany.ToString().Replace("`", "-"));
        }

        // Reverse Charge Method
        if (modifiedDtl.RevChargeMethod != originalDtl.RevChargeMethod)
        {
            changes.Add($"APInvDtl.RevChargeMethod[Line {invoiceLine}]`"
                       + originalDtl.RevChargeMethod.ToString()
                       + "`"
                       + modifiedDtl.RevChargeMethod.ToString().Replace("`", "-"));
        }



        // Reverse Charge Applied
        if (modifiedDtl.RevChargeApplied != originalDtl.RevChargeApplied)
        {
            changes.Add($"APInvDtl.RevChargeApplied[Line {invoiceLine}]`"
                       + originalDtl.RevChargeApplied.ToString()
                       + "`"
                       + modifiedDtl.RevChargeApplied.ToString().Replace("`", "-"));
        }

        // Rpt1 Advance Pay Amount
        if (modifiedDtl.Rpt1AdvancePayAmt != originalDtl.Rpt1AdvancePayAmt)
        {
            changes.Add($"APInvDtl.Rpt1AdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt1AdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt1AdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Advance Pay Amount
        if (modifiedDtl.Rpt2AdvancePayAmt != originalDtl.Rpt2AdvancePayAmt)
        {
            changes.Add($"APInvDtl.Rpt2AdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt2AdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt2AdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Advance Pay Amount
        if (modifiedDtl.Rpt3AdvancePayAmt != originalDtl.Rpt3AdvancePayAmt)
        {
            changes.Add($"APInvDtl.Rpt3AdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt3AdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt3AdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Extended Cost
        if (modifiedDtl.Rpt1ExtCost != originalDtl.Rpt1ExtCost)
        {
            changes.Add($"APInvDtl.Rpt1ExtCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt1ExtCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt1ExtCost.ToString().Replace("`", "-"));
        }

        // Rpt2 Extended Cost
        if (modifiedDtl.Rpt2ExtCost != originalDtl.Rpt2ExtCost)
        {
            changes.Add($"APInvDtl.Rpt2ExtCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt2ExtCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt2ExtCost.ToString().Replace("`", "-"));
        }

        // Rpt3 Extended Cost
        if (modifiedDtl.Rpt3ExtCost != originalDtl.Rpt3ExtCost)
        {
            changes.Add($"APInvDtl.Rpt3ExtCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt3ExtCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt3ExtCost.ToString().Replace("`", "-"));
        }

        // Rpt1 Line Discount Amount
        if (modifiedDtl.Rpt1LineDiscAmt != originalDtl.Rpt1LineDiscAmt)
        {
            changes.Add($"APInvDtl.Rpt1LineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt1LineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt1LineDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Line Discount Amount
        if (modifiedDtl.Rpt2LineDiscAmt != originalDtl.Rpt2LineDiscAmt)
        {
            changes.Add($"APInvDtl.Rpt2LineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt2LineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt2LineDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Line Discount Amount
        if (modifiedDtl.Rpt3LineDiscAmt != originalDtl.Rpt3LineDiscAmt)
        {
            changes.Add($"APInvDtl.Rpt3LineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt3LineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt3LineDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Total Miscellaneous Charge
        if (modifiedDtl.Rpt1TotalMiscChrg != originalDtl.Rpt1TotalMiscChrg)
        {
            changes.Add($"APInvDtl.Rpt1TotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt1TotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt1TotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt2 Total Miscellaneous Charge
        if (modifiedDtl.Rpt2TotalMiscChrg != originalDtl.Rpt2TotalMiscChrg)
        {
            changes.Add($"APInvDtl.Rpt2TotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt2TotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt2TotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt3 Total Miscellaneous Charge
        if (modifiedDtl.Rpt3TotalMiscChrg != originalDtl.Rpt3TotalMiscChrg)
        {
            changes.Add($"APInvDtl.Rpt3TotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt3TotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt3TotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt1 Unit Cost
        if (modifiedDtl.Rpt1UnitCost != originalDtl.Rpt1UnitCost)
        {
            changes.Add($"APInvDtl.Rpt1UnitCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt1UnitCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt1UnitCost.ToString().Replace("`", "-"));
        }

        // Rpt2 Unit Cost
        if (modifiedDtl.Rpt2UnitCost != originalDtl.Rpt2UnitCost)
        {
            changes.Add($"APInvDtl.Rpt2UnitCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt2UnitCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt2UnitCost.ToString().Replace("`", "-"));
        }

        // Rpt3 Unit Cost
        if (modifiedDtl.Rpt3UnitCost != originalDtl.Rpt3UnitCost)
        {
            changes.Add($"APInvDtl.Rpt3UnitCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt3UnitCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt3UnitCost.ToString().Replace("`", "-"));
        }

        // Document Advance Pay Applied
        if (modifiedDtl.DocAdvPayAppld != originalDtl.DocAdvPayAppld)
        {
            changes.Add($"APInvDtl.DocAdvPayAppld[Line {invoiceLine}]`"
                       + originalDtl.DocAdvPayAppld.ToString()
                       + "`"
                       + modifiedDtl.DocAdvPayAppld.ToString().Replace("`", "-"));
        }

        // Rpt1 Advance Gain Loss
        if (modifiedDtl.Rpt1AdvGainLoss != originalDtl.Rpt1AdvGainLoss)
        {
            changes.Add($"APInvDtl.Rpt1AdvGainLoss[Line {invoiceLine}]`"
                       + originalDtl.Rpt1AdvGainLoss.ToString()
                       + "`"
                       + modifiedDtl.Rpt1AdvGainLoss.ToString().Replace("`", "-"));
        }

        // Rpt2 Advance Gain Loss
        if (modifiedDtl.Rpt2AdvGainLoss != originalDtl.Rpt2AdvGainLoss)
        {
            changes.Add($"APInvDtl.Rpt2AdvGainLoss[Line {invoiceLine}]`"
                       + originalDtl.Rpt2AdvGainLoss.ToString()
                       + "`"
                       + modifiedDtl.Rpt2AdvGainLoss.ToString().Replace("`", "-"));
        }

        // Rpt3 Advance Gain Loss
        if (modifiedDtl.Rpt3AdvGainLoss != originalDtl.Rpt3AdvGainLoss)
        {
            changes.Add($"APInvDtl.Rpt3AdvGainLoss[Line {invoiceLine}]`"
                       + originalDtl.Rpt3AdvGainLoss.ToString()
                       + "`"
                       + modifiedDtl.Rpt3AdvGainLoss.ToString().Replace("`", "-"));
        }

        // Match Fiscal Year Suffix
        if (modifiedDtl.MatchFiscalYearSuffix != originalDtl.MatchFiscalYearSuffix)
        {
            changes.Add($"APInvDtl.MatchFiscalYearSuffix[Line {invoiceLine}]`"
                       + originalDtl.MatchFiscalYearSuffix.ToString()
                       + "`"
                       + modifiedDtl.MatchFiscalYearSuffix.ToString().Replace("`", "-"));
        }

        // Match Fiscal Calendar ID
        if (modifiedDtl.MatchFiscalCalendarID != originalDtl.MatchFiscalCalendarID)
        {
            changes.Add($"APInvDtl.MatchFiscalCalendarID[Line {invoiceLine}]`"
                       + originalDtl.MatchFiscalCalendarID.ToString()
                       + "`"
                       + modifiedDtl.MatchFiscalCalendarID.ToString().Replace("`", "-"));
        }

        // Tax Region Code
        if (modifiedDtl.TaxRegionCode != originalDtl.TaxRegionCode)
        {
            changes.Add($"APInvDtl.TaxRegionCode[Line {invoiceLine}]`"
                       + originalDtl.TaxRegionCode.ToString()
                       + "`"
                       + modifiedDtl.TaxRegionCode.ToString().Replace("`", "-"));
        }

        // Container ID
        if (modifiedDtl.ContainerID != originalDtl.ContainerID)
        {
            changes.Add($"APInvDtl.ContainerID[Line {invoiceLine}]`"
                       + originalDtl.ContainerID.ToString()
                       + "`"
                       + modifiedDtl.ContainerID.ToString().Replace("`", "-"));
        }

        // Drop Ship Pack Line
        if (modifiedDtl.DropShipPackLine != originalDtl.DropShipPackLine)
        {
            changes.Add($"APInvDtl.DropShipPackLine[Line {invoiceLine}]`"
                       + originalDtl.DropShipPackLine.ToString()
                       + "`"
                       + modifiedDtl.DropShipPackLine.ToString().Replace("`", "-"));
        }

        // Drop Ship Pack Slip
        if (modifiedDtl.DropShipPackSlip != originalDtl.DropShipPackSlip)
        {
            changes.Add($"APInvDtl.DropShipPackSlip[Line {invoiceLine}]`"
                       + originalDtl.DropShipPackSlip.ToString()
                       + "`"
                       + modifiedDtl.DropShipPackSlip.ToString().Replace("`", "-"));
        }

        // Correction Detail
        if (modifiedDtl.CorrectionDtl != originalDtl.CorrectionDtl)
        {
            changes.Add($"APInvDtl.CorrectionDtl[Line {invoiceLine}]`"
                       + originalDtl.CorrectionDtl.ToString()
                       + "`"
                       + modifiedDtl.CorrectionDtl.ToString().Replace("`", "-"));
        }

        // Tax Connect Calculation
        if (modifiedDtl.TaxConnectCalc != originalDtl.TaxConnectCalc)
        {
            changes.Add($"APInvDtl.TaxConnectCalc[Line {invoiceLine}]`"
                       + originalDtl.TaxConnectCalc.ToString()
                       + "`"
                       + modifiedDtl.TaxConnectCalc.ToString().Replace("`", "-"));
        }

        // Asset Number
        if (modifiedDtl.AssetNum != originalDtl.AssetNum)
        {
            changes.Add($"APInvDtl.AssetNum[Line {invoiceLine}]`"
                       + originalDtl.AssetNum.ToString()
                       + "`"
                       + modifiedDtl.AssetNum.ToString().Replace("`", "-"));
        }

        // Addition Number
        if (modifiedDtl.AdditionNum != originalDtl.AdditionNum)
        {
            changes.Add($"APInvDtl.AdditionNum[Line {invoiceLine}]`"
                       + originalDtl.AdditionNum.ToString()
                       + "`"
                       + modifiedDtl.AdditionNum.ToString().Replace("`", "-"));
        }

        // Invoice Line Reference
        if (modifiedDtl.InvoiceLineRef != originalDtl.InvoiceLineRef)
        {
            changes.Add($"APInvDtl.InvoiceLineRef[Line {invoiceLine}]`"
                       + originalDtl.InvoiceLineRef.ToString()
                       + "`"
                       + modifiedDtl.InvoiceLineRef.ToString().Replace("`", "-"));
        }

        // Document Asset Invoice Balance
        if (modifiedDtl.DocAssetInvoiceBal != originalDtl.DocAssetInvoiceBal)
        {
            changes.Add($"APInvDtl.DocAssetInvoiceBal[Line {invoiceLine}]`"
                       + originalDtl.DocAssetInvoiceBal.ToString()
                       + "`"
                       + modifiedDtl.DocAssetInvoiceBal.ToString().Replace("`", "-"));
        }

        // Asset Balance Our Quantity
        if (modifiedDtl.AssetBalOurQty != originalDtl.AssetBalOurQty)
        {
            changes.Add($"APInvDtl.AssetBalOurQty[Line {invoiceLine}]`"
                       + originalDtl.AssetBalOurQty.ToString()
                       + "`"
                       + modifiedDtl.AssetBalOurQty.ToString().Replace("`", "-"));
        }

        // Asset Quantity IUM
        if (modifiedDtl.AssetQtyIUM != originalDtl.AssetQtyIUM)
        {
            changes.Add($"APInvDtl.AssetQtyIUM[Line {invoiceLine}]`"
                       + originalDtl.AssetQtyIUM.ToString()
                       + "`"
                       + modifiedDtl.AssetQtyIUM.ToString().Replace("`", "-"));
        }

        // DMR Number
        if (modifiedDtl.DMRNum != originalDtl.DMRNum)
        {
            changes.Add($"APInvDtl.DMRNum[Line {invoiceLine}]`"
                       + originalDtl.DMRNum.ToString()
                       + "`"
                       + modifiedDtl.DMRNum.ToString().Replace("`", "-"));
        }

        // DMR Action Number
        if (modifiedDtl.DMRActionNum != originalDtl.DMRActionNum)
        {
            changes.Add($"APInvDtl.DMRActionNum[Line {invoiceLine}]`"
                       + originalDtl.DMRActionNum.ToString()
                       + "`"
                       + modifiedDtl.DMRActionNum.ToString().Replace("`", "-"));
        }

        // Created From Expense
        if (modifiedDtl.CreatedFromExpense != originalDtl.CreatedFromExpense)
        {
            changes.Add($"APInvDtl.CreatedFromExpense[Line {invoiceLine}]`"
                       + originalDtl.CreatedFromExpense.ToString()
                       + "`"
                       + modifiedDtl.CreatedFromExpense.ToString().Replace("`", "-"));
        }

        // In Unit Cost
        if (modifiedDtl.InUnitCost != originalDtl.InUnitCost)
        {
            changes.Add($"APInvDtl.InUnitCost[Line {invoiceLine}]`"
                       + originalDtl.InUnitCost.ToString()
                       + "`"
                       + modifiedDtl.InUnitCost.ToString().Replace("`", "-"));
        }

        // Document In Unit Cost
        if (modifiedDtl.DocInUnitCost != originalDtl.DocInUnitCost)
        {
            changes.Add($"APInvDtl.DocInUnitCost[Line {invoiceLine}]`"
                       + originalDtl.DocInUnitCost.ToString()
                       + "`"
                       + modifiedDtl.DocInUnitCost.ToString().Replace("`", "-"));
        }

        // Rpt1 In Unit Cost
        if (modifiedDtl.Rpt1InUnitCost != originalDtl.Rpt1InUnitCost)
        {
            changes.Add($"APInvDtl.Rpt1InUnitCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InUnitCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InUnitCost.ToString().Replace("`", "-"));
        }

        // Rpt2 In Unit Cost
        if (modifiedDtl.Rpt2InUnitCost != originalDtl.Rpt2InUnitCost)
        {
            changes.Add($"APInvDtl.Rpt2InUnitCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InUnitCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InUnitCost.ToString().Replace("`", "-"));
        }

        // Rpt3 In Unit Cost
        if (modifiedDtl.Rpt3InUnitCost != originalDtl.Rpt3InUnitCost)
        {
            changes.Add($"APInvDtl.Rpt3InUnitCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InUnitCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InUnitCost.ToString().Replace("`", "-"));
        }

        // In Extended Cost
        if (modifiedDtl.InExtCost != originalDtl.InExtCost)
        {
            changes.Add($"APInvDtl.InExtCost[Line {invoiceLine}]`"
                       + originalDtl.InExtCost.ToString()
                       + "`"
                       + modifiedDtl.InExtCost.ToString().Replace("`", "-"));
        }

        // Document In Extended Cost
        if (modifiedDtl.DocInExtCost != originalDtl.DocInExtCost)
        {
            changes.Add($"APInvDtl.DocInExtCost[Line {invoiceLine}]`"
                       + originalDtl.DocInExtCost.ToString()
                       + "`"
                       + modifiedDtl.DocInExtCost.ToString().Replace("`", "-"));
        }

        // Rpt1 In Extended Cost
        if (modifiedDtl.Rpt1InExtCost != originalDtl.Rpt1InExtCost)
        {
            changes.Add($"APInvDtl.Rpt1InExtCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InExtCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InExtCost.ToString().Replace("`", "-"));
        }

        // Rpt2 In Extended Cost
        if (modifiedDtl.Rpt2InExtCost != originalDtl.Rpt2InExtCost)
        {
            changes.Add($"APInvDtl.Rpt2InExtCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InExtCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InExtCost.ToString().Replace("`", "-"));
        }

        // Rpt3 In Extended Cost
        if (modifiedDtl.Rpt3InExtCost != originalDtl.Rpt3InExtCost)
        {
            changes.Add($"APInvDtl.Rpt3InExtCost[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InExtCost.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InExtCost.ToString().Replace("`", "-"));
        }

        // In Total Miscellaneous Charge
        if (modifiedDtl.InTotalMiscChrg != originalDtl.InTotalMiscChrg)
        {
            changes.Add($"APInvDtl.InTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.InTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.InTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Document In Total Miscellaneous Charge
        if (modifiedDtl.DocInTotalMiscChrg != originalDtl.DocInTotalMiscChrg)
        {
            changes.Add($"APInvDtl.DocInTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.DocInTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.DocInTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt1 In Total Miscellaneous Charge
        if (modifiedDtl.Rpt1InTotalMiscChrg != originalDtl.Rpt1InTotalMiscChrg)
        {
            changes.Add($"APInvDtl.Rpt1InTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt2 In Total Miscellaneous Charge
        if (modifiedDtl.Rpt2InTotalMiscChrg != originalDtl.Rpt2InTotalMiscChrg)
        {
            changes.Add($"APInvDtl.Rpt2InTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt3 In Total Miscellaneous Charge
        if (modifiedDtl.Rpt3InTotalMiscChrg != originalDtl.Rpt3InTotalMiscChrg)
        {
            changes.Add($"APInvDtl.Rpt3InTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // In Advance Pay Amount
        if (modifiedDtl.InAdvancePayAmt != originalDtl.InAdvancePayAmt)
        {
            changes.Add($"APInvDtl.InAdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.InAdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.InAdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Document In Advance Pay Amount
        if (modifiedDtl.DocInAdvancePayAmt != originalDtl.DocInAdvancePayAmt)
        {
            changes.Add($"APInvDtl.DocInAdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.DocInAdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.DocInAdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 In Advance Pay Amount
        if (modifiedDtl.Rpt1InAdvancePayAmt != originalDtl.Rpt1InAdvancePayAmt)
        {
            changes.Add($"APInvDtl.Rpt1InAdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InAdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InAdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 In Advance Pay Amount
        if (modifiedDtl.Rpt2InAdvancePayAmt != originalDtl.Rpt2InAdvancePayAmt)
        {
            changes.Add($"APInvDtl.Rpt2InAdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InAdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InAdvancePayAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 In Advance Pay Amount
        if (modifiedDtl.Rpt3InAdvancePayAmt != originalDtl.Rpt3InAdvancePayAmt)
        {
            changes.Add($"APInvDtl.Rpt3InAdvancePayAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InAdvancePayAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InAdvancePayAmt.ToString().Replace("`", "-"));
        }

        // In Line Discount Amount
        if (modifiedDtl.InLineDiscAmt != originalDtl.InLineDiscAmt)
        {
            changes.Add($"APInvDtl.InLineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.InLineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.InLineDiscAmt.ToString().Replace("`", "-"));
        }

        // Document In Line Discount Amount
        if (modifiedDtl.DocInLineDiscAmt != originalDtl.DocInLineDiscAmt)
        {
            changes.Add($"APInvDtl.DocInLineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.DocInLineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.DocInLineDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 In Line Discount Amount
        if (modifiedDtl.Rpt1InLineDiscAmt != originalDtl.Rpt1InLineDiscAmt)
        {
            changes.Add($"APInvDtl.Rpt1InLineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InLineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InLineDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 In Line Discount Amount
        if (modifiedDtl.Rpt2InLineDiscAmt != originalDtl.Rpt2InLineDiscAmt)
        {
            changes.Add($"APInvDtl.Rpt2InLineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InLineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InLineDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 In Line Discount Amount
        if (modifiedDtl.Rpt3InLineDiscAmt != originalDtl.Rpt3InLineDiscAmt)
        {
            changes.Add($"APInvDtl.Rpt3InLineDiscAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InLineDiscAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InLineDiscAmt.ToString().Replace("`", "-"));
        }

        // No Tax Recalculation
        if (modifiedDtl.NoTaxRecal != originalDtl.NoTaxRecal)
        {
            changes.Add($"APInvDtl.NoTaxRecal[Line {invoiceLine}]`"
                       + originalDtl.NoTaxRecal.ToString()
                       + "`"
                       + modifiedDtl.NoTaxRecal.ToString().Replace("`", "-"));
        }

        // Dev Int1
        if (modifiedDtl.DevInt1 != originalDtl.DevInt1)
        {
            changes.Add($"APInvDtl.DevInt1[Line {invoiceLine}]`"
                       + originalDtl.DevInt1.ToString()
                       + "`"
                       + modifiedDtl.DevInt1.ToString().Replace("`", "-"));
        }

        // Dev Int2
        if (modifiedDtl.DevInt2 != originalDtl.DevInt2)
        {
            changes.Add($"APInvDtl.DevInt2[Line {invoiceLine}]`"
                       + originalDtl.DevInt2.ToString()
                       + "`"
                       + modifiedDtl.DevInt2.ToString().Replace("`", "-"));
        }

        // Dev Dec1
        if (modifiedDtl.DevDec1 != originalDtl.DevDec1)
        {
            changes.Add($"APInvDtl.DevDec1[Line {invoiceLine}]`"
                       + originalDtl.DevDec1.ToString()
                       + "`"
                       + modifiedDtl.DevDec1.ToString().Replace("`", "-"));
        }

        // Dev Dec2
        if (modifiedDtl.DevDec2 != originalDtl.DevDec2)
        {
            changes.Add($"APInvDtl.DevDec2[Line {invoiceLine}]`"
                       + originalDtl.DevDec2.ToString()
                       + "`"
                       + modifiedDtl.DevDec2.ToString().Replace("`", "-"));
        }

        // Dev Dec3
        if (modifiedDtl.DevDec3 != originalDtl.DevDec3)
        {
            changes.Add($"APInvDtl.DevDec3[Line {invoiceLine}]`"
                       + originalDtl.DevDec3.ToString()
                       + "`"
                       + modifiedDtl.DevDec3.ToString().Replace("`", "-"));
        }

        // Dev Dec4
        if (modifiedDtl.DevDec4 != originalDtl.DevDec4)
        {
            changes.Add($"APInvDtl.DevDec4[Line {invoiceLine}]`"
                       + originalDtl.DevDec4.ToString()
                       + "`"
                       + modifiedDtl.DevDec4.ToString().Replace("`", "-"));
        }

        // Dev Log1
        if (modifiedDtl.DevLog1 != originalDtl.DevLog1)
        {
            changes.Add($"APInvDtl.DevLog1[Line {invoiceLine}]`"
                       + originalDtl.DevLog1.ToString()
                       + "`"
                       + modifiedDtl.DevLog1.ToString().Replace("`", "-"));
        }

        // Dev Log2
        if (modifiedDtl.DevLog2 != originalDtl.DevLog2)
        {
            changes.Add($"APInvDtl.DevLog2[Line {invoiceLine}]`"
                       + originalDtl.DevLog2.ToString()
                       + "`"
                       + modifiedDtl.DevLog2.ToString().Replace("`", "-"));
        }

        // Dev Char1
        if (modifiedDtl.DevChar1 != originalDtl.DevChar1)
        {
            changes.Add($"APInvDtl.DevChar1[Line {invoiceLine}]`"
                       + originalDtl.DevChar1.ToString()
                       + "`"
                       + modifiedDtl.DevChar1.ToString().Replace("`", "-"));
        }

        // Dev Char2
        if (modifiedDtl.DevChar2 != originalDtl.DevChar2)
        {
            changes.Add($"APInvDtl.DevChar2[Line {invoiceLine}]`"
                       + originalDtl.DevChar2.ToString()
                       + "`"
                       + modifiedDtl.DevChar2.ToString().Replace("`", "-"));
        }

        // Dev Date1
        if (modifiedDtl.DevDate1 != originalDtl.DevDate1)
        {
            changes.Add($"APInvDtl.DevDate1[Line {invoiceLine}]`"
                       + originalDtl.DevDate1.ToString()
                       + "`"
                       + modifiedDtl.DevDate1.ToString().Replace("`", "-"));
        }

        // Dev Date2
        if (modifiedDtl.DevDate2 != originalDtl.DevDate2)
        {
            changes.Add($"APInvDtl.DevDate2[Line {invoiceLine}]`"
                       + originalDtl.DevDate2.ToString()
                       + "`"
                       + modifiedDtl.DevDate2.ToString().Replace("`", "-"));
        }



        // Invoice Reference
        if (modifiedDtl.InvoiceRef != originalDtl.InvoiceRef)
        {
            changes.Add($"APInvDtl.InvoiceRef[Line {invoiceLine}]`"
                       + originalDtl.InvoiceRef.ToString()
                       + "`"
                       + modifiedDtl.InvoiceRef.ToString().Replace("`", "-"));
        }

        // AP Transaction Number
        if (modifiedDtl.APTranNo != originalDtl.APTranNo)
        {
            changes.Add($"APInvDtl.APTranNo[Line {invoiceLine}]`"
                       + originalDtl.APTranNo.ToString()
                       + "`"
                       + modifiedDtl.APTranNo.ToString().Replace("`", "-"));
        }

        // Document Advance Pay Applied Amount
        if (modifiedDtl.DocAdvPayAppliedAmt != originalDtl.DocAdvPayAppliedAmt)
        {
            changes.Add($"APInvDtl.DocAdvPayAppliedAmt[Line {invoiceLine}]`"
                       + originalDtl.DocAdvPayAppliedAmt.ToString()
                       + "`"
                       + modifiedDtl.DocAdvPayAppliedAmt.ToString().Replace("`", "-"));
        }

        // Code 1099 ID
        if (modifiedDtl.Code1099ID != originalDtl.Code1099ID)
        {
            changes.Add($"APInvDtl.Code1099ID[Line {invoiceLine}]`"
                       + originalDtl.Code1099ID.ToString()
                       + "`"
                       + modifiedDtl.Code1099ID.ToString().Replace("`", "-"));
        }

        // Gen 1099 Code
        if (modifiedDtl.Gen1099Code != originalDtl.Gen1099Code)
        {
            changes.Add($"APInvDtl.Gen1099Code[Line {invoiceLine}]`"
                       + originalDtl.Gen1099Code.ToString()
                       + "`"
                       + modifiedDtl.Gen1099Code.ToString().Replace("`", "-"));
        }

        // Form Type ID
        if (modifiedDtl.FormTypeID != originalDtl.FormTypeID)
        {
            changes.Add($"APInvDtl.FormTypeID[Line {invoiceLine}]`"
                       + originalDtl.FormTypeID.ToString()
                       + "`"
                       + modifiedDtl.FormTypeID.ToString().Replace("`", "-"));
        }

        // DE Is Services
        if (modifiedDtl.DEIsServices != originalDtl.DEIsServices)
        {
            changes.Add($"APInvDtl.DEIsServices[Line {invoiceLine}]`"
                       + originalDtl.DEIsServices.ToString()
                       + "`"
                       + modifiedDtl.DEIsServices.ToString().Replace("`", "-"));
        }

        // DE Is Security Financial Derivative
        if (modifiedDtl.DEIsSecurityFinancialDerivative != originalDtl.DEIsSecurityFinancialDerivative)
        {
            changes.Add($"APInvDtl.DEIsSecurityFinancialDerivative[Line {invoiceLine}]`"
                       + originalDtl.DEIsSecurityFinancialDerivative.ToString()
                       + "`"
                       + modifiedDtl.DEIsSecurityFinancialDerivative.ToString().Replace("`", "-"));
        }

        // DE International Securities ID
        if (modifiedDtl.DEInternationalSecuritiesID != originalDtl.DEInternationalSecuritiesID)
        {
            changes.Add($"APInvDtl.DEInternationalSecuritiesID[Line {invoiceLine}]`"
                       + originalDtl.DEInternationalSecuritiesID.ToString()
                       + "`"
                       + modifiedDtl.DEInternationalSecuritiesID.ToString().Replace("`", "-"));
        }

        // DE Is Investment
        if (modifiedDtl.DEIsInvestment != originalDtl.DEIsInvestment)
        {
            changes.Add($"APInvDtl.DEIsInvestment[Line {invoiceLine}]`"
                       + originalDtl.DEIsInvestment.ToString()
                       + "`"
                       + modifiedDtl.DEIsInvestment.ToString().Replace("`", "-"));
        }

        // DE Pay Stat Code
        if (modifiedDtl.DEPayStatCode != originalDtl.DEPayStatCode)
        {
            changes.Add($"APInvDtl.DEPayStatCode[Line {invoiceLine}]`"
                       + originalDtl.DEPayStatCode.ToString()
                       + "`"
                       + modifiedDtl.DEPayStatCode.ToString().Replace("`", "-"));
        }









        // Changed By
        if (modifiedDtl.ChangedBy != originalDtl.ChangedBy)
        {
            changes.Add($"APInvDtl.ChangedBy[Line {invoiceLine}]`"
                       + originalDtl.ChangedBy.ToString()
                       + "`"
                       + modifiedDtl.ChangedBy.ToString().Replace("`", "-"));
        }

        // Change Date
        if (modifiedDtl.ChangeDate != originalDtl.ChangeDate)
        {
            changes.Add($"APInvDtl.ChangeDate[Line {invoiceLine}]`"
                       + originalDtl.ChangeDate.ToString()
                       + "`"
                       + modifiedDtl.ChangeDate.ToString().Replace("`", "-"));
        }

    }
}

// =================================================================
// Final Processing and Output
// =================================================================

// Set callFunc if any changes were detected
if (changes.Count > 0)
{
    callFunc = true;
}

// Distribute changes across changesMade variables using 1000-character chunks
allChanges = string.Join("`~`", changes);

if (allChanges.Length > 0)
{
    // Split into 1000-character chunks
    int chunkSize = 1000;
    int currentIndex = 0;

    if (allChanges.Length > currentIndex)
    {
        changesMade = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade2 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade3 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade4 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade5 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade6 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade7 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade8 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade9 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
        currentIndex += chunkSize;
    }

    if (allChanges.Length > currentIndex)
    {
        changesMade10 = allChanges.Substring(currentIndex, Math.Min(chunkSize, allChanges.Length - currentIndex));
    }
}
