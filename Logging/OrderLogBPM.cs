// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;

// Validate dataset before processing
if (ds.OrderHed == null || ds.OrderHed.Count == 0)
{
    test1 = "Error: No order data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.OrderHed.Count.ToString()} order records";

// Get the modified order record
var modified = ds.OrderHed[0];
orderNum = modified.OrderNum.ToString();
test2 = "Debug: Successfully accessed ds.OrderHed[0]";

// Check if the row has been deleted (RowMod = "D" in original record)
if (ds.OrderHed[0].RowMod == "D")
{
	changesMade = "Order deleted";
	callFunc = true;
}

// Get the original order record from database
var original = (from dbOrder in Db.OrderHed
                where dbOrder.Company == companyID
                   && dbOrder.OrderNum.ToString() == orderNum
                select dbOrder).FirstOrDefault();

// Handle new order creation
if (original == null)
{
    changesMade = $"New order created: {modified.OrderNum.ToString()}";
    callFunc = true;
}
else
{
    // Track all changes in a list
    List<string> changes = new List<string>();

    // Determine which record to use as the modified version
    // Check if there's a second record (modified version)
    if (ds.OrderHed.Count > 1)
    {
        try
        {
            modified = ds.OrderHed[1];
            test2 = "Debug: Successfully accessed ds.OrderHed[1]";
        }
        catch (System.Exception ex)
        {
            test2 = $"Error accessing ds.OrderHed[1]: {ex.Message}";
            return;
        }
    }
    else
    {
        test2 = "Debug: Using ds.OrderHed[0] as modified (only 1 record)";
    }

    // =================================================================
    // OrderHed Field Comparisons
    // =================================================================

    // Customer Number
    if (modified.CustNum != original.CustNum)
    {
        changes.Add("CustNum`"
                   + original.CustNum.ToString()
                   + "`"
                   + modified.CustNum.ToString().Replace("`", "-"));
    }

    // Purchase Order Number
    if (modified.PONum != original.PONum)
    {
        changes.Add("PONum`"
                   + original.PONum.ToString()
                   + "`"
                   + modified.PONum.ToString().Replace("`", "-"));
    }

    // Order Date
    if (modified.OrderDate != original.OrderDate)
    {
        changes.Add("OrderDate`"
                   + original.OrderDate.ToString()
                   + "`"
                   + modified.OrderDate.ToString().Replace("`", "-"));
    }

    // Request Date
    if (modified.RequestDate != original.RequestDate)
    {
        changes.Add("RequestDate`"
                   + original.RequestDate.ToString()
                   + "`"
                   + modified.RequestDate.ToString().Replace("`", "-"));
    }

    // Ship To Number
    if (modified.ShipToNum != original.ShipToNum)
    {
        changes.Add("ShipToNum`"
                   + original.ShipToNum.ToString()
                   + "`"
                   + modified.ShipToNum.ToString().Replace("`", "-"));
    }

    // Ship Via Code
    if (modified.ShipViaCode != original.ShipViaCode)
    {
        changes.Add("ShipViaCode`"
                   + original.ShipViaCode.ToString()
                   + "`"
                   + modified.ShipViaCode.ToString().Replace("`", "-"));
    }

    // Terms Code
    if (modified.TermsCode != original.TermsCode)
    {
        changes.Add("TermsCode`"
                   + original.TermsCode.ToString()
                   + "`"
                   + modified.TermsCode.ToString().Replace("`", "-"));
    }

    // Discount Percent
    if (modified.DiscountPercent != original.DiscountPercent)
    {
        changes.Add("DiscountPercent`"
                   + original.DiscountPercent.ToString()
                   + "`"
                   + modified.DiscountPercent.ToString().Replace("`", "-"));
    }

    // Price Contact Number
    if (modified.PrcConNum != original.PrcConNum)
    {
        changes.Add("PrcConNum`"
                   + original.PrcConNum.ToString()
                   + "`"
                   + modified.PrcConNum.ToString().Replace("`", "-"));
    }

    // Ship Contact Number
    if (modified.ShpConNum != original.ShpConNum)
    {
        changes.Add("ShpConNum`"
                   + original.ShpConNum.ToString()
                   + "`"
                   + modified.ShpConNum.ToString().Replace("`", "-"));
    }

    // Currency Code
    if (modified.CurrencyCode != original.CurrencyCode)
    {
        changes.Add("CurrencyCode`"
                   + original.CurrencyCode.ToString()
                   + "`"
                   + modified.CurrencyCode.ToString().Replace("`", "-"));
    }

    // Exchange Rate
    if (modified.ExchangeRate != original.ExchangeRate)
    {
        changes.Add("ExchangeRate`"
                   + original.ExchangeRate.ToString()
                   + "`"
                   + modified.ExchangeRate.ToString().Replace("`", "-"));
    }

    // Allocation Priority Code
    if (modified.AllocPriorityCode != original.AllocPriorityCode)
    {
        changes.Add("AllocPriorityCode`"
                   + original.AllocPriorityCode.ToString()
                   + "`"
                   + modified.AllocPriorityCode.ToString().Replace("`", "-"));
    }

    // Reserve Priority Code
    if (modified.ReservePriorityCode != original.ReservePriorityCode)
    {
        changes.Add("ReservePriorityCode`"
                   + original.ReservePriorityCode.ToString()
                   + "`"
                   + modified.ReservePriorityCode.ToString().Replace("`", "-"));
    }

    // Ship Order Complete
    if (modified.ShipOrderComplete != original.ShipOrderComplete)
    {
        changes.Add("ShipOrderComplete`"
                   + original.ShipOrderComplete.ToString()
                   + "`"
                   + modified.ShipOrderComplete.ToString().Replace("`", "-"));
    }

    // Order Comment
    if (modified.OrderComment != original.OrderComment)
    {
        changes.Add("OrderComment`"
                   + original.OrderComment.ToString()
                   + "`"
                   + modified.OrderComment.ToString().Replace("`", "-"));
    }

    // Ship Comment
    if (modified.ShipComment != original.ShipComment)
    {
        changes.Add("ShipComment`"
                   + original.ShipComment.ToString()
                   + "`"
                   + modified.ShipComment.ToString().Replace("`", "-"));
    }

    // Invoice Comment
    if (modified.InvoiceComment != original.InvoiceComment)
    {
        changes.Add("InvoiceComment`"
                   + original.InvoiceComment.ToString()
                   + "`"
                   + modified.InvoiceComment.ToString().Replace("`", "-"));
    }

    // Auto Order Based Discount
    if (modified.AutoOrderBasedDisc != original.AutoOrderBasedDisc)
    {
        changes.Add("AutoOrderBasedDisc`"
                   + original.AutoOrderBasedDisc.ToString()
                   + "`"
                   + modified.AutoOrderBasedDisc.ToString().Replace("`", "-"));
    }

    // Apply Order Based Discount
    if (modified.ApplyOrderBasedDisc != original.ApplyOrderBasedDisc)
    {
        changes.Add("ApplyOrderBasedDisc`"
                   + original.ApplyOrderBasedDisc.ToString()
                   + "`"
                   + modified.ApplyOrderBasedDisc.ToString().Replace("`", "-"));
    }

    // Help Desk Case Number
    if (modified.HDCaseNum != original.HDCaseNum)
    {
        changes.Add("HDCaseNum`"
                   + original.HDCaseNum.ToString()
                   + "`"
                   + modified.HDCaseNum.ToString().Replace("`", "-"));
    }

    // Bill To Customer Number
    if (modified.BTCustNum != original.BTCustNum)
    {
        changes.Add("BTCustNum`"
                   + original.BTCustNum.ToString()
                   + "`"
                   + modified.BTCustNum.ToString().Replace("`", "-"));
    }

    // Need By Date
    if (modified.NeedByDate != original.NeedByDate)
    {
        changes.Add("NeedByDate`"
                   + original.NeedByDate.ToString()
                   + "`"
                   + modified.NeedByDate.ToString().Replace("`", "-"));
    }

    // Cancel After Date
    if (modified.CancelAfterDate != original.CancelAfterDate)
    {
        changes.Add("CancelAfterDate`"
                   + original.CancelAfterDate.ToString()
                   + "`"
                   + modified.CancelAfterDate.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Order Amount Fields
    // =================================================================

    // Order Amount
    if (modified.OrderAmt != original.OrderAmt)
    {
        changes.Add("OrderAmt`"
                   + original.OrderAmt.ToString()
                   + "`"
                   + modified.OrderAmt.ToString().Replace("`", "-"));
    }

    // Document Order Amount
    if (modified.DocOrderAmt != original.DocOrderAmt)
    {
        changes.Add("DocOrderAmt`"
                   + original.DocOrderAmt.ToString()
                   + "`"
                   + modified.DocOrderAmt.ToString().Replace("`", "-"));
    }

    // Report 1 Order Amount
    if (modified.Rpt1OrderAmt != original.Rpt1OrderAmt)
    {
        changes.Add("Rpt1OrderAmt`"
                   + original.Rpt1OrderAmt.ToString()
                   + "`"
                   + modified.Rpt1OrderAmt.ToString().Replace("`", "-"));
    }

    // Report 2 Order Amount
    if (modified.Rpt2OrderAmt != original.Rpt2OrderAmt)
    {
        changes.Add("Rpt2OrderAmt`"
                   + original.Rpt2OrderAmt.ToString()
                   + "`"
                   + modified.Rpt2OrderAmt.ToString().Replace("`", "-"));
    }

    // Report 3 Order Amount
    if (modified.Rpt3OrderAmt != original.Rpt3OrderAmt)
    {
        changes.Add("Rpt3OrderAmt`"
                   + original.Rpt3OrderAmt.ToString()
                   + "`"
                   + modified.Rpt3OrderAmt.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Credit Override Fields
    // =================================================================

    // Credit Override
    if (modified.CreditOverride != original.CreditOverride)
    {
        changes.Add("CreditOverride`"
                   + original.CreditOverride.ToString()
                   + "`"
                   + modified.CreditOverride.ToString().Replace("`", "-"));
    }

    // Credit Override User ID
    if (modified.CreditOverrideUserID != original.CreditOverrideUserID)
    {
        changes.Add("CreditOverrideUserID`"
                   + original.CreditOverrideUserID.ToString()
                   + "`"
                   + modified.CreditOverrideUserID.ToString().Replace("`", "-"));
    }

    // Credit Override Date
    if (modified.CreditOverrideDate != original.CreditOverrideDate)
    {
        changes.Add("CreditOverrideDate`"
                   + original.CreditOverrideDate.ToString()
                   + "`"
                   + modified.CreditOverrideDate.ToString().Replace("`", "-"));
    }

    // Credit Override Time
    if (modified.CreditOverrideTime != original.CreditOverrideTime)
    {
        changes.Add("CreditOverrideTime`"
                   + original.CreditOverrideTime.ToString()
                   + "`"
                   + modified.CreditOverrideTime.ToString().Replace("`", "-"));
    }

    // Credit Override Limit
    if (modified.CreditOverrideLimit != original.CreditOverrideLimit)
    {
        changes.Add("CreditOverrideLimit`"
                   + original.CreditOverrideLimit.ToString()
                   + "`"
                   + modified.CreditOverrideLimit.ToString().Replace("`", "-"));
    }

    // =================================================================
    // EDI and Demand Fields
    // =================================================================

    // EDI Order
    if (modified.EDIOrder != original.EDIOrder)
    {
        changes.Add("EDIOrder`"
                   + original.EDIOrder.ToString()
                   + "`"
                   + modified.EDIOrder.ToString().Replace("`", "-"));
    }

    // EDI Acknowledgment
    if (modified.EDIAck != original.EDIAck)
    {
        changes.Add("EDIAck`"
                   + original.EDIAck.ToString()
                   + "`"
                   + modified.EDIAck.ToString().Replace("`", "-"));
    }

    // Demand Contract Number
    if (modified.DemandContractNum != original.DemandContractNum)
    {
        changes.Add("DemandContractNum`"
                   + original.DemandContractNum.ToString()
                   + "`"
                   + modified.DemandContractNum.ToString().Replace("`", "-"));
    }

    // Demand Head Sequence
    if (modified.DemandHeadSeq != original.DemandHeadSeq)
    {
        changes.Add("DemandHeadSeq`"
                   + original.DemandHeadSeq.ToString()
                   + "`"
                   + modified.DemandHeadSeq.ToString().Replace("`", "-"));
    }

    // Demand Process Date
    if (modified.DemandProcessDate != original.DemandProcessDate)
    {
        changes.Add("DemandProcessDate`"
                   + original.DemandProcessDate.ToString()
                   + "`"
                   + modified.DemandProcessDate.ToString().Replace("`", "-"));
    }

    // Demand Process Time
    if (modified.DemandProcessTime != original.DemandProcessTime)
    {
        changes.Add("DemandProcessTime`"
                   + original.DemandProcessTime.ToString()
                   + "`"
                   + modified.DemandProcessTime.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Control Numbers and Document Totals
    // =================================================================

    // Last Transaction Control Number
    if (modified.LastTCtrlNum != original.LastTCtrlNum)
    {
        changes.Add("LastTCtrlNum`"
                   + original.LastTCtrlNum.ToString()
                   + "`"
                   + modified.LastTCtrlNum.ToString().Replace("`", "-"));
    }

    // Last Batch Number
    if (modified.LastBatchNum != original.LastBatchNum)
    {
        changes.Add("LastBatchNum`"
                   + original.LastBatchNum.ToString()
                   + "`"
                   + modified.LastBatchNum.ToString().Replace("`", "-"));
    }

    // Document Total Charges
    if (modified.DocTotalCharges != original.DocTotalCharges)
    {
        changes.Add("DocTotalCharges`"
                   + original.DocTotalCharges.ToString()
                   + "`"
                   + modified.DocTotalCharges.ToString().Replace("`", "-"));
    }

    // Document Total Miscellaneous
    if (modified.DocTotalMisc != original.DocTotalMisc)
    {
        changes.Add("DocTotalMisc`"
                   + original.DocTotalMisc.ToString()
                   + "`"
                   + modified.DocTotalMisc.ToString().Replace("`", "-"));
    }

    // Document Total Sales Tax
    if (modified.DocTotalSATax != original.DocTotalSATax)
    {
        changes.Add("DocTotalSATax`"
                   + original.DocTotalSATax.ToString()
                   + "`"
                   + modified.DocTotalSATax.ToString().Replace("`", "-"));
    }

    // Document Total Tax
    if (modified.DocTotalTax != original.DocTotalTax)
    {
        changes.Add("DocTotalTax`"
                   + original.DocTotalTax.ToString()
                   + "`"
                   + modified.DocTotalTax.ToString().Replace("`", "-"));
    }

    // Document Total Withholding Tax
    if (modified.DocTotalWHTax != original.DocTotalWHTax)
    {
        changes.Add("DocTotalWHTax`"
                   + original.DocTotalWHTax.ToString()
                   + "`"
                   + modified.DocTotalWHTax.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Report 1 Totals
    // =================================================================

    // Report 1 Total Charges
    if (modified.Rpt1TotalCharges != original.Rpt1TotalCharges)
    {
        changes.Add("Rpt1TotalCharges`"
                   + original.Rpt1TotalCharges.ToString()
                   + "`"
                   + modified.Rpt1TotalCharges.ToString().Replace("`", "-"));
    }

    // Report 1 Total Miscellaneous
    if (modified.Rpt1TotalMisc != original.Rpt1TotalMisc)
    {
        changes.Add("Rpt1TotalMisc`"
                   + original.Rpt1TotalMisc.ToString()
                   + "`"
                   + modified.Rpt1TotalMisc.ToString().Replace("`", "-"));
    }

    // Report 1 Total Sales Tax
    if (modified.Rpt1TotalSATax != original.Rpt1TotalSATax)
    {
        changes.Add("Rpt1TotalSATax`"
                   + original.Rpt1TotalSATax.ToString()
                   + "`"
                   + modified.Rpt1TotalSATax.ToString().Replace("`", "-"));
    }

    // Report 1 Total Tax
    if (modified.Rpt1TotalTax != original.Rpt1TotalTax)
    {
        changes.Add("Rpt1TotalTax`"
                   + original.Rpt1TotalTax.ToString()
                   + "`"
                   + modified.Rpt1TotalTax.ToString().Replace("`", "-"));
    }

    // Report 1 Total Withholding Tax
    if (modified.Rpt1TotalWHTax != original.Rpt1TotalWHTax)
    {
        changes.Add("Rpt1TotalWHTax`"
                   + original.Rpt1TotalWHTax.ToString()
                   + "`"
                   + modified.Rpt1TotalWHTax.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Report 2 Totals
    // =================================================================

    // Report 2 Total Charges
    if (modified.Rpt2TotalCharges != original.Rpt2TotalCharges)
    {
        changes.Add("Rpt2TotalCharges`"
                   + original.Rpt2TotalCharges.ToString()
                   + "`"
                   + modified.Rpt2TotalCharges.ToString().Replace("`", "-"));
    }

    // Report 2 Total Miscellaneous
    if (modified.Rpt2TotalMisc != original.Rpt2TotalMisc)
    {
        changes.Add("Rpt2TotalMisc`"
                   + original.Rpt2TotalMisc.ToString()
                   + "`"
                   + modified.Rpt2TotalMisc.ToString().Replace("`", "-"));
    }

    // Report 2 Total Sales Tax
    if (modified.Rpt2TotalSATax != original.Rpt2TotalSATax)
    {
        changes.Add("Rpt2TotalSATax`"
                   + original.Rpt2TotalSATax.ToString()
                   + "`"
                   + modified.Rpt2TotalSATax.ToString().Replace("`", "-"));
    }

    // Report 2 Total Tax
    if (modified.Rpt2TotalTax != original.Rpt2TotalTax)
    {
        changes.Add("Rpt2TotalTax`"
                   + original.Rpt2TotalTax.ToString()
                   + "`"
                   + modified.Rpt2TotalTax.ToString().Replace("`", "-"));
    }

    // Report 2 Total Withholding Tax
    if (modified.Rpt2TotalWHTax != original.Rpt2TotalWHTax)
    {
        changes.Add("Rpt2TotalWHTax`"
                   + original.Rpt2TotalWHTax.ToString()
                   + "`"
                   + modified.Rpt2TotalWHTax.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Report 3 Totals
    // =================================================================

    // Report 3 Total Charges
    if (modified.Rpt3TotalCharges != original.Rpt3TotalCharges)
    {
        changes.Add("Rpt3TotalCharges`"
                   + original.Rpt3TotalCharges.ToString()
                   + "`"
                   + modified.Rpt3TotalCharges.ToString().Replace("`", "-"));
    }

    // Report 3 Total Miscellaneous
    if (modified.Rpt3TotalMisc != original.Rpt3TotalMisc)
    {
        changes.Add("Rpt3TotalMisc`"
                   + original.Rpt3TotalMisc.ToString()
                   + "`"
                   + modified.Rpt3TotalMisc.ToString().Replace("`", "-"));
    }

    // Report 3 Total Sales Tax
    if (modified.Rpt3TotalSATax != original.Rpt3TotalSATax)
    {
        changes.Add("Rpt3TotalSATax`"
                   + original.Rpt3TotalSATax.ToString()
                   + "`"
                   + modified.Rpt3TotalSATax.ToString().Replace("`", "-"));
    }

    // Report 3 Total Tax
    if (modified.Rpt3TotalTax != original.Rpt3TotalTax)
    {
        changes.Add("Rpt3TotalTax`"
                   + original.Rpt3TotalTax.ToString()
                   + "`"
                   + modified.Rpt3TotalTax.ToString().Replace("`", "-"));
    }

    // Report 3 Total Withholding Tax
    if (modified.Rpt3TotalWHTax != original.Rpt3TotalWHTax)
    {
        changes.Add("Rpt3TotalWHTax`"
                   + original.Rpt3TotalWHTax.ToString()
                   + "`"
                   + modified.Rpt3TotalWHTax.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Base Currency Totals
    // =================================================================

    // Total Charges
    if (modified.TotalCharges != original.TotalCharges)
    {
        changes.Add("TotalCharges`"
                   + original.TotalCharges.ToString()
                   + "`"
                   + modified.TotalCharges.ToString().Replace("`", "-"));
    }

    // Total Miscellaneous
    if (modified.TotalMisc != original.TotalMisc)
    {
        changes.Add("TotalMisc`"
                   + original.TotalMisc.ToString()
                   + "`"
                   + modified.TotalMisc.ToString().Replace("`", "-"));
    }

    // Total Sales Tax
    if (modified.TotalSATax != original.TotalSATax)
    {
        changes.Add("TotalSATax`"
                   + original.TotalSATax.ToString()
                   + "`"
                   + modified.TotalSATax.ToString().Replace("`", "-"));
    }

    // Total Tax
    if (modified.TotalTax != original.TotalTax)
    {
        changes.Add("TotalTax`"
                   + original.TotalTax.ToString()
                   + "`"
                   + modified.TotalTax.ToString().Replace("`", "-"));
    }

    // Total Withholding Tax
    if (modified.TotalWHTax != original.TotalWHTax)
    {
        changes.Add("TotalWHTax`"
                   + original.TotalWHTax.ToString()
                   + "`"
                   + modified.TotalWHTax.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Process OrderDtl (Order Line) Changes
    // =================================================================

    if (ds.OrderDtl != null && ds.OrderDtl.Count > 0)
    {
        foreach (var modifiedDtl in ds.OrderDtl)
        {
            // Find the corresponding original order line
            var originalDtl = (from dbDtl in Db.OrderDtl
                              where dbDtl.Company == companyID
                                 && dbDtl.OrderNum.ToString() == orderNum
                                 && dbDtl.OrderLine == modifiedDtl.OrderLine
                              select dbDtl).FirstOrDefault();

            if (originalDtl == null)
            {
                // New order line added
                changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}`NEW`{modifiedDtl.OrderLine.ToString()}");
            }
            else
            {
                // Check for changes in OrderDtl fields

                // Part Number
                if (modifiedDtl.PartNum != originalDtl.PartNum)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_PartNum`"
                               + originalDtl.PartNum.ToString()
                               + "`"
                               + modifiedDtl.PartNum.ToString().Replace("`", "-"));
                }

                // Line Description
                if (modifiedDtl.LineDesc != originalDtl.LineDesc)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_LineDesc`"
                               + originalDtl.LineDesc.ToString()
                               + "`"
                               + modifiedDtl.LineDesc.ToString().Replace("`", "-"));
                }

                // Order Quantity
                if (modifiedDtl.OrderQty != originalDtl.OrderQty)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_OrderQty`"
                               + originalDtl.OrderQty.ToString()
                               + "`"
                               + modifiedDtl.OrderQty.ToString().Replace("`", "-"));
                }

                // Unit Price
                if (modifiedDtl.UnitPrice != originalDtl.UnitPrice)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_UnitPrice`"
                               + originalDtl.UnitPrice.ToString()
                               + "`"
                               + modifiedDtl.UnitPrice.ToString().Replace("`", "-"));
                }

                // Document Unit Price
                if (modifiedDtl.DocUnitPrice != originalDtl.DocUnitPrice)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_DocUnitPrice`"
                               + originalDtl.DocUnitPrice.ToString()
                               + "`"
                               + modifiedDtl.DocUnitPrice.ToString().Replace("`", "-"));
                }

                // Discount
                if (modifiedDtl.Discount != originalDtl.Discount)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_Discount`"
                               + originalDtl.Discount.ToString()
                               + "`"
                               + modifiedDtl.Discount.ToString().Replace("`", "-"));
                }

                // Document Discount
                if (modifiedDtl.DocDiscount != originalDtl.DocDiscount)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_DocDiscount`"
                               + originalDtl.DocDiscount.ToString()
                               + "`"
                               + modifiedDtl.DocDiscount.ToString().Replace("`", "-"));
                }

                // Request Date
                if (modifiedDtl.RequestDate != originalDtl.RequestDate)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_RequestDate`"
                               + originalDtl.RequestDate.ToString()
                               + "`"
                               + modifiedDtl.RequestDate.ToString().Replace("`", "-"));
                }

                // Need By Date
                if (modifiedDtl.NeedByDate != originalDtl.NeedByDate)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_NeedByDate`"
                               + originalDtl.NeedByDate.ToString()
                               + "`"
                               + modifiedDtl.NeedByDate.ToString().Replace("`", "-"));
                }

                // Reference
                if (modifiedDtl.Reference != originalDtl.Reference)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_Reference`"
                               + originalDtl.Reference.ToString()
                               + "`"
                               + modifiedDtl.Reference.ToString().Replace("`", "-"));
                }

                // Line Type
                if (modifiedDtl.LineType != originalDtl.LineType)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_LineType`"
                               + originalDtl.LineType.ToString()
                               + "`"
                               + modifiedDtl.LineType.ToString().Replace("`", "-"));
                }

                // Inventory Unit of Measure
                if (modifiedDtl.IUM != originalDtl.IUM)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_IUM`"
                               + originalDtl.IUM.ToString()
                               + "`"
                               + modifiedDtl.IUM.ToString().Replace("`", "-"));
                }

                // Sales Unit of Measure
                if (modifiedDtl.SalesUM != originalDtl.SalesUM)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_SalesUM`"
                               + originalDtl.SalesUM.ToString()
                               + "`"
                               + modifiedDtl.SalesUM.ToString().Replace("`", "-"));
                }

                // Discount Percent
                if (modifiedDtl.DiscountPercent != originalDtl.DiscountPercent)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_DiscountPercent`"
                               + originalDtl.DiscountPercent.ToString()
                               + "`"
                               + modifiedDtl.DiscountPercent.ToString().Replace("`", "-"));
                }

                // Price Per Code
                if (modifiedDtl.PricePerCode != originalDtl.PricePerCode)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_PricePerCode`"
                               + originalDtl.PricePerCode.ToString()
                               + "`"
                               + modifiedDtl.PricePerCode.ToString().Replace("`", "-"));
                }

                // Product Code
                if (modifiedDtl.ProdCode != originalDtl.ProdCode)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_ProdCode`"
                               + originalDtl.ProdCode.ToString()
                               + "`"
                               + modifiedDtl.ProdCode.ToString().Replace("`", "-"));
                }

                // Cross Reference Part Number
                if (modifiedDtl.XPartNum != originalDtl.XPartNum)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_XPartNum`"
                               + originalDtl.XPartNum.ToString()
                               + "`"
                               + modifiedDtl.XPartNum.ToString().Replace("`", "-"));
                }

                // Cross Reference Revision Number
                if (modifiedDtl.XRevisionNum != originalDtl.XRevisionNum)
                {
                    changes.Add($"OrderLine{modifiedDtl.OrderLine.ToString()}_XRevisionNum`"
                               + originalDtl.XRevisionNum.ToString()
                               + "`"
                               + modifiedDtl.XRevisionNum.ToString().Replace("`", "-"));
                }
            }
        }

        // =================================================================
        // Check for Deleted Order Lines
        // =================================================================

        var originalDtlLines = (from dbDtl in Db.OrderDtl
                               where dbDtl.Company == companyID
                                  && dbDtl.OrderNum.ToString() == orderNum
                               select dbDtl).ToList();

        foreach (var originalDtl in originalDtlLines)
        {
            var modifiedDtl = ds.OrderDtl.FirstOrDefault(d => d.OrderLine == originalDtl.OrderLine);
            if (modifiedDtl == null)
            {
                // Order line was deleted
                changes.Add($"OrderLine{originalDtl.OrderLine.ToString()}`DELETED`");
            }
        }
    }

    // =================================================================
    // Process and Split Changes for Output
    // =================================================================

    if (changes.Count > 0)
    {
        try
        {
            string allChanges = string.Join("`~`", changes);
            test2 = $"Debug: Created allChanges string, length={allChanges.Length.ToString()}";

            // Split changes into chunks if needed (max 1000 characters per chunk)
            if (allChanges.Length <= 1000)
            {
                changesMade = allChanges;
                callFunc = true;
                test2 = "Debug: Single chunk assigned to changesMade";
            }
            else
            {
                // Split into 1000-character chunks with overflow handling
                const int maxChunkSize = 1000;
                int totalLength = allChanges.Length;

                // Clear all changesMade variables first
                changesMade = "";
                changesMade2 = "";
                changesMade3 = "";
                changesMade4 = "";
                changesMade5 = "";
                changesMade6 = "";
                changesMade7 = "";
                changesMade8 = "";
                changesMade9 = "";
                changesMade10 = "";

                // Assign chunks based on position - each exactly 1000 chars (or remaining)
                if (totalLength > 0)
                {
                    changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
                    callFunc = true;
                }
                if (totalLength > 1000)
                {
                    changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
                }
                if (totalLength > 2000)
                {
                    changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
                }
                if (totalLength > 3000)
                {
                    changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
                }
                if (totalLength > 4000)
                {
                    changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
                }
                if (totalLength > 5000)
                {
                    changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
                }
                if (totalLength > 6000)
                {
                    changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
                }
                if (totalLength > 7000)
                {
                    changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
                }
                if (totalLength > 8000)
                {
                    changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
                }
                if (totalLength > 9000)
                {
                    changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
                }

                test2 = $"Debug: Split {totalLength.ToString()} chars into chunks successfully";
            }
        }
        catch (System.Exception ex)
        {
            test2 = $"Debug: Exception in splitting logic: {ex.Message} - Type: {ex.GetType().Name}";
            changesMade = "Error in splitting logic";
            callFunc = true;
        }
    }
}
