// =================================================================
// Transfer Order Change Tracking BPM
// =================================================================
// This BPM tracks changes to TFOrdHed and TFOrdDtl records and logs
// them for auditing purposes. It compares the current dataset with
// the original database records to identify what has changed.
// =================================================================

// =================================================================
// Initialize Variables
// =================================================================

// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;
TransferOrderNumber = "";

// Validate dataset before processing
if (ds.TFOrdHed == null || ds.TFOrdHed.Count == 0)
{
    test1 = "Error: No transfer order data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.TFOrdHed.Count.ToString()} transfer order records";

// Track all changes in a list
List<string> changes = new List<string>();

// =================================================================
// Process TFOrdHed Changes
// =================================================================

// Get the modified transfer order record
var modifiedTFOrdHed = ds.TFOrdHed[0];
string tfOrdNum = modifiedTFOrdHed.TFOrdNum.ToString();
TransferOrderNumber = tfOrdNum;
test2 = "Debug: Successfully accessed ds.TFOrdHed[0]";

// Check if the transfer order row has been deleted (RowMod = "D" in original record)
if (ds.TFOrdHed[0].RowMod == "D")
{
    changes.Add("TFOrdHed deleted");
    callFunc = true;
}
else
{
    // Get the original transfer order record from database
    var originalTFOrdHed = (from dbTFOrdHed in Db.TFOrdHed
                           where dbTFOrdHed.Company == companyID
                              && dbTFOrdHed.TFOrdNum.ToString() == tfOrdNum
                           select dbTFOrdHed).FirstOrDefault();

    // Handle new transfer order creation
    if (originalTFOrdHed == null)
    {
        changes.Add($"New transfer order created: {modifiedTFOrdHed.TFOrdNum.ToString()}");
        callFunc = true;
    }
    else
    {
        // Determine which record to use as the modified version
        // Check if there's a second record (modified version)
        if (ds.TFOrdHed.Count > 1)
        {
            try
            {
                modifiedTFOrdHed = ds.TFOrdHed[1];
                test2 = "Debug: Successfully accessed ds.TFOrdHed[1]";
            }
            catch (System.Exception ex)
            {
                test2 = $"Error accessing ds.TFOrdHed[1]: {ex.Message}";
                return;
            }
        }
        else
        {
            test2 = "Debug: Using ds.TFOrdHed[0] as modified (only 1 record)";
        }

        // =================================================================
        // TFOrdHed Field Comparisons
        // =================================================================

        // Company
        if (modifiedTFOrdHed.Company != originalTFOrdHed.Company)
        {
            changes.Add("TFOrdHed.Company`"
                       + originalTFOrdHed.Company.ToString()
                       + "`"
                       + modifiedTFOrdHed.Company.ToString().Replace("`", "-"));
        }

        // Transfer Order Number
        if (modifiedTFOrdHed.TFOrdNum != originalTFOrdHed.TFOrdNum)
        {
            changes.Add("TFOrdHed.TFOrdNum`"
                       + originalTFOrdHed.TFOrdNum.ToString()
                       + "`"
                       + modifiedTFOrdHed.TFOrdNum.ToString().Replace("`", "-"));
        }

        // Open Order
        if (modifiedTFOrdHed.OpenOrder != originalTFOrdHed.OpenOrder)
        {
            changes.Add("TFOrdHed.OpenOrder`"
                       + originalTFOrdHed.OpenOrder.ToString()
                       + "`"
                       + modifiedTFOrdHed.OpenOrder.ToString().Replace("`", "-"));
        }

        // Plant
        if (modifiedTFOrdHed.Plant != originalTFOrdHed.Plant)
        {
            changes.Add("TFOrdHed.Plant`"
                       + originalTFOrdHed.Plant.ToString()
                       + "`"
                       + modifiedTFOrdHed.Plant.ToString().Replace("`", "-"));
        }

        // To Plant
        if (modifiedTFOrdHed.ToPlant != originalTFOrdHed.ToPlant)
        {
            changes.Add("TFOrdHed.ToPlant`"
                       + originalTFOrdHed.ToPlant.ToString()
                       + "`"
                       + modifiedTFOrdHed.ToPlant.ToString().Replace("`", "-"));
        }

        // Entry Person
        if (modifiedTFOrdHed.EntryPerson != originalTFOrdHed.EntryPerson)
        {
            changes.Add("TFOrdHed.EntryPerson`"
                       + originalTFOrdHed.EntryPerson.ToString()
                       + "`"
                       + modifiedTFOrdHed.EntryPerson.ToString().Replace("`", "-"));
        }

        // Order Date
        if (modifiedTFOrdHed.OrderDate != originalTFOrdHed.OrderDate)
        {
            changes.Add("TFOrdHed.OrderDate`"
                       + originalTFOrdHed.OrderDate.ToString()
                       + "`"
                       + modifiedTFOrdHed.OrderDate.ToString().Replace("`", "-"));
        }

        // Ship Via Code
        if (modifiedTFOrdHed.ShipViaCode != originalTFOrdHed.ShipViaCode)
        {
            changes.Add("TFOrdHed.ShipViaCode`"
                       + originalTFOrdHed.ShipViaCode.ToString()
                       + "`"
                       + modifiedTFOrdHed.ShipViaCode.ToString().Replace("`", "-"));
        }

        // Ship Comment
        if (modifiedTFOrdHed.ShipComment != originalTFOrdHed.ShipComment)
        {
            changes.Add("TFOrdHed.ShipComment`"
                       + originalTFOrdHed.ShipComment.ToString()
                       + "`"
                       + modifiedTFOrdHed.ShipComment.ToString().Replace("`", "-"));
        }

        // Pick List Comment
        if (modifiedTFOrdHed.PickListComment != originalTFOrdHed.PickListComment)
        {
            changes.Add("TFOrdHed.PickListComment`"
                       + originalTFOrdHed.PickListComment.ToString()
                       + "`"
                       + modifiedTFOrdHed.PickListComment.ToString().Replace("`", "-"));
        }

        // Shipped
        if (modifiedTFOrdHed.Shipped != originalTFOrdHed.Shipped)
        {
            changes.Add("TFOrdHed.Shipped`"
                       + originalTFOrdHed.Shipped.ToString()
                       + "`"
                       + modifiedTFOrdHed.Shipped.ToString().Replace("`", "-"));
        }

        // Auto Print Ready
        if (modifiedTFOrdHed.AutoPrintReady != originalTFOrdHed.AutoPrintReady)
        {
            changes.Add("TFOrdHed.AutoPrintReady`"
                       + originalTFOrdHed.AutoPrintReady.ToString()
                       + "`"
                       + modifiedTFOrdHed.AutoPrintReady.ToString().Replace("`", "-"));
        }

        // Transaction Document Type ID
        if (modifiedTFOrdHed.TranDocTypeID != originalTFOrdHed.TranDocTypeID)
        {
            changes.Add("TFOrdHed.TranDocTypeID`"
                       + originalTFOrdHed.TranDocTypeID.ToString()
                       + "`"
                       + modifiedTFOrdHed.TranDocTypeID.ToString().Replace("`", "-"));
        }

        // Legal Number
        if (modifiedTFOrdHed.LegalNumber != originalTFOrdHed.LegalNumber)
        {
            changes.Add("TFOrdHed.LegalNumber`"
                       + originalTFOrdHed.LegalNumber.ToString()
                       + "`"
                       + modifiedTFOrdHed.LegalNumber.ToString().Replace("`", "-"));
        }

        // Warehouse Code
        if (modifiedTFOrdHed.WarehouseCode != originalTFOrdHed.WarehouseCode)
        {
            changes.Add("TFOrdHed.WarehouseCode`"
                       + originalTFOrdHed.WarehouseCode.ToString()
                       + "`"
                       + modifiedTFOrdHed.WarehouseCode.ToString().Replace("`", "-"));
        }

        // Use OTS
        if (modifiedTFOrdHed.UseOTS != originalTFOrdHed.UseOTS)
        {
            changes.Add("TFOrdHed.UseOTS`"
                       + originalTFOrdHed.UseOTS.ToString()
                       + "`"
                       + modifiedTFOrdHed.UseOTS.ToString().Replace("`", "-"));
        }

        // OTS Name
        if (modifiedTFOrdHed.OTSName != originalTFOrdHed.OTSName)
        {
            changes.Add("TFOrdHed.OTSName`"
                       + originalTFOrdHed.OTSName.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSName.ToString().Replace("`", "-"));
        }

        // OTS Address 1
        if (modifiedTFOrdHed.OTSAddress1 != originalTFOrdHed.OTSAddress1)
        {
            changes.Add("TFOrdHed.OTSAddress1`"
                       + originalTFOrdHed.OTSAddress1.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSAddress1.ToString().Replace("`", "-"));
        }

        // OTS Address 2
        if (modifiedTFOrdHed.OTSAddress2 != originalTFOrdHed.OTSAddress2)
        {
            changes.Add("TFOrdHed.OTSAddress2`"
                       + originalTFOrdHed.OTSAddress2.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSAddress2.ToString().Replace("`", "-"));
        }

        // OTS Address 3
        if (modifiedTFOrdHed.OTSAddress3 != originalTFOrdHed.OTSAddress3)
        {
            changes.Add("TFOrdHed.OTSAddress3`"
                       + originalTFOrdHed.OTSAddress3.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSAddress3.ToString().Replace("`", "-"));
        }

        // OTS City
        if (modifiedTFOrdHed.OTSCity != originalTFOrdHed.OTSCity)
        {
            changes.Add("TFOrdHed.OTSCity`"
                       + originalTFOrdHed.OTSCity.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSCity.ToString().Replace("`", "-"));
        }

        // OTS State
        if (modifiedTFOrdHed.OTSState != originalTFOrdHed.OTSState)
        {
            changes.Add("TFOrdHed.OTSState`"
                       + originalTFOrdHed.OTSState.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSState.ToString().Replace("`", "-"));
        }

        // OTS Zip
        if (modifiedTFOrdHed.OTSZip != originalTFOrdHed.OTSZip)
        {
            changes.Add("TFOrdHed.OTSZip`"
                       + originalTFOrdHed.OTSZip.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSZip.ToString().Replace("`", "-"));
        }

        // OTS Contact
        if (modifiedTFOrdHed.OTSContact != originalTFOrdHed.OTSContact)
        {
            changes.Add("TFOrdHed.OTSContact`"
                       + originalTFOrdHed.OTSContact.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSContact.ToString().Replace("`", "-"));
        }

        // OTS Country Number
        if (modifiedTFOrdHed.OTSCountryNum != originalTFOrdHed.OTSCountryNum)
        {
            changes.Add("TFOrdHed.OTSCountryNum`"
                       + originalTFOrdHed.OTSCountryNum.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSCountryNum.ToString().Replace("`", "-"));
        }

        // OTS Phone Number
        if (modifiedTFOrdHed.OTSPhoneNum != originalTFOrdHed.OTSPhoneNum)
        {
            changes.Add("TFOrdHed.OTSPhoneNum`"
                       + originalTFOrdHed.OTSPhoneNum.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSPhoneNum.ToString().Replace("`", "-"));
        }

        // OTS Fax Number
        if (modifiedTFOrdHed.OTSFaxNum != originalTFOrdHed.OTSFaxNum)
        {
            changes.Add("TFOrdHed.OTSFaxNum`"
                       + originalTFOrdHed.OTSFaxNum.ToString()
                       + "`"
                       + modifiedTFOrdHed.OTSFaxNum.ToString().Replace("`", "-"));
        }

        // Ready To Fulfill
        if (modifiedTFOrdHed.ReadyToFulfill != originalTFOrdHed.ReadyToFulfill)
        {
            changes.Add("TFOrdHed.ReadyToFulfill`"
                       + originalTFOrdHed.ReadyToFulfill.ToString()
                       + "`"
                       + modifiedTFOrdHed.ReadyToFulfill.ToString().Replace("`", "-"));
        }



    }
}

// =================================================================
// Process TFOrdDtl Changes
// =================================================================

// Check if TFOrdDtl data exists
if (ds.TFOrdDtl != null && ds.TFOrdDtl.Count > 0)
{
    // Process each TFOrdDtl record
    foreach (var modifiedDtl in ds.TFOrdDtl)
    {
        string tfOrdLine = modifiedDtl.TFOrdLine.ToString();

        // Check if the detail row has been deleted
        if (modifiedDtl.RowMod == "D")
        {
            changes.Add($"TFOrdDtl deleted: Line {tfOrdLine}");
            callFunc = true;
            continue;
        }

        // Get the original detail record from database
        var originalDtl = (from dbTFOrdDtl in Db.TFOrdDtl
                          where dbTFOrdDtl.Company == companyID
                             && dbTFOrdDtl.TFOrdNum.ToString() == tfOrdNum
                             && dbTFOrdDtl.TFOrdLine.ToString() == tfOrdLine
                          select dbTFOrdDtl).FirstOrDefault();

        // Handle new detail creation
        if (originalDtl == null)
        {
            changes.Add($"New TFOrdDtl created: Line {tfOrdLine}");
            callFunc = true;
            continue;
        }

        // =================================================================
        // TFOrdDtl Field Comparisons
        // =================================================================

        // Company
        if (modifiedDtl.Company != originalDtl.Company)
        {
            changes.Add($"TFOrdDtl.Company[Line {tfOrdLine}]`"
                       + originalDtl.Company.ToString()
                       + "`"
                       + modifiedDtl.Company.ToString().Replace("`", "-"));
        }

        // Transfer Order Number
        if (modifiedDtl.TFOrdNum != originalDtl.TFOrdNum)
        {
            changes.Add($"TFOrdDtl.TFOrdNum[Line {tfOrdLine}]`"
                       + originalDtl.TFOrdNum.ToString()
                       + "`"
                       + modifiedDtl.TFOrdNum.ToString().Replace("`", "-"));
        }

        // Transfer Order Line
        if (modifiedDtl.TFOrdLine != originalDtl.TFOrdLine)
        {
            changes.Add($"TFOrdDtl.TFOrdLine[Line {tfOrdLine}]`"
                       + originalDtl.TFOrdLine.ToString()
                       + "`"
                       + modifiedDtl.TFOrdLine.ToString().Replace("`", "-"));
        }

        // Open Line
        if (modifiedDtl.OpenLine != originalDtl.OpenLine)
        {
            changes.Add($"TFOrdDtl.OpenLine[Line {tfOrdLine}]`"
                       + originalDtl.OpenLine.ToString()
                       + "`"
                       + modifiedDtl.OpenLine.ToString().Replace("`", "-"));
        }

        // Part Number
        if (modifiedDtl.PartNum != originalDtl.PartNum)
        {
            changes.Add($"TFOrdDtl.PartNum[Line {tfOrdLine}]`"
                       + originalDtl.PartNum.ToString()
                       + "`"
                       + modifiedDtl.PartNum.ToString().Replace("`", "-"));
        }

        // Our Stock Quantity
        if (modifiedDtl.OurStockQty != originalDtl.OurStockQty)
        {
            changes.Add($"TFOrdDtl.OurStockQty[Line {tfOrdLine}]`"
                       + originalDtl.OurStockQty.ToString()
                       + "`"
                       + modifiedDtl.OurStockQty.ToString().Replace("`", "-"));
        }

        // Warehouse Code
        if (modifiedDtl.WarehouseCode != originalDtl.WarehouseCode)
        {
            changes.Add($"TFOrdDtl.WarehouseCode[Line {tfOrdLine}]`"
                       + originalDtl.WarehouseCode.ToString()
                       + "`"
                       + modifiedDtl.WarehouseCode.ToString().Replace("`", "-"));
        }

        // Our Stock Shipped Quantity
        if (modifiedDtl.OurStockShippedQty != originalDtl.OurStockShippedQty)
        {
            changes.Add($"TFOrdDtl.OurStockShippedQty[Line {tfOrdLine}]`"
                       + originalDtl.OurStockShippedQty.ToString()
                       + "`"
                       + modifiedDtl.OurStockShippedQty.ToString().Replace("`", "-"));
        }

        // Plant
        if (modifiedDtl.Plant != originalDtl.Plant)
        {
            changes.Add($"TFOrdDtl.Plant[Line {tfOrdLine}]`"
                       + originalDtl.Plant.ToString()
                       + "`"
                       + modifiedDtl.Plant.ToString().Replace("`", "-"));
        }

        // To Plant
        if (modifiedDtl.ToPlant != originalDtl.ToPlant)
        {
            changes.Add($"TFOrdDtl.ToPlant[Line {tfOrdLine}]`"
                       + originalDtl.ToPlant.ToString()
                       + "`"
                       + modifiedDtl.ToPlant.ToString().Replace("`", "-"));
        }

        // Order Firm
        if (modifiedDtl.OrderFirm != originalDtl.OrderFirm)
        {
            changes.Add($"TFOrdDtl.OrderFirm[Line {tfOrdLine}]`"
                       + originalDtl.OrderFirm.ToString()
                       + "`"
                       + modifiedDtl.OrderFirm.ToString().Replace("`", "-"));
        }

        // Request Date
        if (modifiedDtl.RequestDate != originalDtl.RequestDate)
        {
            changes.Add($"TFOrdDtl.RequestDate[Line {tfOrdLine}]`"
                       + originalDtl.RequestDate.ToString()
                       + "`"
                       + modifiedDtl.RequestDate.ToString().Replace("`", "-"));
        }

        // Need By Date
        if (modifiedDtl.NeedByDate != originalDtl.NeedByDate)
        {
            changes.Add($"TFOrdDtl.NeedByDate[Line {tfOrdLine}]`"
                       + originalDtl.NeedByDate.ToString()
                       + "`"
                       + modifiedDtl.NeedByDate.ToString().Replace("`", "-"));
        }

        // Shipped
        if (modifiedDtl.Shipped != originalDtl.Shipped)
        {
            changes.Add($"TFOrdDtl.Shipped[Line {tfOrdLine}]`"
                       + originalDtl.Shipped.ToString()
                       + "`"
                       + modifiedDtl.Shipped.ToString().Replace("`", "-"));
        }

        // Manual Order
        if (modifiedDtl.ManualOrder != originalDtl.ManualOrder)
        {
            changes.Add($"TFOrdDtl.ManualOrder[Line {tfOrdLine}]`"
                       + originalDtl.ManualOrder.ToString()
                       + "`"
                       + modifiedDtl.ManualOrder.ToString().Replace("`", "-"));
        }

        // Supply Job Number
        if (modifiedDtl.SupplyJobNum != originalDtl.SupplyJobNum)
        {
            changes.Add($"TFOrdDtl.SupplyJobNum[Line {tfOrdLine}]`"
                       + originalDtl.SupplyJobNum.ToString()
                       + "`"
                       + modifiedDtl.SupplyJobNum.ToString().Replace("`", "-"));
        }

        // Demand Job Number
        if (modifiedDtl.DemandJobNum != originalDtl.DemandJobNum)
        {
            changes.Add($"TFOrdDtl.DemandJobNum[Line {tfOrdLine}]`"
                       + originalDtl.DemandJobNum.ToString()
                       + "`"
                       + modifiedDtl.DemandJobNum.ToString().Replace("`", "-"));
        }

        // Ship Via Code
        if (modifiedDtl.ShipViaCode != originalDtl.ShipViaCode)
        {
            changes.Add($"TFOrdDtl.ShipViaCode[Line {tfOrdLine}]`"
                       + originalDtl.ShipViaCode.ToString()
                       + "`"
                       + modifiedDtl.ShipViaCode.ToString().Replace("`", "-"));
        }

        // Transfer Line Number
        if (modifiedDtl.TFLineNum != originalDtl.TFLineNum)
        {
            changes.Add($"TFOrdDtl.TFLineNum[Line {tfOrdLine}]`"
                       + originalDtl.TFLineNum.ToString()
                       + "`"
                       + modifiedDtl.TFLineNum.ToString().Replace("`", "-"));
        }

        // Received Quantity
        if (modifiedDtl.ReceivedQty != originalDtl.ReceivedQty)
        {
            changes.Add($"TFOrdDtl.ReceivedQty[Line {tfOrdLine}]`"
                       + originalDtl.ReceivedQty.ToString()
                       + "`"
                       + modifiedDtl.ReceivedQty.ToString().Replace("`", "-"));
        }

        // Additional Quantity
        if (modifiedDtl.AdditionalQty != originalDtl.AdditionalQty)
        {
            changes.Add($"TFOrdDtl.AdditionalQty[Line {tfOrdLine}]`"
                       + originalDtl.AdditionalQty.ToString()
                       + "`"
                       + modifiedDtl.AdditionalQty.ToString().Replace("`", "-"));
        }

        // Firm Date
        if (modifiedDtl.FirmDate != originalDtl.FirmDate)
        {
            changes.Add($"TFOrdDtl.FirmDate[Line {tfOrdLine}]`"
                       + originalDtl.FirmDate.ToString()
                       + "`"
                       + modifiedDtl.FirmDate.ToString().Replace("`", "-"));
        }

        // Firm User
        if (modifiedDtl.FirmUser != originalDtl.FirmUser)
        {
            changes.Add($"TFOrdDtl.FirmUser[Line {tfOrdLine}]`"
                       + originalDtl.FirmUser.ToString()
                       + "`"
                       + modifiedDtl.FirmUser.ToString().Replace("`", "-"));
        }

        // Demand Assembly Sequence
        if (modifiedDtl.DemandAsmSeq != originalDtl.DemandAsmSeq)
        {
            changes.Add($"TFOrdDtl.DemandAsmSeq[Line {tfOrdLine}]`"
                       + originalDtl.DemandAsmSeq.ToString()
                       + "`"
                       + modifiedDtl.DemandAsmSeq.ToString().Replace("`", "-"));
        }

        // Demand Material Sequence
        if (modifiedDtl.DemandMtlSeq != originalDtl.DemandMtlSeq)
        {
            changes.Add($"TFOrdDtl.DemandMtlSeq[Line {tfOrdLine}]`"
                       + originalDtl.DemandMtlSeq.ToString()
                       + "`"
                       + modifiedDtl.DemandMtlSeq.ToString().Replace("`", "-"));
        }

        // Supply Assembly Sequence
        if (modifiedDtl.SupplyAsmSeq != originalDtl.SupplyAsmSeq)
        {
            changes.Add($"TFOrdDtl.SupplyAsmSeq[Line {tfOrdLine}]`"
                       + originalDtl.SupplyAsmSeq.ToString()
                       + "`"
                       + modifiedDtl.SupplyAsmSeq.ToString().Replace("`", "-"));
        }

        // Supply Material Sequence
        if (modifiedDtl.SupplyMtlSeq != originalDtl.SupplyMtlSeq)
        {
            changes.Add($"TFOrdDtl.SupplyMtlSeq[Line {tfOrdLine}]`"
                       + originalDtl.SupplyMtlSeq.ToString()
                       + "`"
                       + modifiedDtl.SupplyMtlSeq.ToString().Replace("`", "-"));
        }

        // Stock Transaction
        if (modifiedDtl.StockTrans != originalDtl.StockTrans)
        {
            changes.Add($"TFOrdDtl.StockTrans[Line {tfOrdLine}]`"
                       + originalDtl.StockTrans.ToString()
                       + "`"
                       + modifiedDtl.StockTrans.ToString().Replace("`", "-"));
        }

        // Our Stock Quantity UOM
        if (modifiedDtl.OurStockQtyUOM != originalDtl.OurStockQtyUOM)
        {
            changes.Add($"TFOrdDtl.OurStockQtyUOM[Line {tfOrdLine}]`"
                       + originalDtl.OurStockQtyUOM.ToString()
                       + "`"
                       + modifiedDtl.OurStockQtyUOM.ToString().Replace("`", "-"));
        }

        // Selling Quantity UOM
        if (modifiedDtl.SellingQtyUOM != originalDtl.SellingQtyUOM)
        {
            changes.Add($"TFOrdDtl.SellingQtyUOM[Line {tfOrdLine}]`"
                       + originalDtl.SellingQtyUOM.ToString()
                       + "`"
                       + modifiedDtl.SellingQtyUOM.ToString().Replace("`", "-"));
        }

        // Selling Quantity
        if (modifiedDtl.SellingQty != originalDtl.SellingQty)
        {
            changes.Add($"TFOrdDtl.SellingQty[Line {tfOrdLine}]`"
                       + originalDtl.SellingQty.ToString()
                       + "`"
                       + modifiedDtl.SellingQty.ToString().Replace("`", "-"));
        }

        // Selling Shipped Quantity
        if (modifiedDtl.SellingShippedQty != originalDtl.SellingShippedQty)
        {
            changes.Add($"TFOrdDtl.SellingShippedQty[Line {tfOrdLine}]`"
                       + originalDtl.SellingShippedQty.ToString()
                       + "`"
                       + modifiedDtl.SellingShippedQty.ToString().Replace("`", "-"));
        }

        // Select For Picking
        if (modifiedDtl.SelectForPicking != originalDtl.SelectForPicking)
        {
            changes.Add($"TFOrdDtl.SelectForPicking[Line {tfOrdLine}]`"
                       + originalDtl.SelectForPicking.ToString()
                       + "`"
                       + modifiedDtl.SelectForPicking.ToString().Replace("`", "-"));
        }

        // Staging Warehouse Code
        if (modifiedDtl.StagingWarehouseCode != originalDtl.StagingWarehouseCode)
        {
            changes.Add($"TFOrdDtl.StagingWarehouseCode[Line {tfOrdLine}]`"
                       + originalDtl.StagingWarehouseCode.ToString()
                       + "`"
                       + modifiedDtl.StagingWarehouseCode.ToString().Replace("`", "-"));
        }

        // Staging Bin Number
        if (modifiedDtl.StagingBinNum != originalDtl.StagingBinNum)
        {
            changes.Add($"TFOrdDtl.StagingBinNum[Line {tfOrdLine}]`"
                       + originalDtl.StagingBinNum.ToString()
                       + "`"
                       + modifiedDtl.StagingBinNum.ToString().Replace("`", "-"));
        }

        // Pick Error
        if (modifiedDtl.PickError != originalDtl.PickError)
        {
            changes.Add($"TFOrdDtl.PickError[Line {tfOrdLine}]`"
                       + originalDtl.PickError.ToString()
                       + "`"
                       + modifiedDtl.PickError.ToString().Replace("`", "-"));
        }

        // Contract ID
        if (modifiedDtl.ContractID != originalDtl.ContractID)
        {
            changes.Add($"TFOrdDtl.ContractID[Line {tfOrdLine}]`"
                       + originalDtl.ContractID.ToString()
                       + "`"
                       + modifiedDtl.ContractID.ToString().Replace("`", "-"));
        }

        // Link To Contract
        if (modifiedDtl.LinkToContract != originalDtl.LinkToContract)
        {
            changes.Add($"TFOrdDtl.LinkToContract[Line {tfOrdLine}]`"
                       + originalDtl.LinkToContract.ToString()
                       + "`"
                       + modifiedDtl.LinkToContract.ToString().Replace("`", "-"));
        }

        // Transfer Contract ID
        if (modifiedDtl.TransferContractID != originalDtl.TransferContractID)
        {
            changes.Add($"TFOrdDtl.TransferContractID[Line {tfOrdLine}]`"
                       + originalDtl.TransferContractID.ToString()
                       + "`"
                       + modifiedDtl.TransferContractID.ToString().Replace("`", "-"));
        }

        // Transfer Link To Contract
        if (modifiedDtl.TransferLinkToContract != originalDtl.TransferLinkToContract)
        {
            changes.Add($"TFOrdDtl.TransferLinkToContract[Line {tfOrdLine}]`"
                       + originalDtl.TransferLinkToContract.ToString()
                       + "`"
                       + modifiedDtl.TransferLinkToContract.ToString().Replace("`", "-"));
        }

        // Epicor FSA
        if (modifiedDtl.EpicorFSA != originalDtl.EpicorFSA)
        {
            changes.Add($"TFOrdDtl.EpicorFSA[Line {tfOrdLine}]`"
                       + originalDtl.EpicorFSA.ToString()
                       + "`"
                       + modifiedDtl.EpicorFSA.ToString().Replace("`", "-"));
        }

        // Attribute Set ID
        if (modifiedDtl.AttributeSetID != originalDtl.AttributeSetID)
        {
            changes.Add($"TFOrdDtl.AttributeSetID[Line {tfOrdLine}]`"
                       + originalDtl.AttributeSetID.ToString()
                       + "`"
                       + modifiedDtl.AttributeSetID.ToString().Replace("`", "-"));
        }

        // Planning Number Of Pieces
        if (modifiedDtl.PlanningNumberOfPieces != originalDtl.PlanningNumberOfPieces)
        {
            changes.Add($"TFOrdDtl.PlanningNumberOfPieces[Line {tfOrdLine}]`"
                       + originalDtl.PlanningNumberOfPieces.ToString()
                       + "`"
                       + modifiedDtl.PlanningNumberOfPieces.ToString().Replace("`", "-"));
        }

        // Number Of Pieces
        if (modifiedDtl.NumberOfPieces != originalDtl.NumberOfPieces)
        {
            changes.Add($"TFOrdDtl.NumberOfPieces[Line {tfOrdLine}]`"
                       + originalDtl.NumberOfPieces.ToString()
                       + "`"
                       + modifiedDtl.NumberOfPieces.ToString().Replace("`", "-"));
        }

        // Revision Number
        if (modifiedDtl.RevisionNum != originalDtl.RevisionNum)
        {
            changes.Add($"TFOrdDtl.RevisionNum[Line {tfOrdLine}]`"
                       + originalDtl.RevisionNum.ToString()
                       + "`"
                       + modifiedDtl.RevisionNum.ToString().Replace("`", "-"));
        }

        // Ready To Fulfill
        if (modifiedDtl.ReadyToFulfill != originalDtl.ReadyToFulfill)
        {
            changes.Add($"TFOrdDtl.ReadyToFulfill[Line {tfOrdLine}]`"
                       + originalDtl.ReadyToFulfill.ToString()
                       + "`"
                       + modifiedDtl.ReadyToFulfill.ToString().Replace("`", "-"));
        }

    }
}

// =================================================================
// Final Processing
// =================================================================

// If changes were detected, call the function and log the changes
if (changes.Count > 0)
{
    callFunc = true;

    // Join all changes with the delimiter
    string allChanges = string.Join("`~`", changes);
    int totalLength = allChanges.Length;
    int maxChunkSize = 1000;

    // Assign chunks based on position - each exactly 1000 chars (or remaining)
    if (totalLength > 0)
    {
        changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
    }
    if (totalLength > 1000)
    {
        changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
    }
    if (totalLength > 2000)
    {
        changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
    }
    if (totalLength > 3000)
    {
        changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
    }
    if (totalLength > 4000)
    {
        changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
    }
    if (totalLength > 5000)
    {
        changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
    }
    if (totalLength > 6000)
    {
        changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
    }
    if (totalLength > 7000)
    {
        changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
    }
    if (totalLength > 8000)
    {
        changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
    }
    if (totalLength > 9000)
    {
        changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
    }

    // Set appropriate debug message
    if (totalLength > 10000)
    {
        test2 = $"Warning: {totalLength} characters of changes detected, only first 10000 logged";
    }
    else
    {
        test2 = $"Debug: {changes.Count} changes detected, {totalLength} characters logged";
    }
}
else
{
    test2 = "Debug: No changes detected";
}

// =================================================================
// End of BPM Script
// =================================================================
