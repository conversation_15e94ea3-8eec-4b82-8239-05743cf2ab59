// =================================================================
// Shipment Change Tracking BPM
// =================================================================
// This BPM tracks changes to ShipHead and ShipDtl records and logs
// them for auditing purposes. It compares the current dataset with
// the original database records to identify what has changed.
// =================================================================

// =================================================================
// Initialize Variables
// =================================================================

// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;
ShipmentNumber = "";

// Validate dataset before processing
if (ds.ShipHead == null || ds.ShipHead.Count == 0)
{
    test1 = "Error: No shipment data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.ShipHead.Count.ToString()} shipment records";

// Track all changes in a list
List<string> changes = new List<string>();

// =================================================================
// Process ShipHead Changes
// =================================================================

// Get the modified shipment record
var modifiedShipHead = ds.ShipHead[0];
int packNum = modifiedShipHead.PackNum;
ShipmentNumber = packNum.ToString();
test2 = "Debug: Successfully accessed ds.ShipHead[0]";

// Check if the shipment row has been deleted (RowMod = "D" in original record)
if (ds.ShipHead[0].RowMod == "D")
{
    changes.Add("ShipHead deleted");
    callFunc = true;
}
else
{
    // Get the original shipment record from database
    var originalShipHead = (from dbShipHead in Db.ShipHead
                           where dbShipHead.Company == companyID
                              && dbShipHead.PackNum == packNum
                           select dbShipHead).FirstOrDefault();

    // Handle new shipment creation
    if (originalShipHead == null)
    {
        changes.Add($"New shipment created: {modifiedShipHead.PackNum.ToString()}");
        callFunc = true;
    }
    else
    {
        // Determine which record to use as the modified version
        // Check if there's a second record (modified version)
        if (ds.ShipHead.Count > 1)
        {
            try
            {
                modifiedShipHead = ds.ShipHead[1];
                test2 = "Debug: Successfully accessed ds.ShipHead[1]";
            }
            catch (System.Exception ex)
            {
                test2 = $"Error accessing ds.ShipHead[1]: {ex.Message}";
                return;
            }
        }
        else
        {
            test2 = "Debug: Using ds.ShipHead[0] as modified (only 1 record)";
        }

        // =================================================================
        // ShipHead Field Comparisons
        // =================================================================

        // Company
        if (modifiedShipHead.Company != originalShipHead.Company)
        {
            changes.Add("ShipHead.Company`"
                       + originalShipHead.Company.ToString()
                       + "`"
                       + modifiedShipHead.Company.ToString().Replace("`", "-"));
        }

        // Pack Number
        if (modifiedShipHead.PackNum != originalShipHead.PackNum)
        {
            changes.Add("ShipHead.PackNum`"
                       + originalShipHead.PackNum.ToString()
                       + "`"
                       + modifiedShipHead.PackNum.ToString().Replace("`", "-"));
        }

        // Customer Number
        if (modifiedShipHead.CustNum != originalShipHead.CustNum)
        {
            changes.Add("ShipHead.CustNum`"
                       + originalShipHead.CustNum.ToString()
                       + "`"
                       + modifiedShipHead.CustNum.ToString().Replace("`", "-"));
        }

        // Ship To Number
        if (modifiedShipHead.ShipToNum != originalShipHead.ShipToNum)
        {
            changes.Add("ShipHead.ShipToNum`"
                       + originalShipHead.ShipToNum.ToString()
                       + "`"
                       + modifiedShipHead.ShipToNum.ToString().Replace("`", "-"));
        }

        // Entry Person
        if (modifiedShipHead.EntryPerson != originalShipHead.EntryPerson)
        {
            changes.Add("ShipHead.EntryPerson`"
                       + originalShipHead.EntryPerson.ToString()
                       + "`"
                       + modifiedShipHead.EntryPerson.ToString().Replace("`", "-"));
        }

        // Tracking Number
        if (modifiedShipHead.TrackingNumber != originalShipHead.TrackingNumber)
        {
            changes.Add("ShipHead.TrackingNumber`"
                       + originalShipHead.TrackingNumber.ToString()
                       + "`"
                       + modifiedShipHead.TrackingNumber.ToString().Replace("`", "-"));
        }

        // Weight
        if (modifiedShipHead.Weight != originalShipHead.Weight)
        {
            changes.Add("ShipHead.Weight`"
                       + originalShipHead.Weight.ToString()
                       + "`"
                       + modifiedShipHead.Weight.ToString().Replace("`", "-"));
        }

        // Invoiced
        if (modifiedShipHead.Invoiced != originalShipHead.Invoiced)
        {
            changes.Add("ShipHead.Invoiced`"
                       + originalShipHead.Invoiced.ToString()
                       + "`"
                       + modifiedShipHead.Invoiced.ToString().Replace("`", "-"));
        }

        // Ship Comment
        if (modifiedShipHead.ShipComment != originalShipHead.ShipComment)
        {
            changes.Add("ShipHead.ShipComment`"
                       + originalShipHead.ShipComment.ToString()
                       + "`"
                       + modifiedShipHead.ShipComment.ToString().Replace("`", "-"));
        }

        // Ship Status
        if (modifiedShipHead.ShipStatus != originalShipHead.ShipStatus)
        {
            changes.Add("ShipHead.ShipStatus`"
                       + originalShipHead.ShipStatus.ToString()
                       + "`"
                       + modifiedShipHead.ShipStatus.ToString().Replace("`", "-"));
        }

        // Ship To Customer Number
        if (modifiedShipHead.ShipToCustNum != originalShipHead.ShipToCustNum)
        {
            changes.Add("ShipHead.ShipToCustNum`"
                       + originalShipHead.ShipToCustNum.ToString()
                       + "`"
                       + modifiedShipHead.ShipToCustNum.ToString().Replace("`", "-"));
        }

        // Ready To Invoice
        if (modifiedShipHead.ReadyToInvoice != originalShipHead.ReadyToInvoice)
        {
            changes.Add("ShipHead.ReadyToInvoice`"
                       + originalShipHead.ReadyToInvoice.ToString()
                       + "`"
                       + modifiedShipHead.ReadyToInvoice.ToString().Replace("`", "-"));
        }

        // COD
        if (modifiedShipHead.COD != originalShipHead.COD)
        {
            changes.Add("ShipHead.COD`"
                       + originalShipHead.COD.ToString()
                       + "`"
                       + modifiedShipHead.COD.ToString().Replace("`", "-"));
        }

        // COD Amount
        if (modifiedShipHead.CODAmount != originalShipHead.CODAmount)
        {
            changes.Add("ShipHead.CODAmount`"
                       + originalShipHead.CODAmount.ToString()
                       + "`"
                       + modifiedShipHead.CODAmount.ToString().Replace("`", "-"));
        }

        // COD Freight
        if (modifiedShipHead.CODFreight != originalShipHead.CODFreight)
        {
            changes.Add("ShipHead.CODFreight`"
                       + originalShipHead.CODFreight.ToString()
                       + "`"
                       + modifiedShipHead.CODFreight.ToString().Replace("`", "-"));
        }

        // COD Check
        if (modifiedShipHead.CODCheck != originalShipHead.CODCheck)
        {
            changes.Add("ShipHead.CODCheck`"
                       + originalShipHead.CODCheck.ToString()
                       + "`"
                       + modifiedShipHead.CODCheck.ToString().Replace("`", "-"));
        }

        // Declared Insurance
        if (modifiedShipHead.DeclaredIns != originalShipHead.DeclaredIns)
        {
            changes.Add("ShipHead.DeclaredIns`"
                       + originalShipHead.DeclaredIns.ToString()
                       + "`"
                       + modifiedShipHead.DeclaredIns.ToString().Replace("`", "-"));
        }

        // Residential Delivery
        if (modifiedShipHead.ResDelivery != originalShipHead.ResDelivery)
        {
            changes.Add("ShipHead.ResDelivery`"
                       + originalShipHead.ResDelivery.ToString()
                       + "`"
                       + modifiedShipHead.ResDelivery.ToString().Replace("`", "-"));
        }

        // Saturday Delivery
        if (modifiedShipHead.SatDelivery != originalShipHead.SatDelivery)
        {
            changes.Add("ShipHead.SatDelivery`"
                       + originalShipHead.SatDelivery.ToString()
                       + "`"
                       + modifiedShipHead.SatDelivery.ToString().Replace("`", "-"));
        }

        // Saturday Pickup
        if (modifiedShipHead.SatPickup != originalShipHead.SatPickup)
        {
            changes.Add("ShipHead.SatPickup`"
                       + originalShipHead.SatPickup.ToString()
                       + "`"
                       + modifiedShipHead.SatPickup.ToString().Replace("`", "-"));
        }

        // Hazmat
        if (modifiedShipHead.Hazmat != originalShipHead.Hazmat)
        {
            changes.Add("ShipHead.Hazmat`"
                       + originalShipHead.Hazmat.ToString()
                       + "`"
                       + modifiedShipHead.Hazmat.ToString().Replace("`", "-"));
        }

        // Doc Only
        if (modifiedShipHead.DocOnly != originalShipHead.DocOnly)
        {
            changes.Add("ShipHead.DocOnly`"
                       + originalShipHead.DocOnly.ToString()
                       + "`"
                       + modifiedShipHead.DocOnly.ToString().Replace("`", "-"));
        }

        // Reference Notes
        if (modifiedShipHead.RefNotes != originalShipHead.RefNotes)
        {
            changes.Add("ShipHead.RefNotes`"
                       + originalShipHead.RefNotes.ToString()
                       + "`"
                       + modifiedShipHead.RefNotes.ToString().Replace("`", "-"));
        }

        // Transaction Document Type ID
        if (modifiedShipHead.TranDocTypeID != originalShipHead.TranDocTypeID)
        {
            changes.Add("ShipHead.TranDocTypeID`"
                       + originalShipHead.TranDocTypeID.ToString()
                       + "`"
                       + modifiedShipHead.TranDocTypeID.ToString().Replace("`", "-"));
        }

        // Ship Person
        if (modifiedShipHead.ShipPerson != originalShipHead.ShipPerson)
        {
            changes.Add("ShipHead.ShipPerson`"
                       + originalShipHead.ShipPerson.ToString()
                       + "`"
                       + modifiedShipHead.ShipPerson.ToString().Replace("`", "-"));
        }

        // Ship Log
        if (modifiedShipHead.ShipLog != originalShipHead.ShipLog)
        {
            changes.Add("ShipHead.ShipLog`"
                       + originalShipHead.ShipLog.ToString()
                       + "`"
                       + modifiedShipHead.ShipLog.ToString().Replace("`", "-"));
        }

        // Label Comment
        if (modifiedShipHead.LabelComment != originalShipHead.LabelComment)
        {
            changes.Add("ShipHead.LabelComment`"
                       + originalShipHead.LabelComment.ToString()
                       + "`"
                       + modifiedShipHead.LabelComment.ToString().Replace("`", "-"));
        }

        // Plant
        if (modifiedShipHead.Plant != originalShipHead.Plant)
        {
            changes.Add("ShipHead.Plant`"
                       + originalShipHead.Plant.ToString()
                       + "`"
                       + modifiedShipHead.Plant.ToString().Replace("`", "-"));
        }

        // External Delivery Note
        if (modifiedShipHead.ExternalDeliveryNote != originalShipHead.ExternalDeliveryNote)
        {
            changes.Add("ShipHead.ExternalDeliveryNote`"
                       + originalShipHead.ExternalDeliveryNote.ToString()
                       + "`"
                       + modifiedShipHead.ExternalDeliveryNote.ToString().Replace("`", "-"));
        }

        // External ID
        if (modifiedShipHead.ExternalID != originalShipHead.ExternalID)
        {
            changes.Add("ShipHead.ExternalID`"
                       + originalShipHead.ExternalID.ToString()
                       + "`"
                       + modifiedShipHead.ExternalID.ToString().Replace("`", "-"));
        }

        // IC Received
        if (modifiedShipHead.ICReceived != originalShipHead.ICReceived)
        {
            changes.Add("ShipHead.ICReceived`"
                       + originalShipHead.ICReceived.ToString()
                       + "`"
                       + modifiedShipHead.ICReceived.ToString().Replace("`", "-"));
        }

        // Cross Reference Pack Number
        if (modifiedShipHead.XRefPackNum != originalShipHead.XRefPackNum)
        {
            changes.Add("ShipHead.XRefPackNum`"
                       + originalShipHead.XRefPackNum.ToString()
                       + "`"
                       + modifiedShipHead.XRefPackNum.ToString().Replace("`", "-"));
        }

        // Bill To Customer Number
        if (modifiedShipHead.BTCustNum != originalShipHead.BTCustNum)
        {
            changes.Add("ShipHead.BTCustNum`"
                       + originalShipHead.BTCustNum.ToString()
                       + "`"
                       + modifiedShipHead.BTCustNum.ToString().Replace("`", "-"));
        }

        // Bill To Contact Number
        if (modifiedShipHead.BTConNum != originalShipHead.BTConNum)
        {
            changes.Add("ShipHead.BTConNum`"
                       + originalShipHead.BTConNum.ToString()
                       + "`"
                       + modifiedShipHead.BTConNum.ToString().Replace("`", "-"));
        }

        // Ship Group
        if (modifiedShipHead.ShipGroup != originalShipHead.ShipGroup)
        {
            changes.Add("ShipHead.ShipGroup`"
                       + originalShipHead.ShipGroup.ToString()
                       + "`"
                       + modifiedShipHead.ShipGroup.ToString().Replace("`", "-"));
        }

        // Package Code
        if (modifiedShipHead.PkgCode != originalShipHead.PkgCode)
        {
            changes.Add("ShipHead.PkgCode`"
                       + originalShipHead.PkgCode.ToString()
                       + "`"
                       + modifiedShipHead.PkgCode.ToString().Replace("`", "-"));
        }

        // Package Class
        if (modifiedShipHead.PkgClass != originalShipHead.PkgClass)
        {
            changes.Add("ShipHead.PkgClass`"
                       + originalShipHead.PkgClass.ToString()
                       + "`"
                       + modifiedShipHead.PkgClass.ToString().Replace("`", "-"));
        }

        // Verbal Confirmation
        if (modifiedShipHead.VerbalConf != originalShipHead.VerbalConf)
        {
            changes.Add("ShipHead.VerbalConf`"
                       + originalShipHead.VerbalConf.ToString()
                       + "`"
                       + modifiedShipHead.VerbalConf.ToString().Replace("`", "-"));
        }

        // Apply Charge
        if (modifiedShipHead.ApplyChrg != originalShipHead.ApplyChrg)
        {
            changes.Add("ShipHead.ApplyChrg`"
                       + originalShipHead.ApplyChrg.ToString()
                       + "`"
                       + modifiedShipHead.ApplyChrg.ToString().Replace("`", "-"));
        }

        // Charge Amount
        if (modifiedShipHead.ChrgAmount != originalShipHead.ChrgAmount)
        {
            changes.Add("ShipHead.ChrgAmount`"
                       + originalShipHead.ChrgAmount.ToString()
                       + "`"
                       + modifiedShipHead.ChrgAmount.ToString().Replace("`", "-"));
        }

        // Ground Type
        if (modifiedShipHead.GroundType != originalShipHead.GroundType)
        {
            changes.Add("ShipHead.GroundType`"
                       + originalShipHead.GroundType.ToString()
                       + "`"
                       + modifiedShipHead.GroundType.ToString().Replace("`", "-"));
        }

        // Notify Flag
        if (modifiedShipHead.NotifyFlag != originalShipHead.NotifyFlag)
        {
            changes.Add("ShipHead.NotifyFlag`"
                       + originalShipHead.NotifyFlag.ToString()
                       + "`"
                       + modifiedShipHead.NotifyFlag.ToString().Replace("`", "-"));
        }

        // Notify Email
        if (modifiedShipHead.NotifyEMail != originalShipHead.NotifyEMail)
        {
            changes.Add("ShipHead.NotifyEMail`"
                       + originalShipHead.NotifyEMail.ToString()
                       + "`"
                       + modifiedShipHead.NotifyEMail.ToString().Replace("`", "-"));
        }

        // Declared Amount
        if (modifiedShipHead.DeclaredAmt != originalShipHead.DeclaredAmt)
        {
            changes.Add("ShipHead.DeclaredAmt`"
                       + originalShipHead.DeclaredAmt.ToString()
                       + "`"
                       + modifiedShipHead.DeclaredAmt.ToString().Replace("`", "-"));
        }

        // MF Transaction Number
        if (modifiedShipHead.MFTransNum != originalShipHead.MFTransNum)
        {
            changes.Add("ShipHead.MFTransNum`"
                       + originalShipHead.MFTransNum.ToString()
                       + "`"
                       + modifiedShipHead.MFTransNum.ToString().Replace("`", "-"));
        }

        // MF Call Tag
        if (modifiedShipHead.MFCallTag != originalShipHead.MFCallTag)
        {
            changes.Add("ShipHead.MFCallTag`"
                       + originalShipHead.MFCallTag.ToString()
                       + "`"
                       + modifiedShipHead.MFCallTag.ToString().Replace("`", "-"));
        }

        // MF Pickup Number
        if (modifiedShipHead.MFPickupNum != originalShipHead.MFPickupNum)
        {
            changes.Add("ShipHead.MFPickupNum`"
                       + originalShipHead.MFPickupNum.ToString()
                       + "`"
                       + modifiedShipHead.MFPickupNum.ToString().Replace("`", "-"));
        }

        // MF Discount Freight
        if (modifiedShipHead.MFDiscFreight != originalShipHead.MFDiscFreight)
        {
            changes.Add("ShipHead.MFDiscFreight`"
                       + originalShipHead.MFDiscFreight.ToString()
                       + "`"
                       + modifiedShipHead.MFDiscFreight.ToString().Replace("`", "-"));
        }

        // MF Template
        if (modifiedShipHead.MFTemplate != originalShipHead.MFTemplate)
        {
            changes.Add("ShipHead.MFTemplate`"
                       + originalShipHead.MFTemplate.ToString()
                       + "`"
                       + modifiedShipHead.MFTemplate.ToString().Replace("`", "-"));
        }

        // MF Use 3B
        if (modifiedShipHead.MFUse3B != originalShipHead.MFUse3B)
        {
            changes.Add("ShipHead.MFUse3B`"
                       + originalShipHead.MFUse3B.ToString()
                       + "`"
                       + modifiedShipHead.MFUse3B.ToString().Replace("`", "-"));
        }

        // MF 3B Account
        if (modifiedShipHead.MF3BAccount != originalShipHead.MF3BAccount)
        {
            changes.Add("ShipHead.MF3BAccount`"
                       + originalShipHead.MF3BAccount.ToString()
                       + "`"
                       + modifiedShipHead.MF3BAccount.ToString().Replace("`", "-"));
        }

        // MF Dimensional Weight
        if (modifiedShipHead.MFDimWeight != originalShipHead.MFDimWeight)
        {
            changes.Add("ShipHead.MFDimWeight`"
                       + originalShipHead.MFDimWeight.ToString()
                       + "`"
                       + modifiedShipHead.MFDimWeight.ToString().Replace("`", "-"));
        }

        // MF Zone
        if (modifiedShipHead.MFZone != originalShipHead.MFZone)
        {
            changes.Add("ShipHead.MFZone`"
                       + originalShipHead.MFZone.ToString()
                       + "`"
                       + modifiedShipHead.MFZone.ToString().Replace("`", "-"));
        }

        // MF Freight Amount
        if (modifiedShipHead.MFFreightAmt != originalShipHead.MFFreightAmt)
        {
            changes.Add("ShipHead.MFFreightAmt`"
                       + originalShipHead.MFFreightAmt.ToString()
                       + "`"
                       + modifiedShipHead.MFFreightAmt.ToString().Replace("`", "-"));
        }

        // MF Other Amount
        if (modifiedShipHead.MFOtherAmt != originalShipHead.MFOtherAmt)
        {
            changes.Add("ShipHead.MFOtherAmt`"
                       + originalShipHead.MFOtherAmt.ToString()
                       + "`"
                       + modifiedShipHead.MFOtherAmt.ToString().Replace("`", "-"));
        }

        // MF Oversized
        if (modifiedShipHead.MFOversized != originalShipHead.MFOversized)
        {
            changes.Add("ShipHead.MFOversized`"
                       + originalShipHead.MFOversized.ToString()
                       + "`"
                       + modifiedShipHead.MFOversized.ToString().Replace("`", "-"));
        }

        // Service Saturday Delivery
        if (modifiedShipHead.ServSatDelivery != originalShipHead.ServSatDelivery)
        {
            changes.Add("ShipHead.ServSatDelivery`"
                       + originalShipHead.ServSatDelivery.ToString()
                       + "`"
                       + modifiedShipHead.ServSatDelivery.ToString().Replace("`", "-"));
        }

        // Service Saturday Pickup
        if (modifiedShipHead.ServSatPickup != originalShipHead.ServSatPickup)
        {
            changes.Add("ShipHead.ServSatPickup`"
                       + originalShipHead.ServSatPickup.ToString()
                       + "`"
                       + modifiedShipHead.ServSatPickup.ToString().Replace("`", "-"));
        }

        // Service Signature
        if (modifiedShipHead.ServSignature != originalShipHead.ServSignature)
        {
            changes.Add("ShipHead.ServSignature`"
                       + originalShipHead.ServSignature.ToString()
                       + "`"
                       + modifiedShipHead.ServSignature.ToString().Replace("`", "-"));
        }

        // Service Alert
        if (modifiedShipHead.ServAlert != originalShipHead.ServAlert)
        {
            changes.Add("ShipHead.ServAlert`"
                       + originalShipHead.ServAlert.ToString()
                       + "`"
                       + modifiedShipHead.ServAlert.ToString().Replace("`", "-"));
        }

        // Service POD
        if (modifiedShipHead.ServPOD != originalShipHead.ServPOD)
        {
            changes.Add("ShipHead.ServPOD`"
                       + originalShipHead.ServPOD.ToString()
                       + "`"
                       + modifiedShipHead.ServPOD.ToString().Replace("`", "-"));
        }

        // Service AOD
        if (modifiedShipHead.ServAOD != originalShipHead.ServAOD)
        {
            changes.Add("ShipHead.ServAOD`"
                       + originalShipHead.ServAOD.ToString()
                       + "`"
                       + modifiedShipHead.ServAOD.ToString().Replace("`", "-"));
        }

        // Service Home Delivery
        if (modifiedShipHead.ServHomeDel != originalShipHead.ServHomeDel)
        {
            changes.Add("ShipHead.ServHomeDel`"
                       + originalShipHead.ServHomeDel.ToString()
                       + "`"
                       + modifiedShipHead.ServHomeDel.ToString().Replace("`", "-"));
        }

        // Delivery Type
        if (modifiedShipHead.DeliveryType != originalShipHead.DeliveryType)
        {
            changes.Add("ShipHead.DeliveryType`"
                       + originalShipHead.DeliveryType.ToString()
                       + "`"
                       + modifiedShipHead.DeliveryType.ToString().Replace("`", "-"));
        }

        // Service Delivery Date
        if (modifiedShipHead.ServDeliveryDate != originalShipHead.ServDeliveryDate)
        {
            changes.Add("ShipHead.ServDeliveryDate`"
                       + originalShipHead.ServDeliveryDate.ToString()
                       + "`"
                       + modifiedShipHead.ServDeliveryDate.ToString().Replace("`", "-"));
        }

        // Service Phone
        if (modifiedShipHead.ServPhone != originalShipHead.ServPhone)
        {
            changes.Add("ShipHead.ServPhone`"
                       + originalShipHead.ServPhone.ToString()
                       + "`"
                       + modifiedShipHead.ServPhone.ToString().Replace("`", "-"));
        }

        // Service Instructions
        if (modifiedShipHead.ServInstruct != originalShipHead.ServInstruct)
        {
            changes.Add("ShipHead.ServInstruct`"
                       + originalShipHead.ServInstruct.ToString()
                       + "`"
                       + modifiedShipHead.ServInstruct.ToString().Replace("`", "-"));
        }

        // Service Release
        if (modifiedShipHead.ServRelease != originalShipHead.ServRelease)
        {
            changes.Add("ShipHead.ServRelease`"
                       + originalShipHead.ServRelease.ToString()
                       + "`"
                       + modifiedShipHead.ServRelease.ToString().Replace("`", "-"));
        }

        // Service Authorization Number
        if (modifiedShipHead.ServAuthNum != originalShipHead.ServAuthNum)
        {
            changes.Add("ShipHead.ServAuthNum`"
                       + originalShipHead.ServAuthNum.ToString()
                       + "`"
                       + modifiedShipHead.ServAuthNum.ToString().Replace("`", "-"));
        }

        // Service Reference 1
        if (modifiedShipHead.ServRef1 != originalShipHead.ServRef1)
        {
            changes.Add("ShipHead.ServRef1`"
                       + originalShipHead.ServRef1.ToString()
                       + "`"
                       + modifiedShipHead.ServRef1.ToString().Replace("`", "-"));
        }

        // Service Reference 2
        if (modifiedShipHead.ServRef2 != originalShipHead.ServRef2)
        {
            changes.Add("ShipHead.ServRef2`"
                       + originalShipHead.ServRef2.ToString()
                       + "`"
                       + modifiedShipHead.ServRef2.ToString().Replace("`", "-"));
        }

        // Service Reference 3
        if (modifiedShipHead.ServRef3 != originalShipHead.ServRef3)
        {
            changes.Add("ShipHead.ServRef3`"
                       + originalShipHead.ServRef3.ToString()
                       + "`"
                       + modifiedShipHead.ServRef3.ToString().Replace("`", "-"));
        }

        // Service Reference 4
        if (modifiedShipHead.ServRef4 != originalShipHead.ServRef4)
        {
            changes.Add("ShipHead.ServRef4`"
                       + originalShipHead.ServRef4.ToString()
                       + "`"
                       + modifiedShipHead.ServRef4.ToString().Replace("`", "-"));
        }

        // Service Reference 5
        if (modifiedShipHead.ServRef5 != originalShipHead.ServRef5)
        {
            changes.Add("ShipHead.ServRef5`"
                       + originalShipHead.ServRef5.ToString()
                       + "`"
                       + modifiedShipHead.ServRef5.ToString().Replace("`", "-"));
        }

        // BOL Number
        if (modifiedShipHead.BOLNum != originalShipHead.BOLNum)
        {
            changes.Add("ShipHead.BOLNum`"
                       + originalShipHead.BOLNum.ToString()
                       + "`"
                       + modifiedShipHead.BOLNum.ToString().Replace("`", "-"));
        }

        // BOL Line
        if (modifiedShipHead.BOLLine != originalShipHead.BOLLine)
        {
            changes.Add("ShipHead.BOLLine`"
                       + originalShipHead.BOLLine.ToString()
                       + "`"
                       + modifiedShipHead.BOLLine.ToString().Replace("`", "-"));
        }

        // Commercial Invoice
        if (modifiedShipHead.CommercialInvoice != originalShipHead.CommercialInvoice)
        {
            changes.Add("ShipHead.CommercialInvoice`"
                       + originalShipHead.CommercialInvoice.ToString()
                       + "`"
                       + modifiedShipHead.CommercialInvoice.ToString().Replace("`", "-"));
        }

        // Ship Export Declaration
        if (modifiedShipHead.ShipExprtDeclartn != originalShipHead.ShipExprtDeclartn)
        {
            changes.Add("ShipHead.ShipExprtDeclartn`"
                       + originalShipHead.ShipExprtDeclartn.ToString()
                       + "`"
                       + modifiedShipHead.ShipExprtDeclartn.ToString().Replace("`", "-"));
        }

        // Certificate of Origin
        if (modifiedShipHead.CertOfOrigin != originalShipHead.CertOfOrigin)
        {
            changes.Add("ShipHead.CertOfOrigin`"
                       + originalShipHead.CertOfOrigin.ToString()
                       + "`"
                       + modifiedShipHead.CertOfOrigin.ToString().Replace("`", "-"));
        }

        // Letter of Instructions
        if (modifiedShipHead.LetterOfInstr != originalShipHead.LetterOfInstr)
        {
            changes.Add("ShipHead.LetterOfInstr`"
                       + originalShipHead.LetterOfInstr.ToString()
                       + "`"
                       + modifiedShipHead.LetterOfInstr.ToString().Replace("`", "-"));
        }

        // Hazardous Shipment
        if (modifiedShipHead.HazardousShipment != originalShipHead.HazardousShipment)
        {
            changes.Add("ShipHead.HazardousShipment`"
                       + originalShipHead.HazardousShipment.ToString()
                       + "`"
                       + modifiedShipHead.HazardousShipment.ToString().Replace("`", "-"));
        }

        // International Ship
        if (modifiedShipHead.IntrntlShip != originalShipHead.IntrntlShip)
        {
            changes.Add("ShipHead.IntrntlShip`"
                       + originalShipHead.IntrntlShip.ToString()
                       + "`"
                       + modifiedShipHead.IntrntlShip.ToString().Replace("`", "-"));
        }

        // Pay Flag
        if (modifiedShipHead.PayFlag != originalShipHead.PayFlag)
        {
            changes.Add("ShipHead.PayFlag`"
                       + originalShipHead.PayFlag.ToString()
                       + "`"
                       + modifiedShipHead.PayFlag.ToString().Replace("`", "-"));
        }

        // Pay Account
        if (modifiedShipHead.PayAccount != originalShipHead.PayAccount)
        {
            changes.Add("ShipHead.PayAccount`"
                       + originalShipHead.PayAccount.ToString()
                       + "`"
                       + modifiedShipHead.PayAccount.ToString().Replace("`", "-"));
        }

        // Pay BT Address 1
        if (modifiedShipHead.PayBTAddress1 != originalShipHead.PayBTAddress1)
        {
            changes.Add("ShipHead.PayBTAddress1`"
                       + originalShipHead.PayBTAddress1.ToString()
                       + "`"
                       + modifiedShipHead.PayBTAddress1.ToString().Replace("`", "-"));
        }

        // Pay BT Address 2
        if (modifiedShipHead.PayBTAddress2 != originalShipHead.PayBTAddress2)
        {
            changes.Add("ShipHead.PayBTAddress2`"
                       + originalShipHead.PayBTAddress2.ToString()
                       + "`"
                       + modifiedShipHead.PayBTAddress2.ToString().Replace("`", "-"));
        }

        // Pay BT City
        if (modifiedShipHead.PayBTCity != originalShipHead.PayBTCity)
        {
            changes.Add("ShipHead.PayBTCity`"
                       + originalShipHead.PayBTCity.ToString()
                       + "`"
                       + modifiedShipHead.PayBTCity.ToString().Replace("`", "-"));
        }

        // Pay BT State
        if (modifiedShipHead.PayBTState != originalShipHead.PayBTState)
        {
            changes.Add("ShipHead.PayBTState`"
                       + originalShipHead.PayBTState.ToString()
                       + "`"
                       + modifiedShipHead.PayBTState.ToString().Replace("`", "-"));
        }

        // Pay BT Zip
        if (modifiedShipHead.PayBTZip != originalShipHead.PayBTZip)
        {
            changes.Add("ShipHead.PayBTZip`"
                       + originalShipHead.PayBTZip.ToString()
                       + "`"
                       + modifiedShipHead.PayBTZip.ToString().Replace("`", "-"));
        }

        // Pay BT Country
        if (modifiedShipHead.PayBTCountry != originalShipHead.PayBTCountry)
        {
            changes.Add("ShipHead.PayBTCountry`"
                       + originalShipHead.PayBTCountry.ToString()
                       + "`"
                       + modifiedShipHead.PayBTCountry.ToString().Replace("`", "-"));
        }

        // FF Address 1
        if (modifiedShipHead.FFAddress1 != originalShipHead.FFAddress1)
        {
            changes.Add("ShipHead.FFAddress1`"
                       + originalShipHead.FFAddress1.ToString()
                       + "`"
                       + modifiedShipHead.FFAddress1.ToString().Replace("`", "-"));
        }

        // FF Address 2
        if (modifiedShipHead.FFAddress2 != originalShipHead.FFAddress2)
        {
            changes.Add("ShipHead.FFAddress2`"
                       + originalShipHead.FFAddress2.ToString()
                       + "`"
                       + modifiedShipHead.FFAddress2.ToString().Replace("`", "-"));
        }

        // FF City
        if (modifiedShipHead.FFCity != originalShipHead.FFCity)
        {
            changes.Add("ShipHead.FFCity`"
                       + originalShipHead.FFCity.ToString()
                       + "`"
                       + modifiedShipHead.FFCity.ToString().Replace("`", "-"));
        }

        // FF State
        if (modifiedShipHead.FFState != originalShipHead.FFState)
        {
            changes.Add("ShipHead.FFState`"
                       + originalShipHead.FFState.ToString()
                       + "`"
                       + modifiedShipHead.FFState.ToString().Replace("`", "-"));
        }

        // FF Zip
        if (modifiedShipHead.FFZip != originalShipHead.FFZip)
        {
            changes.Add("ShipHead.FFZip`"
                       + originalShipHead.FFZip.ToString()
                       + "`"
                       + modifiedShipHead.FFZip.ToString().Replace("`", "-"));
        }

        // FF Country
        if (modifiedShipHead.FFCountry != originalShipHead.FFCountry)
        {
            changes.Add("ShipHead.FFCountry`"
                       + originalShipHead.FFCountry.ToString()
                       + "`"
                       + modifiedShipHead.FFCountry.ToString().Replace("`", "-"));
        }

        // FF Contact
        if (modifiedShipHead.FFContact != originalShipHead.FFContact)
        {
            changes.Add("ShipHead.FFContact`"
                       + originalShipHead.FFContact.ToString()
                       + "`"
                       + modifiedShipHead.FFContact.ToString().Replace("`", "-"));
        }

        // FF Company Name
        if (modifiedShipHead.FFCompName != originalShipHead.FFCompName)
        {
            changes.Add("ShipHead.FFCompName`"
                       + originalShipHead.FFCompName.ToString()
                       + "`"
                       + modifiedShipHead.FFCompName.ToString().Replace("`", "-"));
        }

        // FF Phone Number
        if (modifiedShipHead.FFPhoneNum != originalShipHead.FFPhoneNum)
        {
            changes.Add("ShipHead.FFPhoneNum`"
                       + originalShipHead.FFPhoneNum.ToString()
                       + "`"
                       + modifiedShipHead.FFPhoneNum.ToString().Replace("`", "-"));
        }

        // Changed By
        if (modifiedShipHead.ChangedBy != originalShipHead.ChangedBy)
        {
            changes.Add("ShipHead.ChangedBy`"
                       + originalShipHead.ChangedBy.ToString()
                       + "`"
                       + modifiedShipHead.ChangedBy.ToString().Replace("`", "-"));
        }

        // Change Date
        if (modifiedShipHead.ChangeDate != originalShipHead.ChangeDate)
        {
            changes.Add("ShipHead.ChangeDate`"
                       + originalShipHead.ChangeDate.ToString()
                       + "`"
                       + modifiedShipHead.ChangeDate.ToString().Replace("`", "-"));
        }

        // Change Time
        if (modifiedShipHead.ChangeTime != originalShipHead.ChangeTime)
        {
            changes.Add("ShipHead.ChangeTime`"
                       + originalShipHead.ChangeTime.ToString()
                       + "`"
                       + modifiedShipHead.ChangeTime.ToString().Replace("`", "-"));
        }

        // FF ID
        if (modifiedShipHead.FFID != originalShipHead.FFID)
        {
            changes.Add("ShipHead.FFID`"
                       + originalShipHead.FFID.ToString()
                       + "`"
                       + modifiedShipHead.FFID.ToString().Replace("`", "-"));
        }

        // FF Address 3
        if (modifiedShipHead.FFAddress3 != originalShipHead.FFAddress3)
        {
            changes.Add("ShipHead.FFAddress3`"
                       + originalShipHead.FFAddress3.ToString()
                       + "`"
                       + modifiedShipHead.FFAddress3.ToString().Replace("`", "-"));
        }

        // Delivery Confirmation
        if (modifiedShipHead.DeliveryConf != originalShipHead.DeliveryConf)
        {
            changes.Add("ShipHead.DeliveryConf`"
                       + originalShipHead.DeliveryConf.ToString()
                       + "`"
                       + modifiedShipHead.DeliveryConf.ToString().Replace("`", "-"));
        }

        // Additional Handling Flag
        if (modifiedShipHead.AddlHdlgFlag != originalShipHead.AddlHdlgFlag)
        {
            changes.Add("ShipHead.AddlHdlgFlag`"
                       + originalShipHead.AddlHdlgFlag.ToString()
                       + "`"
                       + modifiedShipHead.AddlHdlgFlag.ToString().Replace("`", "-"));
        }

        // Non Standard Package
        if (modifiedShipHead.NonStdPkg != originalShipHead.NonStdPkg)
        {
            changes.Add("ShipHead.NonStdPkg`"
                       + originalShipHead.NonStdPkg.ToString()
                       + "`"
                       + modifiedShipHead.NonStdPkg.ToString().Replace("`", "-"));
        }

        // FF Country Number
        if (modifiedShipHead.FFCountryNum != originalShipHead.FFCountryNum)
        {
            changes.Add("ShipHead.FFCountryNum`"
                       + originalShipHead.FFCountryNum.ToString()
                       + "`"
                       + modifiedShipHead.FFCountryNum.ToString().Replace("`", "-"));
        }

        // Pay BT Address 3
        if (modifiedShipHead.PayBTAddress3 != originalShipHead.PayBTAddress3)
        {
            changes.Add("ShipHead.PayBTAddress3`"
                       + originalShipHead.PayBTAddress3.ToString()
                       + "`"
                       + modifiedShipHead.PayBTAddress3.ToString().Replace("`", "-"));
        }

        // Pay BT Country Number
        if (modifiedShipHead.PayBTCountryNum != originalShipHead.PayBTCountryNum)
        {
            changes.Add("ShipHead.PayBTCountryNum`"
                       + originalShipHead.PayBTCountryNum.ToString()
                       + "`"
                       + modifiedShipHead.PayBTCountryNum.ToString().Replace("`", "-"));
        }

        // Pay BT Phone
        if (modifiedShipHead.PayBTPhone != originalShipHead.PayBTPhone)
        {
            changes.Add("ShipHead.PayBTPhone`"
                       + originalShipHead.PayBTPhone.ToString()
                       + "`"
                       + modifiedShipHead.PayBTPhone.ToString().Replace("`", "-"));
        }

        // Way Bill Number
        if (modifiedShipHead.WayBillNbr != originalShipHead.WayBillNbr)
        {
            changes.Add("ShipHead.WayBillNbr`"
                       + originalShipHead.WayBillNbr.ToString()
                       + "`"
                       + modifiedShipHead.WayBillNbr.ToString().Replace("`", "-"));
        }

        // Freighted Ship Via Code
        if (modifiedShipHead.FreightedShipViaCode != originalShipHead.FreightedShipViaCode)
        {
            changes.Add("ShipHead.FreightedShipViaCode`"
                       + originalShipHead.FreightedShipViaCode.ToString()
                       + "`"
                       + modifiedShipHead.FreightedShipViaCode.ToString().Replace("`", "-"));
        }

        // UPS Quantum View
        if (modifiedShipHead.UPSQuantumView != originalShipHead.UPSQuantumView)
        {
            changes.Add("ShipHead.UPSQuantumView`"
                       + originalShipHead.UPSQuantumView.ToString()
                       + "`"
                       + modifiedShipHead.UPSQuantumView.ToString().Replace("`", "-"));
        }

        // UPS QV Ship From Name
        if (modifiedShipHead.UPSQVShipFromName != originalShipHead.UPSQVShipFromName)
        {
            changes.Add("ShipHead.UPSQVShipFromName`"
                       + originalShipHead.UPSQVShipFromName.ToString()
                       + "`"
                       + modifiedShipHead.UPSQVShipFromName.ToString().Replace("`", "-"));
        }

        // UPS QV Memo
        if (modifiedShipHead.UPSQVMemo != originalShipHead.UPSQVMemo)
        {
            changes.Add("ShipHead.UPSQVMemo`"
                       + originalShipHead.UPSQVMemo.ToString()
                       + "`"
                       + modifiedShipHead.UPSQVMemo.ToString().Replace("`", "-"));
        }

        // Package Length
        if (modifiedShipHead.PkgLength != originalShipHead.PkgLength)
        {
            changes.Add("ShipHead.PkgLength`"
                       + originalShipHead.PkgLength.ToString()
                       + "`"
                       + modifiedShipHead.PkgLength.ToString().Replace("`", "-"));
        }

        // Package Width
        if (modifiedShipHead.PkgWidth != originalShipHead.PkgWidth)
        {
            changes.Add("ShipHead.PkgWidth`"
                       + originalShipHead.PkgWidth.ToString()
                       + "`"
                       + modifiedShipHead.PkgWidth.ToString().Replace("`", "-"));
        }

        // Package Height
        if (modifiedShipHead.PkgHeight != originalShipHead.PkgHeight)
        {
            changes.Add("ShipHead.PkgHeight`"
                       + originalShipHead.PkgHeight.ToString()
                       + "`"
                       + modifiedShipHead.PkgHeight.ToString().Replace("`", "-"));
        }

        // EDI Ready
        if (modifiedShipHead.EDIReady != originalShipHead.EDIReady)
        {
            changes.Add("ShipHead.EDIReady`"
                       + originalShipHead.EDIReady.ToString()
                       + "`"
                       + modifiedShipHead.EDIReady.ToString().Replace("`", "-"));
        }

        // Package Size UOM
        if (modifiedShipHead.PkgSizeUOM != originalShipHead.PkgSizeUOM)
        {
            changes.Add("ShipHead.PkgSizeUOM`"
                       + originalShipHead.PkgSizeUOM.ToString()
                       + "`"
                       + modifiedShipHead.PkgSizeUOM.ToString().Replace("`", "-"));
        }

        // Weight UOM
        if (modifiedShipHead.WeightUOM != originalShipHead.WeightUOM)
        {
            changes.Add("ShipHead.WeightUOM`"
                       + originalShipHead.WeightUOM.ToString()
                       + "`"
                       + modifiedShipHead.WeightUOM.ToString().Replace("`", "-"));
        }

        // Document Printed
        if (modifiedShipHead.DocumentPrinted != originalShipHead.DocumentPrinted)
        {
            changes.Add("ShipHead.DocumentPrinted`"
                       + originalShipHead.DocumentPrinted.ToString()
                       + "`"
                       + modifiedShipHead.DocumentPrinted.ToString().Replace("`", "-"));
        }

        // OTS Order Number
        if (modifiedShipHead.OTSOrderNum != originalShipHead.OTSOrderNum)
        {
            changes.Add("ShipHead.OTSOrderNum`"
                       + originalShipHead.OTSOrderNum.ToString()
                       + "`"
                       + modifiedShipHead.OTSOrderNum.ToString().Replace("`", "-"));
        }

        // Tax Calculated
        if (modifiedShipHead.TaxCalculated != originalShipHead.TaxCalculated)
        {
            changes.Add("ShipHead.TaxCalculated`"
                       + originalShipHead.TaxCalculated.ToString()
                       + "`"
                       + modifiedShipHead.TaxCalculated.ToString().Replace("`", "-"));
        }

        // Tax Calculation Date
        if (modifiedShipHead.TaxCalcDate != originalShipHead.TaxCalcDate)
        {
            changes.Add("ShipHead.TaxCalcDate`"
                       + originalShipHead.TaxCalcDate.ToString()
                       + "`"
                       + modifiedShipHead.TaxCalcDate.ToString().Replace("`", "-"));
        }

        // Rounding
        if (modifiedShipHead.Rounding != originalShipHead.Rounding)
        {
            changes.Add("ShipHead.Rounding`"
                       + originalShipHead.Rounding.ToString()
                       + "`"
                       + modifiedShipHead.Rounding.ToString().Replace("`", "-"));
        }

        // Report 1 Rounding
        if (modifiedShipHead.Rpt1Rounding != originalShipHead.Rpt1Rounding)
        {
            changes.Add("ShipHead.Rpt1Rounding`"
                       + originalShipHead.Rpt1Rounding.ToString()
                       + "`"
                       + modifiedShipHead.Rpt1Rounding.ToString().Replace("`", "-"));
        }

        // Report 2 Rounding
        if (modifiedShipHead.Rpt2Rounding != originalShipHead.Rpt2Rounding)
        {
            changes.Add("ShipHead.Rpt2Rounding`"
                       + originalShipHead.Rpt2Rounding.ToString()
                       + "`"
                       + modifiedShipHead.Rpt2Rounding.ToString().Replace("`", "-"));
        }

        // Report 3 Rounding
        if (modifiedShipHead.Rpt3Rounding != originalShipHead.Rpt3Rounding)
        {
            changes.Add("ShipHead.Rpt3Rounding`"
                       + originalShipHead.Rpt3Rounding.ToString()
                       + "`"
                       + modifiedShipHead.Rpt3Rounding.ToString().Replace("`", "-"));
        }

        // Document Rounding
        if (modifiedShipHead.DocRounding != originalShipHead.DocRounding)
        {
            changes.Add("ShipHead.DocRounding`"
                       + originalShipHead.DocRounding.ToString()
                       + "`"
                       + modifiedShipHead.DocRounding.ToString().Replace("`", "-"));
        }



        // Report 1 Total Tax
        if (modifiedShipHead.Rpt1TotalTax != originalShipHead.Rpt1TotalTax)
        {
            changes.Add("ShipHead.Rpt1TotalTax`"
                       + originalShipHead.Rpt1TotalTax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt1TotalTax.ToString().Replace("`", "-"));
        }

        // Report 2 Total Tax
        if (modifiedShipHead.Rpt2TotalTax != originalShipHead.Rpt2TotalTax)
        {
            changes.Add("ShipHead.Rpt2TotalTax`"
                       + originalShipHead.Rpt2TotalTax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt2TotalTax.ToString().Replace("`", "-"));
        }

        // Report 3 Total Tax
        if (modifiedShipHead.Rpt3TotalTax != originalShipHead.Rpt3TotalTax)
        {
            changes.Add("ShipHead.Rpt3TotalTax`"
                       + originalShipHead.Rpt3TotalTax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt3TotalTax.ToString().Replace("`", "-"));
        }

        // Order Amount
        if (modifiedShipHead.OrderAmt != originalShipHead.OrderAmt)
        {
            changes.Add("ShipHead.OrderAmt`"
                       + originalShipHead.OrderAmt.ToString()
                       + "`"
                       + modifiedShipHead.OrderAmt.ToString().Replace("`", "-"));
        }

        // Document Order Amount
        if (modifiedShipHead.DocOrderAmt != originalShipHead.DocOrderAmt)
        {
            changes.Add("ShipHead.DocOrderAmt`"
                       + originalShipHead.DocOrderAmt.ToString()
                       + "`"
                       + modifiedShipHead.DocOrderAmt.ToString().Replace("`", "-"));
        }

        // Report 1 Order Amount
        if (modifiedShipHead.Rpt1OrderAmt != originalShipHead.Rpt1OrderAmt)
        {
            changes.Add("ShipHead.Rpt1OrderAmt`"
                       + originalShipHead.Rpt1OrderAmt.ToString()
                       + "`"
                       + modifiedShipHead.Rpt1OrderAmt.ToString().Replace("`", "-"));
        }

        // Report 2 Order Amount
        if (modifiedShipHead.Rpt2OrderAmt != originalShipHead.Rpt2OrderAmt)
        {
            changes.Add("ShipHead.Rpt2OrderAmt`"
                       + originalShipHead.Rpt2OrderAmt.ToString()
                       + "`"
                       + modifiedShipHead.Rpt2OrderAmt.ToString().Replace("`", "-"));
        }

        // Report 3 Order Amount
        if (modifiedShipHead.Rpt3OrderAmt != originalShipHead.Rpt3OrderAmt)
        {
            changes.Add("ShipHead.Rpt3OrderAmt`"
                       + originalShipHead.Rpt3OrderAmt.ToString()
                       + "`"
                       + modifiedShipHead.Rpt3OrderAmt.ToString().Replace("`", "-"));
        }

        // Total Withholding Tax
        if (modifiedShipHead.TotalWHTax != originalShipHead.TotalWHTax)
        {
            changes.Add("ShipHead.TotalWHTax`"
                       + originalShipHead.TotalWHTax.ToString()
                       + "`"
                       + modifiedShipHead.TotalWHTax.ToString().Replace("`", "-"));
        }

        // Document Total Withholding Tax
        if (modifiedShipHead.DocTotalWHTax != originalShipHead.DocTotalWHTax)
        {
            changes.Add("ShipHead.DocTotalWHTax`"
                       + originalShipHead.DocTotalWHTax.ToString()
                       + "`"
                       + modifiedShipHead.DocTotalWHTax.ToString().Replace("`", "-"));
        }

        // Report 1 Total Withholding Tax
        if (modifiedShipHead.Rpt1TotalWHTax != originalShipHead.Rpt1TotalWHTax)
        {
            changes.Add("ShipHead.Rpt1TotalWHTax`"
                       + originalShipHead.Rpt1TotalWHTax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt1TotalWHTax.ToString().Replace("`", "-"));
        }

        // Report 2 Total Withholding Tax
        if (modifiedShipHead.Rpt2TotalWHTax != originalShipHead.Rpt2TotalWHTax)
        {
            changes.Add("ShipHead.Rpt2TotalWHTax`"
                       + originalShipHead.Rpt2TotalWHTax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt2TotalWHTax.ToString().Replace("`", "-"));
        }

        // Report 3 Total Withholding Tax
        if (modifiedShipHead.Rpt3TotalWHTax != originalShipHead.Rpt3TotalWHTax)
        {
            changes.Add("ShipHead.Rpt3TotalWHTax`"
                       + originalShipHead.Rpt3TotalWHTax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt3TotalWHTax.ToString().Replace("`", "-"));
        }

        // Total Sales Tax
        if (modifiedShipHead.TotalSATax != originalShipHead.TotalSATax)
        {
            changes.Add("ShipHead.TotalSATax`"
                       + originalShipHead.TotalSATax.ToString()
                       + "`"
                       + modifiedShipHead.TotalSATax.ToString().Replace("`", "-"));
        }

        // Document Total Sales Tax
        if (modifiedShipHead.DocTotalSATax != originalShipHead.DocTotalSATax)
        {
            changes.Add("ShipHead.DocTotalSATax`"
                       + originalShipHead.DocTotalSATax.ToString()
                       + "`"
                       + modifiedShipHead.DocTotalSATax.ToString().Replace("`", "-"));
        }

        // Report 1 Total Sales Tax
        if (modifiedShipHead.Rpt1TotalSATax != originalShipHead.Rpt1TotalSATax)
        {
            changes.Add("ShipHead.Rpt1TotalSATax`"
                       + originalShipHead.Rpt1TotalSATax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt1TotalSATax.ToString().Replace("`", "-"));
        }

        // Report 2 Total Sales Tax
        if (modifiedShipHead.Rpt2TotalSATax != originalShipHead.Rpt2TotalSATax)
        {
            changes.Add("ShipHead.Rpt2TotalSATax`"
                       + originalShipHead.Rpt2TotalSATax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt2TotalSATax.ToString().Replace("`", "-"));
        }

        // Report 3 Total Sales Tax
        if (modifiedShipHead.Rpt3TotalSATax != originalShipHead.Rpt3TotalSATax)
        {
            changes.Add("ShipHead.Rpt3TotalSATax`"
                       + originalShipHead.Rpt3TotalSATax.ToString()
                       + "`"
                       + modifiedShipHead.Rpt3TotalSATax.ToString().Replace("`", "-"));
        }

        // Total Tax
        if (modifiedShipHead.TotalTax != originalShipHead.TotalTax)
        {
            changes.Add("ShipHead.TotalTax`"
                       + originalShipHead.TotalTax.ToString()
                       + "`"
                       + modifiedShipHead.TotalTax.ToString().Replace("`", "-"));
        }

        // Document Total Tax
        if (modifiedShipHead.DocTotalTax != originalShipHead.DocTotalTax)
        {
            changes.Add("ShipHead.DocTotalTax`"
                       + originalShipHead.DocTotalTax.ToString()
                       + "`"
                       + modifiedShipHead.DocTotalTax.ToString().Replace("`", "-"));
        }

        // Total Discount
        if (modifiedShipHead.TotalDiscount != originalShipHead.TotalDiscount)
        {
            changes.Add("ShipHead.TotalDiscount`"
                       + originalShipHead.TotalDiscount.ToString()
                       + "`"
                       + modifiedShipHead.TotalDiscount.ToString().Replace("`", "-"));
        }

        // Report 1 Total Discount
        if (modifiedShipHead.Rpt1TotalDiscount != originalShipHead.Rpt1TotalDiscount)
        {
            changes.Add("ShipHead.Rpt1TotalDiscount`"
                       + originalShipHead.Rpt1TotalDiscount.ToString()
                       + "`"
                       + modifiedShipHead.Rpt1TotalDiscount.ToString().Replace("`", "-"));
        }

        // Report 2 Total Discount
        if (modifiedShipHead.Rpt2TotalDiscount != originalShipHead.Rpt2TotalDiscount)
        {
            changes.Add("ShipHead.Rpt2TotalDiscount`"
                       + originalShipHead.Rpt2TotalDiscount.ToString()
                       + "`"
                       + modifiedShipHead.Rpt2TotalDiscount.ToString().Replace("`", "-"));
        }

        // Report 3 Total Discount
        if (modifiedShipHead.Rpt3TotalDiscount != originalShipHead.Rpt3TotalDiscount)
        {
            changes.Add("ShipHead.Rpt3TotalDiscount`"
                       + originalShipHead.Rpt3TotalDiscount.ToString()
                       + "`"
                       + modifiedShipHead.Rpt3TotalDiscount.ToString().Replace("`", "-"));
        }

        // Document Total Discount
        if (modifiedShipHead.DocTotalDiscount != originalShipHead.DocTotalDiscount)
        {
            changes.Add("ShipHead.DocTotalDiscount`"
                       + originalShipHead.DocTotalDiscount.ToString()
                       + "`"
                       + modifiedShipHead.DocTotalDiscount.ToString().Replace("`", "-"));
        }

        // Device UOM
        if (modifiedShipHead.DeviceUOM != originalShipHead.DeviceUOM)
        {
            changes.Add("ShipHead.DeviceUOM`"
                       + originalShipHead.DeviceUOM.ToString()
                       + "`"
                       + modifiedShipHead.DeviceUOM.ToString().Replace("`", "-"));
        }

        // Manifest Size UOM
        if (modifiedShipHead.ManifestSizeUOM != originalShipHead.ManifestSizeUOM)
        {
            changes.Add("ShipHead.ManifestSizeUOM`"
                       + originalShipHead.ManifestSizeUOM.ToString()
                       + "`"
                       + modifiedShipHead.ManifestSizeUOM.ToString().Replace("`", "-"));
        }

        // Manifest Weight UOM
        if (modifiedShipHead.ManifestWtUOM != originalShipHead.ManifestWtUOM)
        {
            changes.Add("ShipHead.ManifestWtUOM`"
                       + originalShipHead.ManifestWtUOM.ToString()
                       + "`"
                       + modifiedShipHead.ManifestWtUOM.ToString().Replace("`", "-"));
        }

        // Manifest Weight
        if (modifiedShipHead.ManifestWeight != originalShipHead.ManifestWeight)
        {
            changes.Add("ShipHead.ManifestWeight`"
                       + originalShipHead.ManifestWeight.ToString()
                       + "`"
                       + modifiedShipHead.ManifestWeight.ToString().Replace("`", "-"));
        }

        // Manifest Length
        if (modifiedShipHead.ManifestLength != originalShipHead.ManifestLength)
        {
            changes.Add("ShipHead.ManifestLength`"
                       + originalShipHead.ManifestLength.ToString()
                       + "`"
                       + modifiedShipHead.ManifestLength.ToString().Replace("`", "-"));
        }

        // Manifest Width
        if (modifiedShipHead.ManifestWidth != originalShipHead.ManifestWidth)
        {
            changes.Add("ShipHead.ManifestWidth`"
                       + originalShipHead.ManifestWidth.ToString()
                       + "`"
                       + modifiedShipHead.ManifestWidth.ToString().Replace("`", "-"));
        }

        // Manifest Height
        if (modifiedShipHead.ManifestHeight != originalShipHead.ManifestHeight)
        {
            changes.Add("ShipHead.ManifestHeight`"
                       + originalShipHead.ManifestHeight.ToString()
                       + "`"
                       + modifiedShipHead.ManifestHeight.ToString().Replace("`", "-"));
        }

        // Rate Group Code
        if (modifiedShipHead.RateGrpCode != originalShipHead.RateGrpCode)
        {
            changes.Add("ShipHead.RateGrpCode`"
                       + originalShipHead.RateGrpCode.ToString()
                       + "`"
                       + modifiedShipHead.RateGrpCode.ToString().Replace("`", "-"));
        }

        // In Price
        if (modifiedShipHead.InPrice != originalShipHead.InPrice)
        {
            changes.Add("ShipHead.InPrice`"
                       + originalShipHead.InPrice.ToString()
                       + "`"
                       + modifiedShipHead.InPrice.ToString().Replace("`", "-"));
        }

        // PB Hold No Invoice
        if (modifiedShipHead.PBHoldNoInv != originalShipHead.PBHoldNoInv)
        {
            changes.Add("ShipHead.PBHoldNoInv`"
                       + originalShipHead.PBHoldNoInv.ToString()
                       + "`"
                       + modifiedShipHead.PBHoldNoInv.ToString().Replace("`", "-"));
        }

        // Reconcile Quantity
        if (modifiedShipHead.ReconcileQty != originalShipHead.ReconcileQty)
        {
            changes.Add("ShipHead.ReconcileQty`"
                       + originalShipHead.ReconcileQty.ToString()
                       + "`"
                       + modifiedShipHead.ReconcileQty.ToString().Replace("`", "-"));
        }

        // Schedule Number
        if (modifiedShipHead.ScheduleNumber != originalShipHead.ScheduleNumber)
        {
            changes.Add("ShipHead.ScheduleNumber`"
                       + originalShipHead.ScheduleNumber.ToString()
                       + "`"
                       + modifiedShipHead.ScheduleNumber.ToString().Replace("`", "-"));
        }

        // Counter ASN
        if (modifiedShipHead.CounterASN != originalShipHead.CounterASN)
        {
            changes.Add("ShipHead.CounterASN`"
                       + originalShipHead.CounterASN.ToString()
                       + "`"
                       + modifiedShipHead.CounterASN.ToString().Replace("`", "-"));
        }

        // Our Bank
        if (modifiedShipHead.OurBank != originalShipHead.OurBank)
        {
            changes.Add("ShipHead.OurBank`"
                       + originalShipHead.OurBank.ToString()
                       + "`"
                       + modifiedShipHead.OurBank.ToString().Replace("`", "-"));
        }

        // ERS Order
        if (modifiedShipHead.ERSOrder != originalShipHead.ERSOrder)
        {
            changes.Add("ShipHead.ERSOrder`"
                       + originalShipHead.ERSOrder.ToString()
                       + "`"
                       + modifiedShipHead.ERSOrder.ToString().Replace("`", "-"));
        }

        // Auto Print Ready
        if (modifiedShipHead.AutoPrintReady != originalShipHead.AutoPrintReady)
        {
            changes.Add("ShipHead.AutoPrintReady`"
                       + originalShipHead.AutoPrintReady.ToString()
                       + "`"
                       + modifiedShipHead.AutoPrintReady.ToString().Replace("`", "-"));
        }

        // Ship Overs
        if (modifiedShipHead.ShipOvers != originalShipHead.ShipOvers)
        {
            changes.Add("ShipHead.ShipOvers`"
                       + originalShipHead.ShipOvers.ToString()
                       + "`"
                       + modifiedShipHead.ShipOvers.ToString().Replace("`", "-"));
        }

        // WI Pack Slip Created
        if (modifiedShipHead.WIPackSlipCreated != originalShipHead.WIPackSlipCreated)
        {
            changes.Add("ShipHead.WIPackSlipCreated`"
                       + originalShipHead.WIPackSlipCreated.ToString()
                       + "`"
                       + modifiedShipHead.WIPackSlipCreated.ToString().Replace("`", "-"));
        }

        // AG Authorization Code
        if (modifiedShipHead.AGAuthorizationCode != originalShipHead.AGAuthorizationCode)
        {
            changes.Add("ShipHead.AGAuthorizationCode`"
                       + originalShipHead.AGAuthorizationCode.ToString()
                       + "`"
                       + modifiedShipHead.AGAuthorizationCode.ToString().Replace("`", "-"));
        }

        // AG Authorization Date
        if (modifiedShipHead.AGAuthorizationDate != originalShipHead.AGAuthorizationDate)
        {
            changes.Add("ShipHead.AGAuthorizationDate`"
                       + originalShipHead.AGAuthorizationDate.ToString()
                       + "`"
                       + modifiedShipHead.AGAuthorizationDate.ToString().Replace("`", "-"));
        }

        // AG Carrier CUIT
        if (modifiedShipHead.AGCarrierCUIT != originalShipHead.AGCarrierCUIT)
        {
            changes.Add("ShipHead.AGCarrierCUIT`"
                       + originalShipHead.AGCarrierCUIT.ToString()
                       + "`"
                       + modifiedShipHead.AGCarrierCUIT.ToString().Replace("`", "-"));
        }

        // AG COT Mark
        if (modifiedShipHead.AGCOTMark != originalShipHead.AGCOTMark)
        {
            changes.Add("ShipHead.AGCOTMark`"
                       + originalShipHead.AGCOTMark.ToString()
                       + "`"
                       + modifiedShipHead.AGCOTMark.ToString().Replace("`", "-"));
        }

        // AG Document Letter
        if (modifiedShipHead.AGDocumentLetter != originalShipHead.AGDocumentLetter)
        {
            changes.Add("ShipHead.AGDocumentLetter`"
                       + originalShipHead.AGDocumentLetter.ToString()
                       + "`"
                       + modifiedShipHead.AGDocumentLetter.ToString().Replace("`", "-"));
        }

        // AG Invoicing Point
        if (modifiedShipHead.AGInvoicingPoint != originalShipHead.AGInvoicingPoint)
        {
            changes.Add("ShipHead.AGInvoicingPoint`"
                       + originalShipHead.AGInvoicingPoint.ToString()
                       + "`"
                       + modifiedShipHead.AGInvoicingPoint.ToString().Replace("`", "-"));
        }

        // AG Legal Number
        if (modifiedShipHead.AGLegalNumber != originalShipHead.AGLegalNumber)
        {
            changes.Add("ShipHead.AGLegalNumber`"
                       + originalShipHead.AGLegalNumber.ToString()
                       + "`"
                       + modifiedShipHead.AGLegalNumber.ToString().Replace("`", "-"));
        }

        // AG Printing Control Type
        if (modifiedShipHead.AGPrintingControlType != originalShipHead.AGPrintingControlType)
        {
            changes.Add("ShipHead.AGPrintingControlType`"
                       + originalShipHead.AGPrintingControlType.ToString()
                       + "`"
                       + modifiedShipHead.AGPrintingControlType.ToString().Replace("`", "-"));
        }

        // AG Track License
        if (modifiedShipHead.AGTrackLicense != originalShipHead.AGTrackLicense)
        {
            changes.Add("ShipHead.AGTrackLicense`"
                       + originalShipHead.AGTrackLicense.ToString()
                       + "`"
                       + modifiedShipHead.AGTrackLicense.ToString().Replace("`", "-"));
        }

        // Dispatch Reason
        if (modifiedShipHead.DispatchReason != originalShipHead.DispatchReason)
        {
            changes.Add("ShipHead.DispatchReason`"
                       + originalShipHead.DispatchReason.ToString()
                       + "`"
                       + modifiedShipHead.DispatchReason.ToString().Replace("`", "-"));
        }

        // AG Shipping Way
        if (modifiedShipHead.AGShippingWay != originalShipHead.AGShippingWay)
        {
            changes.Add("ShipHead.AGShippingWay`"
                       + originalShipHead.AGShippingWay.ToString()
                       + "`"
                       + modifiedShipHead.AGShippingWay.ToString().Replace("`", "-"));
        }

        // Our Supplier Code
        if (modifiedShipHead.OurSupplierCode != originalShipHead.OurSupplierCode)
        {
            changes.Add("ShipHead.OurSupplierCode`"
                       + originalShipHead.OurSupplierCode.ToString()
                       + "`"
                       + modifiedShipHead.OurSupplierCode.ToString().Replace("`", "-"));
        }

        // ASN Printed Date
        if (modifiedShipHead.ASNPrintedDate != originalShipHead.ASNPrintedDate)
        {
            changes.Add("ShipHead.ASNPrintedDate`"
                       + originalShipHead.ASNPrintedDate.ToString()
                       + "`"
                       + modifiedShipHead.ASNPrintedDate.ToString().Replace("`", "-"));
        }

        // EDI Ship To Number
        if (modifiedShipHead.EDIShipToNum != originalShipHead.EDIShipToNum)
        {
            changes.Add("ShipHead.EDIShipToNum`"
                       + originalShipHead.EDIShipToNum.ToString()
                       + "`"
                       + modifiedShipHead.EDIShipToNum.ToString().Replace("`", "-"));
        }

        // MX Incoterm
        if (modifiedShipHead.MXIncoterm != originalShipHead.MXIncoterm)
        {
            changes.Add("ShipHead.MXIncoterm`"
                       + originalShipHead.MXIncoterm.ToString()
                       + "`"
                       + modifiedShipHead.MXIncoterm.ToString().Replace("`", "-"));
        }

        // Created On
        if (modifiedShipHead.CreatedOn != originalShipHead.CreatedOn)
        {
            changes.Add("ShipHead.CreatedOn`"
                       + originalShipHead.CreatedOn.ToString()
                       + "`"
                       + modifiedShipHead.CreatedOn.ToString().Replace("`", "-"));
        }

        // Digital Signature
        if (modifiedShipHead.DigitalSignature != originalShipHead.DigitalSignature)
        {
            changes.Add("ShipHead.DigitalSignature`"
                       + originalShipHead.DigitalSignature.ToString()
                       + "`"
                       + modifiedShipHead.DigitalSignature.ToString().Replace("`", "-"));
        }

        // Signed On
        if (modifiedShipHead.SignedOn != originalShipHead.SignedOn)
        {
            changes.Add("ShipHead.SignedOn`"
                       + originalShipHead.SignedOn.ToString()
                       + "`"
                       + modifiedShipHead.SignedOn.ToString().Replace("`", "-"));
        }

        // Signed By
        if (modifiedShipHead.SignedBy != originalShipHead.SignedBy)
        {
            changes.Add("ShipHead.SignedBy`"
                       + originalShipHead.SignedBy.ToString()
                       + "`"
                       + modifiedShipHead.SignedBy.ToString().Replace("`", "-"));
        }

        // First Print Date
        if (modifiedShipHead.FirstPrintDate != originalShipHead.FirstPrintDate)
        {
            changes.Add("ShipHead.FirstPrintDate`"
                       + originalShipHead.FirstPrintDate.ToString()
                       + "`"
                       + modifiedShipHead.FirstPrintDate.ToString().Replace("`", "-"));
        }

        // Document Copy Number
        if (modifiedShipHead.DocCopyNum != originalShipHead.DocCopyNum)
        {
            changes.Add("ShipHead.DocCopyNum`"
                       + originalShipHead.DocCopyNum.ToString()
                       + "`"
                       + modifiedShipHead.DocCopyNum.ToString().Replace("`", "-"));
        }

        // CN Declaration Bill
        if (modifiedShipHead.CNDeclarationBill != originalShipHead.CNDeclarationBill)
        {
            changes.Add("ShipHead.CNDeclarationBill`"
                       + originalShipHead.CNDeclarationBill.ToString()
                       + "`"
                       + modifiedShipHead.CNDeclarationBill.ToString().Replace("`", "-"));
        }

        // CN Sample
        if (modifiedShipHead.CNSample != originalShipHead.CNSample)
        {
            changes.Add("ShipHead.CNSample`"
                       + originalShipHead.CNSample.ToString()
                       + "`"
                       + modifiedShipHead.CNSample.ToString().Replace("`", "-"));
        }

        // CN Bonded
        if (modifiedShipHead.CNBonded != originalShipHead.CNBonded)
        {
            changes.Add("ShipHead.CNBonded`"
                       + originalShipHead.CNBonded.ToString()
                       + "`"
                       + modifiedShipHead.CNBonded.ToString().Replace("`", "-"));
        }

        // MX Certified Timestamp
        if (modifiedShipHead.MXCertifiedTimestamp != originalShipHead.MXCertifiedTimestamp)
        {
            changes.Add("ShipHead.MXCertifiedTimestamp`"
                       + originalShipHead.MXCertifiedTimestamp.ToString()
                       + "`"
                       + modifiedShipHead.MXCertifiedTimestamp.ToString().Replace("`", "-"));
        }

        // MX Certificate Serial Number
        if (modifiedShipHead.MXCertificateSN != originalShipHead.MXCertificateSN)
        {
            changes.Add("ShipHead.MXCertificateSN`"
                       + originalShipHead.MXCertificateSN.ToString()
                       + "`"
                       + modifiedShipHead.MXCertificateSN.ToString().Replace("`", "-"));
        }

        // MX Certificate
        if (modifiedShipHead.MXCertificate != originalShipHead.MXCertificate)
        {
            changes.Add("ShipHead.MXCertificate`"
                       + originalShipHead.MXCertificate.ToString()
                       + "`"
                       + modifiedShipHead.MXCertificate.ToString().Replace("`", "-"));
        }

        // MX Fiscal Folio
        if (modifiedShipHead.MXFiscalFolio != originalShipHead.MXFiscalFolio)
        {
            changes.Add("ShipHead.MXFiscalFolio`"
                       + originalShipHead.MXFiscalFolio.ToString()
                       + "`"
                       + modifiedShipHead.MXFiscalFolio.ToString().Replace("`", "-"));
        }

        // MX SAT Certificate Serial Number
        if (modifiedShipHead.MXSATCertificateSN != originalShipHead.MXSATCertificateSN)
        {
            changes.Add("ShipHead.MXSATCertificateSN`"
                       + originalShipHead.MXSATCertificateSN.ToString()
                       + "`"
                       + modifiedShipHead.MXSATCertificateSN.ToString().Replace("`", "-"));
        }

        // MX Digital Seal
        if (modifiedShipHead.MXDigitalSeal != originalShipHead.MXDigitalSeal)
        {
            changes.Add("ShipHead.MXDigitalSeal`"
                       + originalShipHead.MXDigitalSeal.ToString()
                       + "`"
                       + modifiedShipHead.MXDigitalSeal.ToString().Replace("`", "-"));
        }

        // MX SAT Seal
        if (modifiedShipHead.MXSATSeal != originalShipHead.MXSATSeal)
        {
            changes.Add("ShipHead.MXSATSeal`"
                       + originalShipHead.MXSATSeal.ToString()
                       + "`"
                       + modifiedShipHead.MXSATSeal.ToString().Replace("`", "-"));
        }

        // MX Original String
        if (modifiedShipHead.MXOriginalString != originalShipHead.MXOriginalString)
        {
            changes.Add("ShipHead.MXOriginalString`"
                       + originalShipHead.MXOriginalString.ToString()
                       + "`"
                       + modifiedShipHead.MXOriginalString.ToString().Replace("`", "-"));
        }

        // MX Original String TFD
        if (modifiedShipHead.MXOriginalStringTFD != originalShipHead.MXOriginalStringTFD)
        {
            changes.Add("ShipHead.MXOriginalStringTFD`"
                       + originalShipHead.MXOriginalStringTFD.ToString()
                       + "`"
                       + modifiedShipHead.MXOriginalStringTFD.ToString().Replace("`", "-"));
        }

        // MX Distance
        if (modifiedShipHead.MXDistance != originalShipHead.MXDistance)
        {
            changes.Add("ShipHead.MXDistance`"
                       + originalShipHead.MXDistance.ToString()
                       + "`"
                       + modifiedShipHead.MXDistance.ToString().Replace("`", "-"));
        }

        // MX Permit Number
        if (modifiedShipHead.MXPermitNum != originalShipHead.MXPermitNum)
        {
            changes.Add("ShipHead.MXPermitNum`"
                       + originalShipHead.MXPermitNum.ToString()
                       + "`"
                       + modifiedShipHead.MXPermitNum.ToString().Replace("`", "-"));
        }

        // MX Permit Type
        if (modifiedShipHead.MXPermitType != originalShipHead.MXPermitType)
        {
            changes.Add("ShipHead.MXPermitType`"
                       + originalShipHead.MXPermitType.ToString()
                       + "`"
                       + modifiedShipHead.MXPermitType.ToString().Replace("`", "-"));
        }

        // MX Serie
        if (modifiedShipHead.MXSerie != originalShipHead.MXSerie)
        {
            changes.Add("ShipHead.MXSerie`"
                       + originalShipHead.MXSerie.ToString()
                       + "`"
                       + modifiedShipHead.MXSerie.ToString().Replace("`", "-"));
        }

        // MX Folio
        if (modifiedShipHead.MXFolio != originalShipHead.MXFolio)
        {
            changes.Add("ShipHead.MXFolio`"
                       + originalShipHead.MXFolio.ToString()
                       + "`"
                       + modifiedShipHead.MXFolio.ToString().Replace("`", "-"));
        }

        // MX ETD
        if (modifiedShipHead.MXETD != originalShipHead.MXETD)
        {
            changes.Add("ShipHead.MXETD`"
                       + originalShipHead.MXETD.ToString()
                       + "`"
                       + modifiedShipHead.MXETD.ToString().Replace("`", "-"));
        }

        // MX ETA
        if (modifiedShipHead.MXETA != originalShipHead.MXETA)
        {
            changes.Add("ShipHead.MXETA`"
                       + originalShipHead.MXETA.ToString()
                       + "`"
                       + modifiedShipHead.MXETA.ToString().Replace("`", "-"));
        }

        // MX Carta Porte Status
        if (modifiedShipHead.MXCartaPorteStatus != originalShipHead.MXCartaPorteStatus)
        {
            changes.Add("ShipHead.MXCartaPorteStatus`"
                       + originalShipHead.MXCartaPorteStatus.ToString()
                       + "`"
                       + modifiedShipHead.MXCartaPorteStatus.ToString().Replace("`", "-"));
        }

        // Vehicle License Plate
        if (modifiedShipHead.VehicleLicensePlate != originalShipHead.VehicleLicensePlate)
        {
            changes.Add("ShipHead.VehicleLicensePlate`"
                       + originalShipHead.VehicleLicensePlate.ToString()
                       + "`"
                       + modifiedShipHead.VehicleLicensePlate.ToString().Replace("`", "-"));
        }

        // Vehicle Type
        if (modifiedShipHead.VehicleType != originalShipHead.VehicleType)
        {
            changes.Add("ShipHead.VehicleType`"
                       + originalShipHead.VehicleType.ToString()
                       + "`"
                       + modifiedShipHead.VehicleType.ToString().Replace("`", "-"));
        }

        // Vehicle Year
        if (modifiedShipHead.VehicleYear != originalShipHead.VehicleYear)
        {
            changes.Add("ShipHead.VehicleYear`"
                       + originalShipHead.VehicleYear.ToString()
                       + "`"
                       + modifiedShipHead.VehicleYear.ToString().Replace("`", "-"));
        }

        // Driver ID
        if (modifiedShipHead.DriverID != originalShipHead.DriverID)
        {
            changes.Add("ShipHead.DriverID`"
                       + originalShipHead.DriverID.ToString()
                       + "`"
                       + modifiedShipHead.DriverID.ToString().Replace("`", "-"));
        }

        // MX Cancel Fiscal Folio
        if (modifiedShipHead.MXCancelFiscalFolio != originalShipHead.MXCancelFiscalFolio)
        {
            changes.Add("ShipHead.MXCancelFiscalFolio`"
                       + originalShipHead.MXCancelFiscalFolio.ToString()
                       + "`"
                       + modifiedShipHead.MXCancelFiscalFolio.ToString().Replace("`", "-"));
        }

        // Incoterm Code
        if (modifiedShipHead.IncotermCode != originalShipHead.IncotermCode)
        {
            changes.Add("ShipHead.IncotermCode`"
                       + originalShipHead.IncotermCode.ToString()
                       + "`"
                       + modifiedShipHead.IncotermCode.ToString().Replace("`", "-"));
        }

        // Incoterm Location
        if (modifiedShipHead.IncotermLocation != originalShipHead.IncotermLocation)
        {
            changes.Add("ShipHead.IncotermLocation`"
                       + originalShipHead.IncotermLocation.ToString()
                       + "`"
                       + modifiedShipHead.IncotermLocation.ToString().Replace("`", "-"));
        }

        // Vehicle Weight
        if (modifiedShipHead.VehicleWeight != originalShipHead.VehicleWeight)
        {
            changes.Add("ShipHead.VehicleWeight`"
                       + originalShipHead.VehicleWeight.ToString()
                       + "`"
                       + modifiedShipHead.VehicleWeight.ToString().Replace("`", "-"));
        }

        // MX ID CCP
        if (modifiedShipHead.MXIdCCP != originalShipHead.MXIdCCP)
        {
            changes.Add("ShipHead.MXIdCCP`"
                       + originalShipHead.MXIdCCP.ToString()
                       + "`"
                       + modifiedShipHead.MXIdCCP.ToString().Replace("`", "-"));
        }

        // MX Customs Regime
        if (modifiedShipHead.MXCustomsRegime != originalShipHead.MXCustomsRegime)
        {
            changes.Add("ShipHead.MXCustomsRegime`"
                       + originalShipHead.MXCustomsRegime.ToString()
                       + "`"
                       + modifiedShipHead.MXCustomsRegime.ToString().Replace("`", "-"));
        }

        // MX Reverse Logistics
        if (modifiedShipHead.MXReverseLogistics != originalShipHead.MXReverseLogistics)
        {
            changes.Add("ShipHead.MXReverseLogistics`"
                       + originalShipHead.MXReverseLogistics.ToString()
                       + "`"
                       + modifiedShipHead.MXReverseLogistics.ToString().Replace("`", "-"));
        }
    }
}

// =================================================================
// Process ShipDtl Changes
// =================================================================

// Check if ShipDtl data exists
if (ds.ShipDtl != null && ds.ShipDtl.Count > 0)
{
    // Process each ShipDtl record
    for (int i = 0; i < ds.ShipDtl.Count; i++)
    {
        var modifiedDtl = ds.ShipDtl[i];
        int dtlPackNum = modifiedDtl.PackNum;
        int packLine = modifiedDtl.PackLine;

        // Check if the detail row has been deleted
        if (modifiedDtl.RowMod == "D")
        {
            changes.Add($"ShipDtl deleted: Line {packLine}");
            callFunc = true;
            continue;
        }

        // Get the original detail record from database
        var originalDtl = (from dbDtl in Db.ShipDtl
                          where dbDtl.Company == companyID
                             && dbDtl.PackNum == dtlPackNum
                             && dbDtl.PackLine == packLine
                          select dbDtl).FirstOrDefault();

        // Handle new detail creation
        if (originalDtl == null)
        {
            changes.Add($"New shipment detail created: Line {packLine}");
            callFunc = true;
            continue;
        }

        // =================================================================
        // ShipDtl Field Comparisons
        // =================================================================

        // Company
        if (modifiedDtl.Company != originalDtl.Company)
        {
            changes.Add($"ShipDtl.Company[Line {packLine}]`"
                       + originalDtl.Company.ToString()
                       + "`"
                       + modifiedDtl.Company.ToString().Replace("`", "-"));
        }

        // Pack Number
        if (modifiedDtl.PackNum != originalDtl.PackNum)
        {
            changes.Add($"ShipDtl.PackNum[Line {packLine}]`"
                       + originalDtl.PackNum.ToString()
                       + "`"
                       + modifiedDtl.PackNum.ToString().Replace("`", "-"));
        }

        // Pack Line
        if (modifiedDtl.PackLine != originalDtl.PackLine)
        {
            changes.Add($"ShipDtl.PackLine[Line {packLine}]`"
                       + originalDtl.PackLine.ToString()
                       + "`"
                       + modifiedDtl.PackLine.ToString().Replace("`", "-"));
        }

        // Order Number
        if (modifiedDtl.OrderNum != originalDtl.OrderNum)
        {
            changes.Add($"ShipDtl.OrderNum[Line {packLine}]`"
                       + originalDtl.OrderNum.ToString()
                       + "`"
                       + modifiedDtl.OrderNum.ToString().Replace("`", "-"));
        }

        // Order Line
        if (modifiedDtl.OrderLine != originalDtl.OrderLine)
        {
            changes.Add($"ShipDtl.OrderLine[Line {packLine}]`"
                       + originalDtl.OrderLine.ToString()
                       + "`"
                       + modifiedDtl.OrderLine.ToString().Replace("`", "-"));
        }

        // Order Release Number
        if (modifiedDtl.OrderRelNum != originalDtl.OrderRelNum)
        {
            changes.Add($"ShipDtl.OrderRelNum[Line {packLine}]`"
                       + originalDtl.OrderRelNum.ToString()
                       + "`"
                       + modifiedDtl.OrderRelNum.ToString().Replace("`", "-"));
        }

        // Line Type
        if (modifiedDtl.LineType != originalDtl.LineType)
        {
            changes.Add($"ShipDtl.LineType[Line {packLine}]`"
                       + originalDtl.LineType.ToString()
                       + "`"
                       + modifiedDtl.LineType.ToString().Replace("`", "-"));
        }

        // Part Number
        if (modifiedDtl.PartNum != originalDtl.PartNum)
        {
            changes.Add($"ShipDtl.PartNum[Line {packLine}]`"
                       + originalDtl.PartNum.ToString()
                       + "`"
                       + modifiedDtl.PartNum.ToString().Replace("`", "-"));
        }

        // Line Description
        if (modifiedDtl.LineDesc != originalDtl.LineDesc)
        {
            changes.Add($"ShipDtl.LineDesc[Line {packLine}]`"
                       + originalDtl.LineDesc.ToString()
                       + "`"
                       + modifiedDtl.LineDesc.ToString().Replace("`", "-"));
        }

        // Inventory Unit of Measure
        if (modifiedDtl.IUM != originalDtl.IUM)
        {
            changes.Add($"ShipDtl.IUM[Line {packLine}]`"
                       + originalDtl.IUM.ToString()
                       + "`"
                       + modifiedDtl.IUM.ToString().Replace("`", "-"));
        }



        // Revision Number
        if (modifiedDtl.RevisionNum != originalDtl.RevisionNum)
        {
            changes.Add($"ShipDtl.RevisionNum[Line {packLine}]`"
                       + originalDtl.RevisionNum.ToString()
                       + "`"
                       + modifiedDtl.RevisionNum.ToString().Replace("`", "-"));
        }



        // Bin Number
        if (modifiedDtl.BinNum != originalDtl.BinNum)
        {
            changes.Add($"ShipDtl.BinNum[Line {packLine}]`"
                       + originalDtl.BinNum.ToString()
                       + "`"
                       + modifiedDtl.BinNum.ToString().Replace("`", "-"));
        }

        // Lot Number
        if (modifiedDtl.LotNum != originalDtl.LotNum)
        {
            changes.Add($"ShipDtl.LotNum[Line {packLine}]`"
                       + originalDtl.LotNum.ToString()
                       + "`"
                       + modifiedDtl.LotNum.ToString().Replace("`", "-"));
        }

        // Dimension Code
        if (modifiedDtl.DimCode != originalDtl.DimCode)
        {
            changes.Add($"ShipDtl.DimCode[Line {packLine}]`"
                       + originalDtl.DimCode.ToString()
                       + "`"
                       + modifiedDtl.DimCode.ToString().Replace("`", "-"));
        }

        // Dimension Conversion Factor
        if (modifiedDtl.DimConvFactor != originalDtl.DimConvFactor)
        {
            changes.Add($"ShipDtl.DimConvFactor[Line {packLine}]`"
                       + originalDtl.DimConvFactor.ToString()
                       + "`"
                       + modifiedDtl.DimConvFactor.ToString().Replace("`", "-"));
        }



        // Warehouse Code
        if (modifiedDtl.WarehouseCode != originalDtl.WarehouseCode)
        {
            changes.Add($"ShipDtl.WarehouseCode[Line {packLine}]`"
                       + originalDtl.WarehouseCode.ToString()
                       + "`"
                       + modifiedDtl.WarehouseCode.ToString().Replace("`", "-"));
        }



        // Selling Factor
        if (modifiedDtl.SellingFactor != originalDtl.SellingFactor)
        {
            changes.Add($"ShipDtl.SellingFactor[Line {packLine}]`"
                       + originalDtl.SellingFactor.ToString()
                       + "`"
                       + modifiedDtl.SellingFactor.ToString().Replace("`", "-"));
        }

        // Selling Factor Direction
        if (modifiedDtl.SellingFactorDirection != originalDtl.SellingFactorDirection)
        {
            changes.Add($"ShipDtl.SellingFactorDirection[Line {packLine}]`"
                       + originalDtl.SellingFactorDirection.ToString()
                       + "`"
                       + modifiedDtl.SellingFactorDirection.ToString().Replace("`", "-"));
        }





        // WIP Warehouse Code
        if (modifiedDtl.WIPWarehouseCode != originalDtl.WIPWarehouseCode)
        {
            changes.Add($"ShipDtl.WIPWarehouseCode[Line {packLine}]`"
                       + originalDtl.WIPWarehouseCode.ToString()
                       + "`"
                       + modifiedDtl.WIPWarehouseCode.ToString().Replace("`", "-"));
        }

        // WIP Bin Number
        if (modifiedDtl.WIPBinNum != originalDtl.WIPBinNum)
        {
            changes.Add($"ShipDtl.WIPBinNum[Line {packLine}]`"
                       + originalDtl.WIPBinNum.ToString()
                       + "`"
                       + modifiedDtl.WIPBinNum.ToString().Replace("`", "-"));
        }

        // Our Inventory Ship Quantity
        if (modifiedDtl.OurInventoryShipQty != originalDtl.OurInventoryShipQty)
        {
            changes.Add($"ShipDtl.OurInventoryShipQty[Line {packLine}]`"
                       + originalDtl.OurInventoryShipQty.ToString()
                       + "`"
                       + modifiedDtl.OurInventoryShipQty.ToString().Replace("`", "-"));
        }

        // Our Job Ship Quantity
        if (modifiedDtl.OurJobShipQty != originalDtl.OurJobShipQty)
        {
            changes.Add($"ShipDtl.OurJobShipQty[Line {packLine}]`"
                       + originalDtl.OurJobShipQty.ToString()
                       + "`"
                       + modifiedDtl.OurJobShipQty.ToString().Replace("`", "-"));
        }

        // Job Number
        if (modifiedDtl.JobNum != originalDtl.JobNum)
        {
            changes.Add($"ShipDtl.JobNum[Line {packLine}]`"
                       + originalDtl.JobNum.ToString()
                       + "`"
                       + modifiedDtl.JobNum.ToString().Replace("`", "-"));
        }

        // Packages
        if (modifiedDtl.Packages != originalDtl.Packages)
        {
            changes.Add($"ShipDtl.Packages[Line {packLine}]`"
                       + originalDtl.Packages.ToString()
                       + "`"
                       + modifiedDtl.Packages.ToString().Replace("`", "-"));
        }

        // Ship Comment
        if (modifiedDtl.ShipComment != originalDtl.ShipComment)
        {
            changes.Add($"ShipDtl.ShipComment[Line {packLine}]`"
                       + originalDtl.ShipComment.ToString()
                       + "`"
                       + modifiedDtl.ShipComment.ToString().Replace("`", "-"));
        }

        // Ship Complete
        if (modifiedDtl.ShipCmpl != originalDtl.ShipCmpl)
        {
            changes.Add($"ShipDtl.ShipCmpl[Line {packLine}]`"
                       + originalDtl.ShipCmpl.ToString()
                       + "`"
                       + modifiedDtl.ShipCmpl.ToString().Replace("`", "-"));
        }

        // Updated Inventory
        if (modifiedDtl.UpdatedInventory != originalDtl.UpdatedInventory)
        {
            changes.Add($"ShipDtl.UpdatedInventory[Line {packLine}]`"
                       + originalDtl.UpdatedInventory.ToString()
                       + "`"
                       + modifiedDtl.UpdatedInventory.ToString().Replace("`", "-"));
        }

        // Cross Reference Part Number
        if (modifiedDtl.XPartNum != originalDtl.XPartNum)
        {
            changes.Add($"ShipDtl.XPartNum[Line {packLine}]`"
                       + originalDtl.XPartNum.ToString()
                       + "`"
                       + modifiedDtl.XPartNum.ToString().Replace("`", "-"));
        }

        // Cross Reference Revision Number
        if (modifiedDtl.XRevisionNum != originalDtl.XRevisionNum)
        {
            changes.Add($"ShipDtl.XRevisionNum[Line {packLine}]`"
                       + originalDtl.XRevisionNum.ToString()
                       + "`"
                       + modifiedDtl.XRevisionNum.ToString().Replace("`", "-"));
        }

        // Ship Contract Number
        if (modifiedDtl.ShpConNum != originalDtl.ShpConNum)
        {
            changes.Add($"ShipDtl.ShpConNum[Line {packLine}]`"
                       + originalDtl.ShpConNum.ToString()
                       + "`"
                       + modifiedDtl.ShpConNum.ToString().Replace("`", "-"));
        }

        // TM Billing
        if (modifiedDtl.TMBilling != originalDtl.TMBilling)
        {
            changes.Add($"ShipDtl.TMBilling[Line {packLine}]`"
                       + originalDtl.TMBilling.ToString()
                       + "`"
                       + modifiedDtl.TMBilling.ToString().Replace("`", "-"));
        }

        // Weight Unit of Measure
        if (modifiedDtl.WUM != originalDtl.WUM)
        {
            changes.Add($"ShipDtl.WUM[Line {packLine}]`"
                       + originalDtl.WUM.ToString()
                       + "`"
                       + modifiedDtl.WUM.ToString().Replace("`", "-"));
        }

        // Dimension Unit of Measure
        if (modifiedDtl.DUM != originalDtl.DUM)
        {
            changes.Add($"ShipDtl.DUM[Line {packLine}]`"
                       + originalDtl.DUM.ToString()
                       + "`"
                       + modifiedDtl.DUM.ToString().Replace("`", "-"));
        }

        // Invoice Comment
        if (modifiedDtl.InvoiceComment != originalDtl.InvoiceComment)
        {
            changes.Add($"ShipDtl.InvoiceComment[Line {packLine}]`"
                       + originalDtl.InvoiceComment.ToString()
                       + "`"
                       + modifiedDtl.InvoiceComment.ToString().Replace("`", "-"));
        }

        // Warranty Code
        if (modifiedDtl.WarrantyCode != originalDtl.WarrantyCode)
        {
            changes.Add($"ShipDtl.WarrantyCode[Line {packLine}]`"
                       + originalDtl.WarrantyCode.ToString()
                       + "`"
                       + modifiedDtl.WarrantyCode.ToString().Replace("`", "-"));
        }

        // Customer Number
        if (modifiedDtl.CustNum != originalDtl.CustNum)
        {
            changes.Add($"ShipDtl.CustNum[Line {packLine}]`"
                       + originalDtl.CustNum.ToString()
                       + "`"
                       + modifiedDtl.CustNum.ToString().Replace("`", "-"));
        }

        // Ship To Number
        if (modifiedDtl.ShipToNum != originalDtl.ShipToNum)
        {
            changes.Add($"ShipDtl.ShipToNum[Line {packLine}]`"
                       + originalDtl.ShipToNum.ToString()
                       + "`"
                       + modifiedDtl.ShipToNum.ToString().Replace("`", "-"));
        }

        // Effective Date
        if (modifiedDtl.EffectiveDate != originalDtl.EffectiveDate)
        {
            changes.Add($"ShipDtl.EffectiveDate[Line {packLine}]`"
                       + originalDtl.EffectiveDate.ToString()
                       + "`"
                       + modifiedDtl.EffectiveDate.ToString().Replace("`", "-"));
        }

        // Material Duration
        if (modifiedDtl.MaterialDuration != originalDtl.MaterialDuration)
        {
            changes.Add($"ShipDtl.MaterialDuration[Line {packLine}]`"
                       + originalDtl.MaterialDuration.ToString()
                       + "`"
                       + modifiedDtl.MaterialDuration.ToString().Replace("`", "-"));
        }

        // Labor Duration
        if (modifiedDtl.LaborDuration != originalDtl.LaborDuration)
        {
            changes.Add($"ShipDtl.LaborDuration[Line {packLine}]`"
                       + originalDtl.LaborDuration.ToString()
                       + "`"
                       + modifiedDtl.LaborDuration.ToString().Replace("`", "-"));
        }

        // Miscellaneous Duration
        if (modifiedDtl.MiscDuration != originalDtl.MiscDuration)
        {
            changes.Add($"ShipDtl.MiscDuration[Line {packLine}]`"
                       + originalDtl.MiscDuration.ToString()
                       + "`"
                       + modifiedDtl.MiscDuration.ToString().Replace("`", "-"));
        }

        // Material Modifier
        if (modifiedDtl.MaterialMod != originalDtl.MaterialMod)
        {
            changes.Add($"ShipDtl.MaterialMod[Line {packLine}]`"
                       + originalDtl.MaterialMod.ToString()
                       + "`"
                       + modifiedDtl.MaterialMod.ToString().Replace("`", "-"));
        }

        // Labor Modifier
        if (modifiedDtl.LaborMod != originalDtl.LaborMod)
        {
            changes.Add($"ShipDtl.LaborMod[Line {packLine}]`"
                       + originalDtl.LaborMod.ToString()
                       + "`"
                       + modifiedDtl.LaborMod.ToString().Replace("`", "-"));
        }

        // Miscellaneous Modifier
        if (modifiedDtl.MiscMod != originalDtl.MiscMod)
        {
            changes.Add($"ShipDtl.MiscMod[Line {packLine}]`"
                       + originalDtl.MiscMod.ToString()
                       + "`"
                       + modifiedDtl.MiscMod.ToString().Replace("`", "-"));
        }

        // Material Expiration
        if (modifiedDtl.MaterialExpiration != originalDtl.MaterialExpiration)
        {
            changes.Add($"ShipDtl.MaterialExpiration[Line {packLine}]`"
                       + originalDtl.MaterialExpiration.ToString()
                       + "`"
                       + modifiedDtl.MaterialExpiration.ToString().Replace("`", "-"));
        }

        // Labor Expiration
        if (modifiedDtl.LaborExpiration != originalDtl.LaborExpiration)
        {
            changes.Add($"ShipDtl.LaborExpiration[Line {packLine}]`"
                       + originalDtl.LaborExpiration.ToString()
                       + "`"
                       + modifiedDtl.LaborExpiration.ToString().Replace("`", "-"));
        }

        // Miscellaneous Expiration
        if (modifiedDtl.MiscExpiration != originalDtl.MiscExpiration)
        {
            changes.Add($"ShipDtl.MiscExpiration[Line {packLine}]`"
                       + originalDtl.MiscExpiration.ToString()
                       + "`"
                       + modifiedDtl.MiscExpiration.ToString().Replace("`", "-"));
        }

        // Last Expiration
        if (modifiedDtl.LastExpiration != originalDtl.LastExpiration)
        {
            changes.Add($"ShipDtl.LastExpiration[Line {packLine}]`"
                       + originalDtl.LastExpiration.ToString()
                       + "`"
                       + modifiedDtl.LastExpiration.ToString().Replace("`", "-"));
        }

        // Warranty Comment
        if (modifiedDtl.WarrantyComment != originalDtl.WarrantyComment)
        {
            changes.Add($"ShipDtl.WarrantyComment[Line {packLine}]`"
                       + originalDtl.WarrantyComment.ToString()
                       + "`"
                       + modifiedDtl.WarrantyComment.ToString().Replace("`", "-"));
        }

        // Contract Number
        if (modifiedDtl.ContractNum != originalDtl.ContractNum)
        {
            changes.Add($"ShipDtl.ContractNum[Line {packLine}]`"
                       + originalDtl.ContractNum.ToString()
                       + "`"
                       + modifiedDtl.ContractNum.ToString().Replace("`", "-"));
        }

        // Contract Code
        if (modifiedDtl.ContractCode != originalDtl.ContractCode)
        {
            changes.Add($"ShipDtl.ContractCode[Line {packLine}]`"
                       + originalDtl.ContractCode.ToString()
                       + "`"
                       + modifiedDtl.ContractCode.ToString().Replace("`", "-"));
        }

        // Onsite
        if (modifiedDtl.Onsite != originalDtl.Onsite)
        {
            changes.Add($"ShipDtl.Onsite[Line {packLine}]`"
                       + originalDtl.Onsite.ToString()
                       + "`"
                       + modifiedDtl.Onsite.ToString().Replace("`", "-"));
        }

        // Material Covered
        if (modifiedDtl.MatCovered != originalDtl.MatCovered)
        {
            changes.Add($"ShipDtl.MatCovered[Line {packLine}]`"
                       + originalDtl.MatCovered.ToString()
                       + "`"
                       + modifiedDtl.MatCovered.ToString().Replace("`", "-"));
        }

        // Labor Covered
        if (modifiedDtl.LabCovered != originalDtl.LabCovered)
        {
            changes.Add($"ShipDtl.LabCovered[Line {packLine}]`"
                       + originalDtl.LabCovered.ToString()
                       + "`"
                       + modifiedDtl.LabCovered.ToString().Replace("`", "-"));
        }

        // Miscellaneous Covered
        if (modifiedDtl.MiscCovered != originalDtl.MiscCovered)
        {
            changes.Add($"ShipDtl.MiscCovered[Line {packLine}]`"
                       + originalDtl.MiscCovered.ToString()
                       + "`"
                       + modifiedDtl.MiscCovered.ToString().Replace("`", "-"));
        }

        // Plant
        if (modifiedDtl.Plant != originalDtl.Plant)
        {
            changes.Add($"ShipDtl.Plant[Line {packLine}]`"
                       + originalDtl.Plant.ToString()
                       + "`"
                       + modifiedDtl.Plant.ToString().Replace("`", "-"));
        }

        // Ready To Invoice
        if (modifiedDtl.ReadyToInvoice != originalDtl.ReadyToInvoice)
        {
            changes.Add($"ShipDtl.ReadyToInvoice[Line {packLine}]`"
                       + originalDtl.ReadyToInvoice.ToString()
                       + "`"
                       + modifiedDtl.ReadyToInvoice.ToString().Replace("`", "-"));
        }

        // Selling Inventory Ship Quantity
        if (modifiedDtl.SellingInventoryShipQty != originalDtl.SellingInventoryShipQty)
        {
            changes.Add($"ShipDtl.SellingInventoryShipQty[Line {packLine}]`"
                       + originalDtl.SellingInventoryShipQty.ToString()
                       + "`"
                       + modifiedDtl.SellingInventoryShipQty.ToString().Replace("`", "-"));
        }

        // Selling Job Ship Quantity
        if (modifiedDtl.SellingJobShipQty != originalDtl.SellingJobShipQty)
        {
            changes.Add($"ShipDtl.SellingJobShipQty[Line {packLine}]`"
                       + originalDtl.SellingJobShipQty.ToString()
                       + "`"
                       + modifiedDtl.SellingJobShipQty.ToString().Replace("`", "-"));
        }

        // Sales Unit of Measure
        if (modifiedDtl.SalesUM != originalDtl.SalesUM)
        {
            changes.Add($"ShipDtl.SalesUM[Line {packLine}]`"
                       + originalDtl.SalesUM.ToString()
                       + "`"
                       + modifiedDtl.SalesUM.ToString().Replace("`", "-"));
        }

        // Total Net Weight
        if (modifiedDtl.TotalNetWeight != originalDtl.TotalNetWeight)
        {
            changes.Add($"ShipDtl.TotalNetWeight[Line {packLine}]`"
                       + originalDtl.TotalNetWeight.ToString()
                       + "`"
                       + modifiedDtl.TotalNetWeight.ToString().Replace("`", "-"));
        }

        // Header Ship Comment
        if (modifiedDtl.HeaderShipComment != originalDtl.HeaderShipComment)
        {
            changes.Add($"ShipDtl.HeaderShipComment[Line {packLine}]`"
                       + originalDtl.HeaderShipComment.ToString()
                       + "`"
                       + modifiedDtl.HeaderShipComment.ToString().Replace("`", "-"));
        }

        // Kit Parent Line
        if (modifiedDtl.KitParentLine != originalDtl.KitParentLine)
        {
            changes.Add($"ShipDtl.KitParentLine[Line {packLine}]`"
                       + originalDtl.KitParentLine.ToString()
                       + "`"
                       + modifiedDtl.KitParentLine.ToString().Replace("`", "-"));
        }

        // Changed By
        if (modifiedDtl.ChangedBy != originalDtl.ChangedBy)
        {
            changes.Add($"ShipDtl.ChangedBy[Line {packLine}]`"
                       + originalDtl.ChangedBy.ToString()
                       + "`"
                       + modifiedDtl.ChangedBy.ToString().Replace("`", "-"));
        }

        // Change Date
        if (modifiedDtl.ChangeDate != originalDtl.ChangeDate)
        {
            changes.Add($"ShipDtl.ChangeDate[Line {packLine}]`"
                       + originalDtl.ChangeDate.ToString()
                       + "`"
                       + modifiedDtl.ChangeDate.ToString().Replace("`", "-"));
        }

        // Change Time
        if (modifiedDtl.ChangeTime != originalDtl.ChangeTime)
        {
            changes.Add($"ShipDtl.ChangeTime[Line {packLine}]`"
                       + originalDtl.ChangeTime.ToString()
                       + "`"
                       + modifiedDtl.ChangeTime.ToString().Replace("`", "-"));
        }

        // Inventory Ship UOM
        if (modifiedDtl.InventoryShipUOM != originalDtl.InventoryShipUOM)
        {
            changes.Add($"ShipDtl.InventoryShipUOM[Line {packLine}]`"
                       + originalDtl.InventoryShipUOM.ToString()
                       + "`"
                       + modifiedDtl.InventoryShipUOM.ToString().Replace("`", "-"));
        }

        // Job Ship UOM
        if (modifiedDtl.JobShipUOM != originalDtl.JobShipUOM)
        {
            changes.Add($"ShipDtl.JobShipUOM[Line {packLine}]`"
                       + originalDtl.JobShipUOM.ToString()
                       + "`"
                       + modifiedDtl.JobShipUOM.ToString().Replace("`", "-"));
        }

        // Track Serial Number
        if (modifiedDtl.TrackSerialNum != originalDtl.TrackSerialNum)
        {
            changes.Add($"ShipDtl.TrackSerialNum[Line {packLine}]`"
                       + originalDtl.TrackSerialNum.ToString()
                       + "`"
                       + modifiedDtl.TrackSerialNum.ToString().Replace("`", "-"));
        }

        // Job Lot Number
        if (modifiedDtl.JobLotNum != originalDtl.JobLotNum)
        {
            changes.Add($"ShipDtl.JobLotNum[Line {packLine}]`"
                       + originalDtl.JobLotNum.ToString()
                       + "`"
                       + modifiedDtl.JobLotNum.ToString().Replace("`", "-"));
        }

        // Bin Type
        if (modifiedDtl.BinType != originalDtl.BinType)
        {
            changes.Add($"ShipDtl.BinType[Line {packLine}]`"
                       + originalDtl.BinType.ToString()
                       + "`"
                       + modifiedDtl.BinType.ToString().Replace("`", "-"));
        }

        // Not Compliant
        if (modifiedDtl.NotCompliant != originalDtl.NotCompliant)
        {
            changes.Add($"ShipDtl.NotCompliant[Line {packLine}]`"
                       + originalDtl.NotCompliant.ToString()
                       + "`"
                       + modifiedDtl.NotCompliant.ToString().Replace("`", "-"));
        }

        // Compliance Message
        if (modifiedDtl.ComplianceMsg != originalDtl.ComplianceMsg)
        {
            changes.Add($"ShipDtl.ComplianceMsg[Line {packLine}]`"
                       + originalDtl.ComplianceMsg.ToString()
                       + "`"
                       + modifiedDtl.ComplianceMsg.ToString().Replace("`", "-"));
        }

        // Discount Percent
        if (modifiedDtl.DiscountPercent != originalDtl.DiscountPercent)
        {
            changes.Add($"ShipDtl.DiscountPercent[Line {packLine}]`"
                       + originalDtl.DiscountPercent.ToString()
                       + "`"
                       + modifiedDtl.DiscountPercent.ToString().Replace("`", "-"));
        }

        // Price Per Code
        if (modifiedDtl.PricePerCode != originalDtl.PricePerCode)
        {
            changes.Add($"ShipDtl.PricePerCode[Line {packLine}]`"
                       + originalDtl.PricePerCode.ToString()
                       + "`"
                       + modifiedDtl.PricePerCode.ToString().Replace("`", "-"));
        }

        // Discount
        if (modifiedDtl.Discount != originalDtl.Discount)
        {
            changes.Add($"ShipDtl.Discount[Line {packLine}]`"
                       + originalDtl.Discount.ToString()
                       + "`"
                       + modifiedDtl.Discount.ToString().Replace("`", "-"));
        }

        // Document Discount
        if (modifiedDtl.DocDiscount != originalDtl.DocDiscount)
        {
            changes.Add($"ShipDtl.DocDiscount[Line {packLine}]`"
                       + originalDtl.DocDiscount.ToString()
                       + "`"
                       + modifiedDtl.DocDiscount.ToString().Replace("`", "-"));
        }

        // Report 1 Discount
        if (modifiedDtl.Rpt1Discount != originalDtl.Rpt1Discount)
        {
            changes.Add($"ShipDtl.Rpt1Discount[Line {packLine}]`"
                       + originalDtl.Rpt1Discount.ToString()
                       + "`"
                       + modifiedDtl.Rpt1Discount.ToString().Replace("`", "-"));
        }

        // Report 2 Discount
        if (modifiedDtl.Rpt2Discount != originalDtl.Rpt2Discount)
        {
            changes.Add($"ShipDtl.Rpt2Discount[Line {packLine}]`"
                       + originalDtl.Rpt2Discount.ToString()
                       + "`"
                       + modifiedDtl.Rpt2Discount.ToString().Replace("`", "-"));
        }

        // Report 3 Discount
        if (modifiedDtl.Rpt3Discount != originalDtl.Rpt3Discount)
        {
            changes.Add($"ShipDtl.Rpt3Discount[Line {packLine}]`"
                       + originalDtl.Rpt3Discount.ToString()
                       + "`"
                       + modifiedDtl.Rpt3Discount.ToString().Replace("`", "-"));
        }

        // Extended Price
        if (modifiedDtl.ExtPrice != originalDtl.ExtPrice)
        {
            changes.Add($"ShipDtl.ExtPrice[Line {packLine}]`"
                       + originalDtl.ExtPrice.ToString()
                       + "`"
                       + modifiedDtl.ExtPrice.ToString().Replace("`", "-"));
        }

        // Document Extended Price
        if (modifiedDtl.DocExtPrice != originalDtl.DocExtPrice)
        {
            changes.Add($"ShipDtl.DocExtPrice[Line {packLine}]`"
                       + originalDtl.DocExtPrice.ToString()
                       + "`"
                       + modifiedDtl.DocExtPrice.ToString().Replace("`", "-"));
        }

        // Report 1 Extended Price
        if (modifiedDtl.Rpt1ExtPrice != originalDtl.Rpt1ExtPrice)
        {
            changes.Add($"ShipDtl.Rpt1ExtPrice[Line {packLine}]`"
                       + originalDtl.Rpt1ExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1ExtPrice.ToString().Replace("`", "-"));
        }

        // Report 2 Extended Price
        if (modifiedDtl.Rpt2ExtPrice != originalDtl.Rpt2ExtPrice)
        {
            changes.Add($"ShipDtl.Rpt2ExtPrice[Line {packLine}]`"
                       + originalDtl.Rpt2ExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2ExtPrice.ToString().Replace("`", "-"));
        }

        // Report 3 Extended Price
        if (modifiedDtl.Rpt3ExtPrice != originalDtl.Rpt3ExtPrice)
        {
            changes.Add($"ShipDtl.Rpt3ExtPrice[Line {packLine}]`"
                       + originalDtl.Rpt3ExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3ExtPrice.ToString().Replace("`", "-"));
        }

        // Unit Price
        if (modifiedDtl.UnitPrice != originalDtl.UnitPrice)
        {
            changes.Add($"ShipDtl.UnitPrice[Line {packLine}]`"
                       + originalDtl.UnitPrice.ToString()
                       + "`"
                       + modifiedDtl.UnitPrice.ToString().Replace("`", "-"));
        }

        // Document Unit Price
        if (modifiedDtl.DocUnitPrice != originalDtl.DocUnitPrice)
        {
            changes.Add($"ShipDtl.DocUnitPrice[Line {packLine}]`"
                       + originalDtl.DocUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.DocUnitPrice.ToString().Replace("`", "-"));
        }

        // Report 1 Unit Price
        if (modifiedDtl.Rpt1UnitPrice != originalDtl.Rpt1UnitPrice)
        {
            changes.Add($"ShipDtl.Rpt1UnitPrice[Line {packLine}]`"
                       + originalDtl.Rpt1UnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1UnitPrice.ToString().Replace("`", "-"));
        }

        // Report 2 Unit Price
        if (modifiedDtl.Rpt2UnitPrice != originalDtl.Rpt2UnitPrice)
        {
            changes.Add($"ShipDtl.Rpt2UnitPrice[Line {packLine}]`"
                       + originalDtl.Rpt2UnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2UnitPrice.ToString().Replace("`", "-"));
        }

        // Report 3 Unit Price
        if (modifiedDtl.Rpt3UnitPrice != originalDtl.Rpt3UnitPrice)
        {
            changes.Add($"ShipDtl.Rpt3UnitPrice[Line {packLine}]`"
                       + originalDtl.Rpt3UnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3UnitPrice.ToString().Replace("`", "-"));
        }

        // Picked Auto Allocated Quantity
        if (modifiedDtl.PickedAutoAllocatedQty != originalDtl.PickedAutoAllocatedQty)
        {
            changes.Add($"ShipDtl.PickedAutoAllocatedQty[Line {packLine}]`"
                       + originalDtl.PickedAutoAllocatedQty.ToString()
                       + "`"
                       + modifiedDtl.PickedAutoAllocatedQty.ToString().Replace("`", "-"));
        }

        // Ship To Customer Number
        if (modifiedDtl.ShipToCustNum != originalDtl.ShipToCustNum)
        {
            changes.Add($"ShipDtl.ShipToCustNum[Line {packLine}]`"
                       + originalDtl.ShipToCustNum.ToString()
                       + "`"
                       + modifiedDtl.ShipToCustNum.ToString().Replace("`", "-"));
        }

        // In Discount
        if (modifiedDtl.InDiscount != originalDtl.InDiscount)
        {
            changes.Add($"ShipDtl.InDiscount[Line {packLine}]`"
                       + originalDtl.InDiscount.ToString()
                       + "`"
                       + modifiedDtl.InDiscount.ToString().Replace("`", "-"));
        }

        // Document In Discount
        if (modifiedDtl.DocInDiscount != originalDtl.DocInDiscount)
        {
            changes.Add($"ShipDtl.DocInDiscount[Line {packLine}]`"
                       + originalDtl.DocInDiscount.ToString()
                       + "`"
                       + modifiedDtl.DocInDiscount.ToString().Replace("`", "-"));
        }

        // Report 1 In Discount
        if (modifiedDtl.Rpt1InDiscount != originalDtl.Rpt1InDiscount)
        {
            changes.Add($"ShipDtl.Rpt1InDiscount[Line {packLine}]`"
                       + originalDtl.Rpt1InDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InDiscount.ToString().Replace("`", "-"));
        }

        // Report 2 In Discount
        if (modifiedDtl.Rpt2InDiscount != originalDtl.Rpt2InDiscount)
        {
            changes.Add($"ShipDtl.Rpt2InDiscount[Line {packLine}]`"
                       + originalDtl.Rpt2InDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InDiscount.ToString().Replace("`", "-"));
        }

        // Report 3 In Discount
        if (modifiedDtl.Rpt3InDiscount != originalDtl.Rpt3InDiscount)
        {
            changes.Add($"ShipDtl.Rpt3InDiscount[Line {packLine}]`"
                       + originalDtl.Rpt3InDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InDiscount.ToString().Replace("`", "-"));
        }

        // In Extended Price
        if (modifiedDtl.InExtPrice != originalDtl.InExtPrice)
        {
            changes.Add($"ShipDtl.InExtPrice[Line {packLine}]`"
                       + originalDtl.InExtPrice.ToString()
                       + "`"
                       + modifiedDtl.InExtPrice.ToString().Replace("`", "-"));
        }

        // Document In Extended Price
        if (modifiedDtl.DocInExtPrice != originalDtl.DocInExtPrice)
        {
            changes.Add($"ShipDtl.DocInExtPrice[Line {packLine}]`"
                       + originalDtl.DocInExtPrice.ToString()
                       + "`"
                       + modifiedDtl.DocInExtPrice.ToString().Replace("`", "-"));
        }

        // Report 1 In Extended Price
        if (modifiedDtl.Rpt1InExtPrice != originalDtl.Rpt1InExtPrice)
        {
            changes.Add($"ShipDtl.Rpt1InExtPrice[Line {packLine}]`"
                       + originalDtl.Rpt1InExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InExtPrice.ToString().Replace("`", "-"));
        }

        // Report 2 In Extended Price
        if (modifiedDtl.Rpt2InExtPrice != originalDtl.Rpt2InExtPrice)
        {
            changes.Add($"ShipDtl.Rpt2InExtPrice[Line {packLine}]`"
                       + originalDtl.Rpt2InExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InExtPrice.ToString().Replace("`", "-"));
        }

        // Report 3 In Extended Price
        if (modifiedDtl.Rpt3InExtPrice != originalDtl.Rpt3InExtPrice)
        {
            changes.Add($"ShipDtl.Rpt3InExtPrice[Line {packLine}]`"
                       + originalDtl.Rpt3InExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InExtPrice.ToString().Replace("`", "-"));
        }

        // In Unit Price
        if (modifiedDtl.InUnitPrice != originalDtl.InUnitPrice)
        {
            changes.Add($"ShipDtl.InUnitPrice[Line {packLine}]`"
                       + originalDtl.InUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.InUnitPrice.ToString().Replace("`", "-"));
        }

        // Document In Unit Price
        if (modifiedDtl.DocInUnitPrice != originalDtl.DocInUnitPrice)
        {
            changes.Add($"ShipDtl.DocInUnitPrice[Line {packLine}]`"
                       + originalDtl.DocInUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.DocInUnitPrice.ToString().Replace("`", "-"));
        }

        // Report 1 In Unit Price
        if (modifiedDtl.Rpt1InUnitPrice != originalDtl.Rpt1InUnitPrice)
        {
            changes.Add($"ShipDtl.Rpt1InUnitPrice[Line {packLine}]`"
                       + originalDtl.Rpt1InUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InUnitPrice.ToString().Replace("`", "-"));
        }

        // Report 2 In Unit Price
        if (modifiedDtl.Rpt2InUnitPrice != originalDtl.Rpt2InUnitPrice)
        {
            changes.Add($"ShipDtl.Rpt2InUnitPrice[Line {packLine}]`"
                       + originalDtl.Rpt2InUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InUnitPrice.ToString().Replace("`", "-"));
        }

        // Report 3 In Unit Price
        if (modifiedDtl.Rpt3InUnitPrice != originalDtl.Rpt3InUnitPrice)
        {
            changes.Add($"ShipDtl.Rpt3InUnitPrice[Line {packLine}]`"
                       + originalDtl.Rpt3InUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InUnitPrice.ToString().Replace("`", "-"));
        }

        // In Price
        if (modifiedDtl.InPrice != originalDtl.InPrice)
        {
            changes.Add($"ShipDtl.InPrice[Line {packLine}]`"
                       + originalDtl.InPrice.ToString()
                       + "`"
                       + modifiedDtl.InPrice.ToString().Replace("`", "-"));
        }

        // MF Customer Number
        if (modifiedDtl.MFCustNum != originalDtl.MFCustNum)
        {
            changes.Add($"ShipDtl.MFCustNum[Line {packLine}]`"
                       + originalDtl.MFCustNum.ToString()
                       + "`"
                       + modifiedDtl.MFCustNum.ToString().Replace("`", "-"));
        }

        // MF Ship To Number
        if (modifiedDtl.MFShipToNum != originalDtl.MFShipToNum)
        {
            changes.Add($"ShipDtl.MFShipToNum[Line {packLine}]`"
                       + originalDtl.MFShipToNum.ToString()
                       + "`"
                       + modifiedDtl.MFShipToNum.ToString().Replace("`", "-"));
        }

        // Use OTMF
        if (modifiedDtl.UseOTMF != originalDtl.UseOTMF)
        {
            changes.Add($"ShipDtl.UseOTMF[Line {packLine}]`"
                       + originalDtl.UseOTMF.ToString()
                       + "`"
                       + modifiedDtl.UseOTMF.ToString().Replace("`", "-"));
        }

        // OTMF Name
        if (modifiedDtl.OTMFName != originalDtl.OTMFName)
        {
            changes.Add($"ShipDtl.OTMFName[Line {packLine}]`"
                       + originalDtl.OTMFName.ToString()
                       + "`"
                       + modifiedDtl.OTMFName.ToString().Replace("`", "-"));
        }

        // OTMF Address 1
        if (modifiedDtl.OTMFAddress1 != originalDtl.OTMFAddress1)
        {
            changes.Add($"ShipDtl.OTMFAddress1[Line {packLine}]`"
                       + originalDtl.OTMFAddress1.ToString()
                       + "`"
                       + modifiedDtl.OTMFAddress1.ToString().Replace("`", "-"));
        }

        // OTMF Address 2
        if (modifiedDtl.OTMFAddress2 != originalDtl.OTMFAddress2)
        {
            changes.Add($"ShipDtl.OTMFAddress2[Line {packLine}]`"
                       + originalDtl.OTMFAddress2.ToString()
                       + "`"
                       + modifiedDtl.OTMFAddress2.ToString().Replace("`", "-"));
        }

        // OTMF Address 3
        if (modifiedDtl.OTMFAddress3 != originalDtl.OTMFAddress3)
        {
            changes.Add($"ShipDtl.OTMFAddress3[Line {packLine}]`"
                       + originalDtl.OTMFAddress3.ToString()
                       + "`"
                       + modifiedDtl.OTMFAddress3.ToString().Replace("`", "-"));
        }

        // OTMF City
        if (modifiedDtl.OTMFCity != originalDtl.OTMFCity)
        {
            changes.Add($"ShipDtl.OTMFCity[Line {packLine}]`"
                       + originalDtl.OTMFCity.ToString()
                       + "`"
                       + modifiedDtl.OTMFCity.ToString().Replace("`", "-"));
        }

        // OTMF State
        if (modifiedDtl.OTMFState != originalDtl.OTMFState)
        {
            changes.Add($"ShipDtl.OTMFState[Line {packLine}]`"
                       + originalDtl.OTMFState.ToString()
                       + "`"
                       + modifiedDtl.OTMFState.ToString().Replace("`", "-"));
        }

        // OTMF ZIP
        if (modifiedDtl.OTMFZIP != originalDtl.OTMFZIP)
        {
            changes.Add($"ShipDtl.OTMFZIP[Line {packLine}]`"
                       + originalDtl.OTMFZIP.ToString()
                       + "`"
                       + modifiedDtl.OTMFZIP.ToString().Replace("`", "-"));
        }

        // OTMF Contact
        if (modifiedDtl.OTMFContact != originalDtl.OTMFContact)
        {
            changes.Add($"ShipDtl.OTMFContact[Line {packLine}]`"
                       + originalDtl.OTMFContact.ToString()
                       + "`"
                       + modifiedDtl.OTMFContact.ToString().Replace("`", "-"));
        }

        // OTMF Fax Number
        if (modifiedDtl.OTMFFaxNum != originalDtl.OTMFFaxNum)
        {
            changes.Add($"ShipDtl.OTMFFaxNum[Line {packLine}]`"
                       + originalDtl.OTMFFaxNum.ToString()
                       + "`"
                       + modifiedDtl.OTMFFaxNum.ToString().Replace("`", "-"));
        }

        // OTMF Phone Number
        if (modifiedDtl.OTMFPhoneNum != originalDtl.OTMFPhoneNum)
        {
            changes.Add($"ShipDtl.OTMFPhoneNum[Line {packLine}]`"
                       + originalDtl.OTMFPhoneNum.ToString()
                       + "`"
                       + modifiedDtl.OTMFPhoneNum.ToString().Replace("`", "-"));
        }

        // OTMF Country Number
        if (modifiedDtl.OTMFCountryNum != originalDtl.OTMFCountryNum)
        {
            changes.Add($"ShipDtl.OTMFCountryNum[Line {packLine}]`"
                       + originalDtl.OTMFCountryNum.ToString()
                       + "`"
                       + modifiedDtl.OTMFCountryNum.ToString().Replace("`", "-"));
        }

        // Renewal Number
        if (modifiedDtl.RenewalNbr != originalDtl.RenewalNbr)
        {
            changes.Add($"ShipDtl.RenewalNbr[Line {packLine}]`"
                       + originalDtl.RenewalNbr.ToString()
                       + "`"
                       + modifiedDtl.RenewalNbr.ToString().Replace("`", "-"));
        }

        // Ship Overs
        if (modifiedDtl.ShipOvers != originalDtl.ShipOvers)
        {
            changes.Add($"ShipDtl.ShipOvers[Line {packLine}]`"
                       + originalDtl.ShipOvers.ToString()
                       + "`"
                       + modifiedDtl.ShipOvers.ToString().Replace("`", "-"));
        }

        // Allowed Overs
        if (modifiedDtl.AllowedOvers != originalDtl.AllowedOvers)
        {
            changes.Add($"ShipDtl.AllowedOvers[Line {packLine}]`"
                       + originalDtl.AllowedOvers.ToString()
                       + "`"
                       + modifiedDtl.AllowedOvers.ToString().Replace("`", "-"));
        }

        // Allowed Unders
        if (modifiedDtl.AllowedUnders != originalDtl.AllowedUnders)
        {
            changes.Add($"ShipDtl.AllowedUnders[Line {packLine}]`"
                       + originalDtl.AllowedUnders.ToString()
                       + "`"
                       + modifiedDtl.AllowedUnders.ToString().Replace("`", "-"));
        }

        // Not Allocated Quantity
        if (modifiedDtl.NotAllocatedQty != originalDtl.NotAllocatedQty)
        {
            changes.Add($"ShipDtl.NotAllocatedQty[Line {packLine}]`"
                       + originalDtl.NotAllocatedQty.ToString()
                       + "`"
                       + modifiedDtl.NotAllocatedQty.ToString().Replace("`", "-"));
        }

        // PCID
        if (modifiedDtl.PCID != originalDtl.PCID)
        {
            changes.Add($"ShipDtl.PCID[Line {packLine}]`"
                       + originalDtl.PCID.ToString()
                       + "`"
                       + modifiedDtl.PCID.ToString().Replace("`", "-"));
        }

        // PCID Item Sequence
        if (modifiedDtl.PCIDItemSeq != originalDtl.PCIDItemSeq)
        {
            changes.Add($"ShipDtl.PCIDItemSeq[Line {packLine}]`"
                       + originalDtl.PCIDItemSeq.ToString()
                       + "`"
                       + modifiedDtl.PCIDItemSeq.ToString().Replace("`", "-"));
        }

        // Docking Station
        if (modifiedDtl.DockingStation != originalDtl.DockingStation)
        {
            changes.Add($"ShipDtl.DockingStation[Line {packLine}]`"
                       + originalDtl.DockingStation.ToString()
                       + "`"
                       + modifiedDtl.DockingStation.ToString().Replace("`", "-"));
        }

        // Use Ship Detail Info
        if (modifiedDtl.UseShipDtlInfo != originalDtl.UseShipDtlInfo)
        {
            changes.Add($"ShipDtl.UseShipDtlInfo[Line {packLine}]`"
                       + originalDtl.UseShipDtlInfo.ToString()
                       + "`"
                       + modifiedDtl.UseShipDtlInfo.ToString().Replace("`", "-"));
        }

        // Package Code Part Number
        if (modifiedDtl.PkgCodePartNum != originalDtl.PkgCodePartNum)
        {
            changes.Add($"ShipDtl.PkgCodePartNum[Line {packLine}]`"
                       + originalDtl.PkgCodePartNum.ToString()
                       + "`"
                       + modifiedDtl.PkgCodePartNum.ToString().Replace("`", "-"));
        }

        // Customer Container Part Number
        if (modifiedDtl.CustContainerPartNum != originalDtl.CustContainerPartNum)
        {
            changes.Add($"ShipDtl.CustContainerPartNum[Line {packLine}]`"
                       + originalDtl.CustContainerPartNum.ToString()
                       + "`"
                       + modifiedDtl.CustContainerPartNum.ToString().Replace("`", "-"));
        }

        // Label Type
        if (modifiedDtl.LabelType != originalDtl.LabelType)
        {
            changes.Add($"ShipDtl.LabelType[Line {packLine}]`"
                       + originalDtl.LabelType.ToString()
                       + "`"
                       + modifiedDtl.LabelType.ToString().Replace("`", "-"));
        }

        // Warranty Send To FSA
        if (modifiedDtl.WarrantySendToFSA != originalDtl.WarrantySendToFSA)
        {
            changes.Add($"ShipDtl.WarrantySendToFSA[Line {packLine}]`"
                       + originalDtl.WarrantySendToFSA.ToString()
                       + "`"
                       + modifiedDtl.WarrantySendToFSA.ToString().Replace("`", "-"));
        }

        // FSA Equipment
        if (modifiedDtl.FSAEquipment != originalDtl.FSAEquipment)
        {
            changes.Add($"ShipDtl.FSAEquipment[Line {packLine}]`"
                       + originalDtl.FSAEquipment.ToString()
                       + "`"
                       + modifiedDtl.FSAEquipment.ToString().Replace("`", "-"));
        }

        // Attribute Set ID
        if (modifiedDtl.AttributeSetID != originalDtl.AttributeSetID)
        {
            changes.Add($"ShipDtl.AttributeSetID[Line {packLine}]`"
                       + originalDtl.AttributeSetID.ToString()
                       + "`"
                       + modifiedDtl.AttributeSetID.ToString().Replace("`", "-"));
        }

        // Inventory Number of Pieces
        if (modifiedDtl.InventoryNumberOfPieces != originalDtl.InventoryNumberOfPieces)
        {
            changes.Add($"ShipDtl.InventoryNumberOfPieces[Line {packLine}]`"
                       + originalDtl.InventoryNumberOfPieces.ToString()
                       + "`"
                       + modifiedDtl.InventoryNumberOfPieces.ToString().Replace("`", "-"));
        }

        // Job Number of Pieces
        if (modifiedDtl.JobNumberOfPieces != originalDtl.JobNumberOfPieces)
        {
            changes.Add($"ShipDtl.JobNumberOfPieces[Line {packLine}]`"
                       + originalDtl.JobNumberOfPieces.ToString()
                       + "`"
                       + modifiedDtl.JobNumberOfPieces.ToString().Replace("`", "-"));
        }

        // MX Estimated Value
        if (modifiedDtl.MXEstValue != originalDtl.MXEstValue)
        {
            changes.Add($"ShipDtl.MXEstValue[Line {packLine}]`"
                       + originalDtl.MXEstValue.ToString()
                       + "`"
                       + modifiedDtl.MXEstValue.ToString().Replace("`", "-"));
        }

        // MX Estimated Value Currency Code
        if (modifiedDtl.MXEstValueCurrencyCode != originalDtl.MXEstValueCurrencyCode)
        {
            changes.Add($"ShipDtl.MXEstValueCurrencyCode[Line {packLine}]`"
                       + originalDtl.MXEstValueCurrencyCode.ToString()
                       + "`"
                       + modifiedDtl.MXEstValueCurrencyCode.ToString().Replace("`", "-"));
        }

        // MX Hazardous Shipment
        if (modifiedDtl.MXHazardousShipment != originalDtl.MXHazardousShipment)
        {
            changes.Add($"ShipDtl.MXHazardousShipment[Line {packLine}]`"
                       + originalDtl.MXHazardousShipment.ToString()
                       + "`"
                       + modifiedDtl.MXHazardousShipment.ToString().Replace("`", "-"));
        }

        // MX Hazardous Type
        if (modifiedDtl.MXHazardousType != originalDtl.MXHazardousType)
        {
            changes.Add($"ShipDtl.MXHazardousType[Line {packLine}]`"
                       + originalDtl.MXHazardousType.ToString()
                       + "`"
                       + modifiedDtl.MXHazardousType.ToString().Replace("`", "-"));
        }

        // MX Package Type
        if (modifiedDtl.MXPackageType != originalDtl.MXPackageType)
        {
            changes.Add($"ShipDtl.MXPackageType[Line {packLine}]`"
                       + originalDtl.MXPackageType.ToString()
                       + "`"
                       + modifiedDtl.MXPackageType.ToString().Replace("`", "-"));
        }

        // CN Declaration Bill Line
        if (modifiedDtl.CNDeclarationBillLine != originalDtl.CNDeclarationBillLine)
        {
            changes.Add($"ShipDtl.CNDeclarationBillLine[Line {packLine}]`"
                       + originalDtl.CNDeclarationBillLine.ToString()
                       + "`"
                       + modifiedDtl.CNDeclarationBillLine.ToString().Replace("`", "-"));
        }

        // Job Not Allocated Quantity
        if (modifiedDtl.JobNotAllocatedQty != originalDtl.JobNotAllocatedQty)
        {
            changes.Add($"ShipDtl.JobNotAllocatedQty[Line {packLine}]`"
                       + originalDtl.JobNotAllocatedQty.ToString()
                       + "`"
                       + modifiedDtl.JobNotAllocatedQty.ToString().Replace("`", "-"));
        }

        // Job Picked Auto Allocated Quantity
        if (modifiedDtl.JobPickedAutoAllocatedQty != originalDtl.JobPickedAutoAllocatedQty)
        {
            changes.Add($"ShipDtl.JobPickedAutoAllocatedQty[Line {packLine}]`"
                       + originalDtl.JobPickedAutoAllocatedQty.ToString()
                       + "`"
                       + modifiedDtl.JobPickedAutoAllocatedQty.ToString().Replace("`", "-"));
        }

        // Is WIP
        if (modifiedDtl.IsWIP != originalDtl.IsWIP)
        {
            changes.Add($"ShipDtl.IsWIP[Line {packLine}]`"
                       + originalDtl.IsWIP.ToString()
                       + "`"
                       + modifiedDtl.IsWIP.ToString().Replace("`", "-"));
        }

        // WIP PCID
        if (modifiedDtl.WIPPCID != originalDtl.WIPPCID)
        {
            changes.Add($"ShipDtl.WIPPCID[Line {packLine}]`"
                       + originalDtl.WIPPCID.ToString()
                       + "`"
                       + modifiedDtl.WIPPCID.ToString().Replace("`", "-"));
        }

        // WIP PCID Staging Number
        if (modifiedDtl.WIPPCIDStagingNum != originalDtl.WIPPCIDStagingNum)
        {
            changes.Add($"ShipDtl.WIPPCIDStagingNum[Line {packLine}]`"
                       + originalDtl.WIPPCIDStagingNum.ToString()
                       + "`"
                       + modifiedDtl.WIPPCIDStagingNum.ToString().Replace("`", "-"));
        }

        // WIP PCID Item Sequence
        if (modifiedDtl.WIPPCIDItemSeq != originalDtl.WIPPCIDItemSeq)
        {
            changes.Add($"ShipDtl.WIPPCIDItemSeq[Line {packLine}]`"
                       + originalDtl.WIPPCIDItemSeq.ToString()
                       + "`"
                       + modifiedDtl.WIPPCIDItemSeq.ToString().Replace("`", "-"));
        }



        // Line Type
        if (modifiedDtl.LineType != originalDtl.LineType)
        {
            changes.Add($"ShipDtl.LineType[Line {packLine}]`"
                       + originalDtl.LineType.ToString()
                       + "`"
                       + modifiedDtl.LineType.ToString().Replace("`", "-"));
        }

        // Lot Number
        if (modifiedDtl.LotNum != originalDtl.LotNum)
        {
            changes.Add($"ShipDtl.LotNum[Line {packLine}]`"
                       + originalDtl.LotNum.ToString()
                       + "`"
                       + modifiedDtl.LotNum.ToString().Replace("`", "-"));
        }

        // MF Customer Number
        if (modifiedDtl.MFCustNum != originalDtl.MFCustNum)
        {
            changes.Add($"ShipDtl.MFCustNum[Line {packLine}]`"
                       + originalDtl.MFCustNum.ToString()
                       + "`"
                       + modifiedDtl.MFCustNum.ToString().Replace("`", "-"));
        }



        // Order Line
        if (modifiedDtl.OrderLine != originalDtl.OrderLine)
        {
            changes.Add($"ShipDtl.OrderLine[Line {packLine}]`"
                       + originalDtl.OrderLine.ToString()
                       + "`"
                       + modifiedDtl.OrderLine.ToString().Replace("`", "-"));
        }

        // Order Release Number
        if (modifiedDtl.OrderRelNum != originalDtl.OrderRelNum)
        {
            changes.Add($"ShipDtl.OrderRelNum[Line {packLine}]`"
                       + originalDtl.OrderRelNum.ToString()
                       + "`"
                       + modifiedDtl.OrderRelNum.ToString().Replace("`", "-"));
        }



        // Warehouse Code
        if (modifiedDtl.WarehouseCode != originalDtl.WarehouseCode)
        {
            changes.Add($"ShipDtl.WarehouseCode[Line {packLine}]`"
                       + originalDtl.WarehouseCode.ToString()
                       + "`"
                       + modifiedDtl.WarehouseCode.ToString().Replace("`", "-"));
        }

        // Bin Number
        if (modifiedDtl.BinNum != originalDtl.BinNum)
        {
            changes.Add($"ShipDtl.BinNum[Line {packLine}]`"
                       + originalDtl.BinNum.ToString()
                       + "`"
                       + modifiedDtl.BinNum.ToString().Replace("`", "-"));
        }
    }
}

// =================================================================
// Final Processing
// =================================================================

// If changes were detected, call the function and log the changes
if (changes.Count > 0)
{
    callFunc = true;

    // Join all changes with the delimiter
    string allChanges = string.Join("`~`", changes);
    int totalLength = allChanges.Length;
    int maxChunkSize = 1000;

    // Assign chunks based on position - each exactly 1000 chars (or remaining)
    if (totalLength > 0)
    {
        changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
    }
    if (totalLength > 1000)
    {
        changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
    }
    if (totalLength > 2000)
    {
        changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
    }
    if (totalLength > 3000)
    {
        changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
    }
    if (totalLength > 4000)
    {
        changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
    }
    if (totalLength > 5000)
    {
        changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
    }
    if (totalLength > 6000)
    {
        changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
    }
    if (totalLength > 7000)
    {
        changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
    }
    if (totalLength > 8000)
    {
        changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
    }
    if (totalLength > 9000)
    {
        changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
    }

    // Set appropriate debug message
    if (totalLength > 10000)
    {
        test2 = $"Warning: {totalLength} characters of changes detected, only first 10000 logged";
    }
    else
    {
        test2 = $"Debug: {changes.Count} changes detected, {totalLength} characters logged";
    }
}
else
{
    test2 = "Debug: No changes detected";
}

// =================================================================
// End of BPM Script
// =================================================================
