changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;

// Debug: Check dataset before accessing
if (ds.Customer == null || ds.Customer.Count == 0)
{
    test1 = "Error: No customer data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.Customer.Count} customer records";

var modified = ds.Customer[0];
custID = modified.CustID;
test2 = "Debug: Successfully accessed ds.Customer[0]";

var original = (from dbCust in Db.Customer
                where dbCust.Company == companyID
                && dbCust.CustID == custID
                select dbCust).FirstOrDefault();

if (original == null)
{
    changesMade = $"New customer created: {modified.CustID}";
	callFunc = true;
}
else
{
    List<string> changes = new List<string>();

    // Check if there's a second customer record (modified version)
    // If not, use the first record (which should be the modified version)
    if (ds.Customer.Count > 1)
    {
        try
        {
            modified = ds.Customer[1];
            test2 = "Debug: Successfully accessed ds.Customer[1]";
        }
        catch (System.Exception ex)
        {
            test2 = $"Error accessing ds.Customer[1]: {ex.Message}";
            return;
        }
    }
    else
    {
        test2 = "Debug: Using ds.Customer[0] as modified (only 1 record)";
    }
    try
    {
        if (modified.CustID != custID)
            changes.Add("CustID`"
                        + (original.CustID.ToString())
                        + "`"
                        + (modified.CustID.ToString()).Replace("`", "-"));

        test2 = "Debug: CustID comparison completed";

        if (modified.Name != original.Name)
            changes.Add("Name`"
                        + (original.Name.ToString())
                        + "`"
                        + (modified.Name.ToString()).Replace("`", "-"));

        test2 = "Debug: Name comparison completed";
    }
    catch (System.Exception ex)
    {
        test2 = $"Error in field comparisons: {ex.Message} - Type: {ex.GetType().Name}";
        return;
    }
    if (modified.Address1 != original.Address1)
        changes.Add("Address1`" 
                    + (original.Address1.ToString()) 
                    + "`" 
                    + (modified.Address1.ToString()).Replace("`", "-"));

    if (modified.Address2 != original.Address2)
        changes.Add("Address2`" 
                    + (original.Address2.ToString()) 
                    + "`" 
                    + (modified.Address2.ToString()).Replace("`", "-"));
    if (modified.Address3 != original.Address3)
        changes.Add("Address3`" 
                    + (original.Address3.ToString()) 
                    + "`" 
                    + (modified.Address3.ToString()).Replace("`", "-"));
    if (modified.City != original.City)
        changes.Add("City`" 
                    + (original.City.ToString()) 
                    + "`" 
                    + (modified.City.ToString()).Replace("`", "-"));
    if (modified.State != original.State)
        changes.Add("State`" 
                    + (original.State.ToString()) 
                    + "`" 
                    + (modified.State.ToString()).Replace("`", "-"));
    if (modified.Zip != original.Zip)
        changes.Add("Zip`" 
                    + (original.Zip.ToString()) 
                    + "`" 
                    + (modified.Zip.ToString()).Replace("`", "-"));
    if (modified.Country != original.Country)
        changes.Add("Country`" 
                    + (original.Country.ToString()) 
                    + "`" 
                    + (modified.Country.ToString()).Replace("`", "-"));
    if (modified.ResaleID != original.ResaleID)
        changes.Add("ResaleID`" 
                    + (original.ResaleID.ToString()) 
                    + "`" 
                    + (modified.ResaleID.ToString()).Replace("`", "-"));
    if (modified.SalesRepCode != original.SalesRepCode)
        changes.Add("SalesRepCode`" 
                    + (original.SalesRepCode.ToString()) 
                    + "`" 
                    + (modified.SalesRepCode.ToString()).Replace("`", "-"));
    if (modified.TerritoryID != original.TerritoryID)
        changes.Add("TerritoryID`" 
                    + (original.TerritoryID.ToString()) 
                    + "`" 
                    + (modified.TerritoryID.ToString()).Replace("`", "-"));
    if (modified.ShipToNum != original.ShipToNum)
        changes.Add("ShipToNum`" 
                    + (original.ShipToNum.ToString()) 
                    + "`" 
                    + (modified.ShipToNum.ToString()).Replace("`", "-"));
    if (modified.TermsCode != original.TermsCode)
        changes.Add("TermsCode`" 
                    + (original.TermsCode.ToString()) 
                    + "`" 
                    + (modified.TermsCode.ToString()).Replace("`", "-"));
    if (modified.ShipViaCode != original.ShipViaCode)
        changes.Add("ShipViaCode`" 
                    + (original.ShipViaCode.ToString()) 
                    + "`" 
                    + (modified.ShipViaCode.ToString()).Replace("`", "-"));
    if (modified.PrintStatements != original.PrintStatements)
        changes.Add("PrintStatements`" 
                    + (original.PrintStatements.ToString()) 
                    + "`" 
                    + (modified.PrintStatements.ToString()).Replace("`", "-"));
    if (modified.PrintLabels != original.PrintLabels)
        changes.Add("PrintLabels`" 
                    + (original.PrintLabels.ToString()) 
                    + "`" 
                    + (modified.PrintLabels.ToString()).Replace("`", "-"));
    if (modified.PrintAck != original.PrintAck)
        changes.Add("PrintAck`" 
                    + (original.PrintAck.ToString()) 
                    + "`" 
                    + (modified.PrintAck.ToString()).Replace("`", "-"));
    if (modified.FinCharges != original.FinCharges)
        changes.Add("FinCharges`" 
                    + (original.FinCharges.ToString()) 
                    + "`" 
                    + (modified.FinCharges.ToString()).Replace("`", "-"));
    if (modified.CreditHold != original.CreditHold)
        changes.Add("CreditHold`" 
                    + (original.CreditHold.ToString()) 
                    + "`" 
                    + (modified.CreditHold.ToString()).Replace("`", "-"));
    if (modified.GroupCode != original.GroupCode)
        changes.Add("GroupCode`" 
                    + (original.GroupCode.ToString()) 
                    + "`" 
                    + (modified.GroupCode.ToString()).Replace("`", "-"));
    if (modified.DiscountPercent != original.DiscountPercent)
        changes.Add("DiscountPercent`" 
                    + (original.DiscountPercent.ToString()) 
                    + "`" 
                    + (modified.DiscountPercent.ToString()).Replace("`", "-"));
    if (modified.PrimPCon != original.PrimPCon)
        changes.Add("PrimPCon`" 
                    + (original.PrimPCon.ToString()) 
                    + "`" 
                    + (modified.PrimPCon.ToString()).Replace("`", "-"));
    if (modified.PrimBCon != original.PrimBCon)
        changes.Add("PrimBCon`" 
                    + (original.PrimBCon.ToString()) 
                    + "`" 
                    + (modified.PrimBCon.ToString()).Replace("`", "-"));
    if (modified.PrimSCon != original.PrimSCon)
        changes.Add("PrimSCon`" 
                    + (original.PrimSCon.ToString()) 
                    + "`" 
                    + (modified.PrimSCon.ToString()).Replace("`", "-"));
    if (modified.Comment != original.Comment)
        changes.Add("Comment`" 
                    + (original.Comment.ToString()) 
                    + "`" 
                    + (modified.Comment.ToString()).Replace("`", "-"));
    if (modified.EstDate != original.EstDate)
        changes.Add("EstDate`" 
                    + (original.EstDate.ToString()) 
                    + "`" 
                    + (modified.EstDate.ToString()).Replace("`", "-"));
    if (modified.FaxNum != original.FaxNum)
        changes.Add("FaxNum`" 
                    + (original.FaxNum.ToString()) 
                    + "`" 
                    + (modified.FaxNum.ToString()).Replace("`", "-"));
    if (modified.PhoneNum != original.PhoneNum)
        changes.Add("PhoneNum`" 
                    + (original.PhoneNum.ToString()) 
                    + "`" 
                    + (modified.PhoneNum.ToString()).Replace("`", "-"));
    if (modified.TaxExempt != original.TaxExempt)
        changes.Add("TaxExempt`" 
                    + (original.TaxExempt.ToString()) 
                    + "`" 
                    + (modified.TaxExempt.ToString()).Replace("`", "-"));
    if (modified.MarkUpID != original.MarkUpID)
        changes.Add("MarkUpID`" 
                    + (original.MarkUpID.ToString()) 
                    + "`" 
                    + (modified.MarkUpID.ToString()).Replace("`", "-"));
    if (modified.BillDay != original.BillDay)
        changes.Add("BillDay`" 
                    + (original.BillDay.ToString()) 
                    + "`" 
                    + (modified.BillDay.ToString()).Replace("`", "-"));
    if (modified.OneInvPerPS != original.OneInvPerPS)
        changes.Add("OneInvPerPS`" 
                    + (original.OneInvPerPS.ToString()) 
                    + "`" 
                    + (modified.OneInvPerPS.ToString()).Replace("`", "-"));
    if (modified.DefaultFOB != original.DefaultFOB)
        changes.Add("DefaultFOB`" 
                    + (original.DefaultFOB.ToString()) 
                    + "`" 
                    + (modified.DefaultFOB.ToString()).Replace("`", "-"));
    if (modified.CreditIncludeOrders != original.CreditIncludeOrders)
        changes.Add("CreditIncludeOrders`" 
                    + (original.CreditIncludeOrders.ToString()) 
                    + "`" 
                    + (modified.CreditIncludeOrders.ToString()).Replace("`", "-"));
    if (modified.CreditReviewDate != original.CreditReviewDate)
        changes.Add("CreditReviewDate`" 
                    + (original.CreditReviewDate.ToString()) 
                    + "`" 
                    + (modified.CreditReviewDate.ToString()).Replace("`", "-"));
    if (modified.CreditHoldDate != original.CreditHoldDate)
        changes.Add("CreditHoldDate`" 
                    + (original.CreditHoldDate.ToString()) 
                    + "`" 
                    + (modified.CreditHoldDate.ToString()).Replace("`", "-"));
    if (modified.CreditHoldSource != original.CreditHoldSource)
        changes.Add("CreditHoldSource`" 
                    + (original.CreditHoldSource.ToString()) 
                    + "`" 
                    + (modified.CreditHoldSource.ToString()).Replace("`", "-"));
    if (modified.CreditClearUserID != original.CreditClearUserID)
        changes.Add("CreditClearUserID`" 
                    + (original.CreditClearUserID.ToString()) 
                    + "`" 
                    + (modified.CreditClearUserID.ToString()).Replace("`", "-"));
    if (modified.CreditClearDate != original.CreditClearDate)
        changes.Add("CreditClearDate`" 
                    + (original.CreditClearDate.ToString()) 
                    + "`" 
                    + (modified.CreditClearDate.ToString()).Replace("`", "-"));
    if (modified.CreditClearTime != original.CreditClearTime)
        changes.Add("CreditClearTime`" 
                    + (original.CreditClearTime.ToString()) 
                    + "`" 
                    + (modified.CreditClearTime.ToString()).Replace("`", "-"));
    if (modified.EDICode != original.EDICode)
        changes.Add("EDICode`" 
                    + (original.EDICode.ToString()) 
                    + "`" 
                    + (modified.EDICode.ToString()).Replace("`", "-"));
    if (modified.CurrencyCode != original.CurrencyCode)
        changes.Add("CurrencyCode`" 
                    + (original.CurrencyCode.ToString()) 
                    + "`" 
                    + (modified.CurrencyCode.ToString()).Replace("`", "-"));
    if (modified.CountryNum != original.CountryNum)
        changes.Add("CountryNum`" 
                    + (original.CountryNum.ToString()) 
                    + "`" 
                    + (modified.CountryNum.ToString()).Replace("`", "-"));
    if (modified.LangNameID != original.LangNameID)
        changes.Add("LangNameID`" 
                    + (original.LangNameID.ToString()) 
                    + "`" 
                    + (modified.LangNameID.ToString()).Replace("`", "-"));
    if (modified.BorderCrossing != original.BorderCrossing)
        changes.Add("BorderCrossing`" 
                    + (original.BorderCrossing.ToString()) 
                    + "`" 
                    + (modified.BorderCrossing.ToString()).Replace("`", "-"));
    if (modified.FormatStr != original.FormatStr)
        changes.Add("FormatStr`" 
                    + (original.FormatStr.ToString()) 
                    + "`" 
                    + (modified.FormatStr.ToString()).Replace("`", "-"));
    if (modified.BTName != original.BTName)
        changes.Add("BTName`" 
                    + (original.BTName.ToString()) 
                    + "`" 
                    + (modified.BTName.ToString()).Replace("`", "-"));
    if (modified.BTAddress1 != original.BTAddress1)
        changes.Add("BTAddress1`" 
                    + (original.BTAddress1.ToString()) 
                    + "`" 
                    + (modified.BTAddress1.ToString()).Replace("`", "-"));
    if (modified.BTAddress2 != original.BTAddress2)
        changes.Add("BTAddress2`" 
                    + (original.BTAddress2.ToString()) 
                    + "`" 
                    + (modified.BTAddress2.ToString()).Replace("`", "-"));
    if (modified.BTAddress3 != original.BTAddress3)
        changes.Add("BTAddress3`" 
                    + (original.BTAddress3.ToString()) 
                    + "`" 
                    + (modified.BTAddress3.ToString()).Replace("`", "-"));
    if (modified.BTCity != original.BTCity)
        changes.Add("BTCity`" 
                    + (original.BTCity.ToString()) 
                    + "`" 
                    + (modified.BTCity.ToString()).Replace("`", "-"));
    if (modified.BTState != original.BTState)
        changes.Add("BTState`" 
                    + (original.BTState.ToString()) 
                    + "`" 
                    + (modified.BTState.ToString()).Replace("`", "-"));
    if (modified.BTZip != original.BTZip)
        changes.Add("BTZip`" 
                    + (original.BTZip.ToString()) 
                    + "`" 
                    + (modified.BTZip.ToString()).Replace("`", "-"));
    if (modified.BTCountryNum != original.BTCountryNum)
        changes.Add("BTCountryNum`" 
                    + (original.BTCountryNum.ToString()) 
                    + "`" 
                    + (modified.BTCountryNum.ToString()).Replace("`", "-"));
    if (modified.BTCountry != original.BTCountry)
        changes.Add("BTCountry`" 
                    + (original.BTCountry.ToString()) 
                    + "`" 
                    + (modified.BTCountry.ToString()).Replace("`", "-"));
    if (modified.BTPhoneNum != original.BTPhoneNum)
        changes.Add("BTPhoneNum`" 
                    + (original.BTPhoneNum.ToString()) 
                    + "`" 
                    + (modified.BTPhoneNum.ToString()).Replace("`", "-"));
    if (modified.BTFaxNum != original.BTFaxNum)
        changes.Add("BTFaxNum`" 
                    + (original.BTFaxNum.ToString()) 
                    + "`" 
                    + (modified.BTFaxNum.ToString()).Replace("`", "-"));
    if (modified.BTFormatStr != original.BTFormatStr)
        changes.Add("BTFormatStr`" 
                    + (original.BTFormatStr.ToString()) 
                    + "`" 
                    + (modified.BTFormatStr.ToString()).Replace("`", "-"));
    if (modified.ParentCustNum != original.ParentCustNum)
        changes.Add("ParentCustNum`" 
                    + (original.ParentCustNum.ToString()) 
                    + "`" 
                    + (modified.ParentCustNum.ToString()).Replace("`", "-"));
    if (modified.TaxRegionCode != original.TaxRegionCode)
        changes.Add("TaxRegionCode`" 
                    + (original.TaxRegionCode.ToString()) 
                    + "`" 
                    + (modified.TaxRegionCode.ToString()).Replace("`", "-"));
    if (modified.ICCust != original.ICCust)
        changes.Add("ICCust`" 
                    + (original.ICCust.ToString()) 
                    + "`" 
                    + (modified.ICCust.ToString()).Replace("`", "-"));
    if (modified.ContBillDay != original.ContBillDay)
        changes.Add("ContBillDay`" 
                    + (original.ContBillDay.ToString()) 
                    + "`" 
                    + (modified.ContBillDay.ToString()).Replace("`", "-"));
    if (modified.EMailAddress != original.EMailAddress)
        changes.Add("EMailAddress`" 
                    + (original.EMailAddress.ToString()) 
                    + "`" 
                    + (modified.EMailAddress.ToString()).Replace("`", "-"));
    if (modified.ShippingQualifier != original.ShippingQualifier)
        changes.Add("ShippingQualifier`" 
                    + (original.ShippingQualifier.ToString()) 
                    + "`" 
                    + (modified.ShippingQualifier.ToString()).Replace("`", "-"));
    if (modified.AllocPriorityCode != original.AllocPriorityCode)
        changes.Add("AllocPriorityCode`" 
                    + (original.AllocPriorityCode.ToString()) 
                    + "`" 
                    + (modified.AllocPriorityCode.ToString()).Replace("`", "-"));
    if (modified.ReservePriorityCode != original.ReservePriorityCode)
        changes.Add("ReservePriorityCode`" 
                    + (original.ReservePriorityCode.ToString()) 
                    + "`" 
                    + (modified.ReservePriorityCode.ToString()).Replace("`", "-"));
    if (modified.LinkPortNum != original.LinkPortNum)
        changes.Add("LinkPortNum`" 
                    + (original.LinkPortNum.ToString()) 
                    + "`" 
                    + (modified.LinkPortNum.ToString()).Replace("`", "-"));
    if (modified.WebCustomer != original.WebCustomer)
        changes.Add("WebCustomer`" 
                    + (original.WebCustomer.ToString()) 
                    + "`" 
                    + (modified.WebCustomer.ToString()).Replace("`", "-"));
    if (modified.CustomerType != original.CustomerType)
        changes.Add("CustomerType`" 
                    + (original.CustomerType.ToString()) 
                    + "`" 
                    + (modified.CustomerType.ToString()).Replace("`", "-"));
    if (modified.NoContact != original.NoContact)
        changes.Add("NoContact`" 
                    + (original.NoContact.ToString()) 
                    + "`" 
                    + (modified.NoContact.ToString()).Replace("`", "-"));
    if (modified.TerritoryLock != original.TerritoryLock)
        changes.Add("TerritoryLock`" 
                    + (original.TerritoryLock.ToString()) 
                    + "`" 
                    + (modified.TerritoryLock.ToString()).Replace("`", "-"));
    if (modified.CustURL != original.CustURL)
        changes.Add("CustURL`" 
                    + (original.CustURL.ToString()) 
                    + "`" 
                    + (modified.CustURL.ToString()).Replace("`", "-"));
    if (modified.PendingTerritoryID != original.PendingTerritoryID)
        changes.Add("PendingTerritoryID`" 
                    + (original.PendingTerritoryID.ToString()) 
                    + "`" 
                    + (modified.PendingTerritoryID.ToString()).Replace("`", "-"));
    if (modified.ExtID != original.ExtID)
        changes.Add("ExtID`" 
                    + (original.ExtID.ToString()) 
                    + "`" 
                    + (modified.ExtID.ToString()).Replace("`", "-"));
    if (modified.ConsolidateSO != original.ConsolidateSO)
        changes.Add("ConsolidateSO`" 
                    + (original.ConsolidateSO.ToString()) 
                    + "`" 
                    + (modified.ConsolidateSO.ToString()).Replace("`", "-"));
    if (modified.BillFrequency != original.BillFrequency)
        changes.Add("BillFrequency`" 
                    + (original.BillFrequency.ToString()) 
                    + "`" 
                    + (modified.BillFrequency.ToString()).Replace("`", "-"));
    if (modified.CreditIncludePI != original.CreditIncludePI)
        changes.Add("CreditIncludePI`" 
                    + (original.CreditIncludePI.ToString()) 
                    + "`" 
                    + (modified.CreditIncludePI.ToString()).Replace("`", "-"));
    if (modified.GlobalCust != original.GlobalCust)
        changes.Add("GlobalCust`" 
                    + (original.GlobalCust.ToString()) 
                    + "`" 
                    + (modified.GlobalCust.ToString()).Replace("`", "-"));
    if (modified.ICTrader != original.ICTrader)
        changes.Add("ICTrader`" 
                    + (original.ICTrader.ToString()) 
                    + "`" 
                    + (modified.ICTrader.ToString()).Replace("`", "-"));
    if (modified.TaxAuthorityCode != original.TaxAuthorityCode)
        changes.Add("TaxAuthorityCode`" 
                    + (original.TaxAuthorityCode.ToString()) 
                    + "`" 
                    + (modified.TaxAuthorityCode.ToString()).Replace("`", "-"));
    if (modified.ExternalDeliveryNote != original.ExternalDeliveryNote)
        changes.Add("ExternalDeliveryNote`" 
                    + (original.ExternalDeliveryNote.ToString()) 
                    + "`" 
                    + (modified.ExternalDeliveryNote.ToString()).Replace("`", "-"));
    if (modified.GlobalCredIncOrd != original.GlobalCredIncOrd)
        changes.Add("GlobalCredIncOrd`" 
                    + (original.GlobalCredIncOrd.ToString()) 
                    + "`" 
                    + (modified.GlobalCredIncOrd.ToString()).Replace("`", "-"));
    if (modified.GlobalCredIncPI != original.GlobalCredIncPI)
        changes.Add("GlobalCredIncPI`" 
                    + (original.GlobalCredIncPI.ToString()) 
                    + "`" 
                    + (modified.GlobalCredIncPI.ToString()).Replace("`", "-"));
    if (modified.GlobalCurrencyCode != original.GlobalCurrencyCode)
        changes.Add("GlobalCurrencyCode`" 
                    + (original.GlobalCurrencyCode.ToString()) 
                    + "`" 
                    + (modified.GlobalCurrencyCode.ToString()).Replace("`", "-"));
    if (modified.ExternalID != original.ExternalID)
        changes.Add("ExternalID`" 
                    + (original.ExternalID.ToString()) 
                    + "`" 
                    + (modified.ExternalID.ToString()).Replace("`", "-"));
    if (modified.GlobalCreditHold != original.GlobalCreditHold)
        changes.Add("GlobalCreditHold`" 
                    + (original.GlobalCreditHold.ToString()) 
                    + "`" 
                    + (modified.GlobalCreditHold.ToString()).Replace("`", "-"));
    if (modified.GlobalLock != original.GlobalLock)
        changes.Add("GlobalLock`" 
                    + (original.GlobalLock.ToString()) 
                    + "`" 
                    + (modified.GlobalLock.ToString()).Replace("`", "-"));
    if (modified.CheckDuplicatePO != original.CheckDuplicatePO)
        changes.Add("CheckDuplicatePO`" 
                    + (original.CheckDuplicatePO.ToString()) 
                    + "`" 
                    + (modified.CheckDuplicatePO.ToString()).Replace("`", "-"));
    if (modified.CreditLimit != original.CreditLimit)
        changes.Add("CreditLimit`" 
                    + (original.CreditLimit.ToString()) 
                    + "`" 
                    + (modified.CreditLimit.ToString()).Replace("`", "-"));
    if (modified.CustPILimit != original.CustPILimit)
        changes.Add("CustPILimit`" 
                    + (original.CustPILimit.ToString()) 
                    + "`" 
                    + (modified.CustPILimit.ToString()).Replace("`", "-"));
    if (modified.GlobalCreditLimit != original.GlobalCreditLimit)
        changes.Add("GlobalCreditLimit`" 
                    + (original.GlobalCreditLimit.ToString()) 
                    + "`" 
                    + (modified.GlobalCreditLimit.ToString()).Replace("`", "-"));
    if (modified.GlobalPILimit != original.GlobalPILimit)
        changes.Add("GlobalPILimit`" 
                    + (original.GlobalPILimit.ToString()) 
                    + "`" 
                    + (modified.GlobalPILimit.ToString()).Replace("`", "-"));
    if (modified.DocGlobalCreditLimit != original.DocGlobalCreditLimit)
        changes.Add("DocGlobalCreditLimit`" 
                    + (original.DocGlobalCreditLimit.ToString()) 
                    + "`" 
                    + (modified.DocGlobalCreditLimit.ToString()).Replace("`", "-"));
    if (modified.DocGlobalPILimit != original.DocGlobalPILimit)
        changes.Add("DocGlobalPILimit`" 
                    + (original.DocGlobalPILimit.ToString()) 
                    + "`" 
                    + (modified.DocGlobalPILimit.ToString()).Replace("`", "-"));
    if (modified.RfqAttachAllow != original.RfqAttachAllow)
        changes.Add("RfqAttachAllow`" 
                    + (original.RfqAttachAllow.ToString()) 
                    + "`" 
                    + (modified.RfqAttachAllow.ToString()).Replace("`", "-"));
    if (modified.DiscountQualifier != original.DiscountQualifier)
        changes.Add("DiscountQualifier`" 
                    + (original.DiscountQualifier.ToString()) 
                    + "`" 
                    + (modified.DiscountQualifier.ToString()).Replace("`", "-"));
    if (modified.AllowAltBillTo != original.AllowAltBillTo)
        changes.Add("AllowAltBillTo`" 
                    + (original.AllowAltBillTo.ToString()) 
                    + "`" 
                    + (modified.AllowAltBillTo.ToString()).Replace("`", "-"));
    if (modified.DemandDeliveryDays != original.DemandDeliveryDays)
        changes.Add("DemandDeliveryDays`" 
                    + (original.DemandDeliveryDays.ToString()) 
                    + "`" 
                    + (modified.DemandDeliveryDays.ToString()).Replace("`", "-"));
    if (modified.DemandDateType != original.DemandDateType)
        changes.Add("DemandDateType`" 
                    + (original.DemandDateType.ToString()) 
                    + "`" 
                    + (modified.DemandDateType.ToString()).Replace("`", "-"));
    if (modified.DemandAddLeadTime != original.DemandAddLeadTime)
        changes.Add("DemandAddLeadTime`" 
                    + (original.DemandAddLeadTime.ToString()) 
                    + "`" 
                    + (modified.DemandAddLeadTime.ToString()).Replace("`", "-"));
    if (modified.DemandAddAction != original.DemandAddAction)
        changes.Add("DemandAddAction`" 
                    + (original.DemandAddAction.ToString()) 
                    + "`" 
                    + (modified.DemandAddAction.ToString()).Replace("`", "-"));
    if (modified.DemandChangeLeadTime != original.DemandChangeLeadTime)
        changes.Add("DemandChangeLeadTime`" 
                    + (original.DemandChangeLeadTime.ToString()) 
                    + "`" 
                    + (modified.DemandChangeLeadTime.ToString()).Replace("`", "-"));
    if (modified.DemandChangeAction != original.DemandChangeAction)
        changes.Add("DemandChangeAction`" 
                    + (original.DemandChangeAction.ToString()) 
                    + "`" 
                    + (modified.DemandChangeAction.ToString()).Replace("`", "-"));
    if (modified.DemandCancelLeadTime != original.DemandCancelLeadTime)
        changes.Add("DemandCancelLeadTime`" 
                    + (original.DemandCancelLeadTime.ToString()) 
                    + "`" 
                    + (modified.DemandCancelLeadTime.ToString()).Replace("`", "-"));
    if (modified.DemandCancelAction != original.DemandCancelAction)
        changes.Add("DemandCancelAction`" 
                    + (original.DemandCancelAction.ToString()) 
                    + "`" 
                    + (modified.DemandCancelAction.ToString()).Replace("`", "-"));
    if (modified.DemandNewLineLeadTime != original.DemandNewLineLeadTime)
        changes.Add("DemandNewLineLeadTime`" 
                    + (original.DemandNewLineLeadTime.ToString()) 
                    + "`" 
                    + (modified.DemandNewLineLeadTime.ToString()).Replace("`", "-"));
    if (modified.DemandNewLineAction != original.DemandNewLineAction)
        changes.Add("DemandNewLineAction`" 
                    + (original.DemandNewLineAction.ToString()) 
                    + "`" 
                    + (modified.DemandNewLineAction.ToString()).Replace("`", "-"));
    if (modified.DemandQtyChangeLeadTime != original.DemandQtyChangeLeadTime)
        changes.Add("DemandQtyChangeLeadTime`" 
                    + (original.DemandQtyChangeLeadTime.ToString()) 
                    + "`" 
                    + (modified.DemandQtyChangeLeadTime.ToString()).Replace("`", "-"));
    if (modified.DemandQtyChangeAction != original.DemandQtyChangeAction)
        changes.Add("DemandQtyChangeAction`" 
                    + (original.DemandQtyChangeAction.ToString()) 
                    + "`" 
                    + (modified.DemandQtyChangeAction.ToString()).Replace("`", "-"));
    if (modified.DemandChangeDateLeadTime != original.DemandChangeDateLeadTime)
        changes.Add("DemandChangeDateLeadTime`" 
                    + (original.DemandChangeDateLeadTime.ToString()) 
                    + "`" 
                    + (modified.DemandChangeDateLeadTime.ToString()).Replace("`", "-"));
    if (modified.DemandChangeDateAction != original.DemandChangeDateAction)
        changes.Add("DemandChangeDateAction`" 
                    + (original.DemandChangeDateAction.ToString()) 
                    + "`" 
                    + (modified.DemandChangeDateAction.ToString()).Replace("`", "-"));
    if (modified.TradingPartnerName != original.TradingPartnerName)
        changes.Add("TradingPartnerName`" 
                    + (original.TradingPartnerName.ToString()) 
                    + "`" 
                    + (modified.TradingPartnerName.ToString()).Replace("`", "-"));
    if (modified.ResDelivery != original.ResDelivery)
        changes.Add("ResDelivery`" 
                    + (original.ResDelivery.ToString()) 
                    + "`" 
                    + (modified.ResDelivery.ToString()).Replace("`", "-"));
    if (modified.SatDelivery != original.SatDelivery)
        changes.Add("SatDelivery`" 
                    + (original.SatDelivery.ToString()) 
                    + "`" 
                    + (modified.SatDelivery.ToString()).Replace("`", "-"));
    if (modified.SatPickup != original.SatPickup)
        changes.Add("SatPickup`" 
                    + (original.SatPickup.ToString()) 
                    + "`" 
                    + (modified.SatPickup.ToString()).Replace("`", "-"));
    if (modified.Hazmat != original.Hazmat)
        changes.Add("Hazmat`" 
                    + (original.Hazmat.ToString()) 
                    + "`" 
                    + (modified.Hazmat.ToString()).Replace("`", "-"));
    if (modified.DocOnly != original.DocOnly)
        changes.Add("DocOnly`" 
                    + (original.DocOnly.ToString()) 
                    + "`" 
                    + (modified.DocOnly.ToString()).Replace("`", "-"));
    if (modified.RefNotes != original.RefNotes)
        changes.Add("RefNotes`" 
                    + (original.RefNotes.ToString()) 
                    + "`" 
                    + (modified.RefNotes.ToString()).Replace("`", "-"));
    if (modified.ApplyChrg != original.ApplyChrg)
        changes.Add("ApplyChrg`" 
                    + (original.ApplyChrg.ToString()) 
                    + "`" 
                    + (modified.ApplyChrg.ToString()).Replace("`", "-"));
    if (modified.ChrgAmount != original.ChrgAmount)
        changes.Add("ChrgAmount`" 
                    + (original.ChrgAmount.ToString()) 
                    + "`" 
                    + (modified.ChrgAmount.ToString()).Replace("`", "-"));
    if (modified.COD != original.COD)
        changes.Add("COD`" 
                    + (original.COD.ToString()) 
                    + "`" 
                    + (modified.COD.ToString()).Replace("`", "-"));
    if (modified.CODFreight != original.CODFreight)
        changes.Add("CODFreight`" 
                    + (original.CODFreight.ToString()) 
                    + "`" 
                    + (modified.CODFreight.ToString()).Replace("`", "-"));
    if (modified.CODCheck != original.CODCheck)
        changes.Add("CODCheck`" 
                    + (original.CODCheck.ToString()) 
                    + "`" 
                    + (modified.CODCheck.ToString()).Replace("`", "-"));
    if (modified.CODAmount != original.CODAmount)
        changes.Add("CODAmount`" 
                    + (original.CODAmount.ToString()) 
                    + "`" 
                    + (modified.CODAmount.ToString()).Replace("`", "-"));
    if (modified.GroundType != original.GroundType)
        changes.Add("GroundType`" 
                    + (original.GroundType.ToString()) 
                    + "`" 
                    + (modified.GroundType.ToString()).Replace("`", "-"));
    if (modified.NotifyFlag != original.NotifyFlag)
        changes.Add("NotifyFlag`" 
                    + (original.NotifyFlag.ToString()) 
                    + "`" 
                    + (modified.NotifyFlag.ToString()).Replace("`", "-"));
    if (modified.NotifyEMail != original.NotifyEMail)
        changes.Add("NotifyEMail`" 
                    + (original.NotifyEMail.ToString()) 
                    + "`" 
                    + (modified.NotifyEMail.ToString()).Replace("`", "-"));
    if (modified.DeclaredIns != original.DeclaredIns)
        changes.Add("DeclaredIns`" 
                    + (original.DeclaredIns.ToString()) 
                    + "`" 
                    + (modified.DeclaredIns.ToString()).Replace("`", "-"));
    if (modified.DeclaredAmt != original.DeclaredAmt)
        changes.Add("DeclaredAmt`" 
                    + (original.DeclaredAmt.ToString()) 
                    + "`" 
                    + (modified.DeclaredAmt.ToString()).Replace("`", "-"));
    if (modified.PeriodicityCode != original.PeriodicityCode)
        changes.Add("PeriodicityCode`" 
                    + (original.PeriodicityCode.ToString()) 
                    + "`" 
                    + (modified.PeriodicityCode.ToString()).Replace("`", "-"));
    if (modified.ServSignature != original.ServSignature)
        changes.Add("ServSignature`" 
                    + (original.ServSignature.ToString()) 
                    + "`" 
                    + (modified.ServSignature.ToString()).Replace("`", "-"));
    if (modified.ServAlert != original.ServAlert)
        changes.Add("ServAlert`" 
                    + (original.ServAlert.ToString()) 
                    + "`" 
                    + (modified.ServAlert.ToString()).Replace("`", "-"));
    if (modified.ServHomeDel != original.ServHomeDel)
        changes.Add("ServHomeDel`" 
                    + (original.ServHomeDel.ToString()) 
                    + "`" 
                    + (modified.ServHomeDel.ToString()).Replace("`", "-"));
    if (modified.DeliveryType != original.DeliveryType)
        changes.Add("DeliveryType`" 
                    + (original.DeliveryType.ToString()) 
                    + "`" 
                    + (modified.DeliveryType.ToString()).Replace("`", "-"));
    if (modified.ServDeliveryDate != original.ServDeliveryDate)
        changes.Add("ServDeliveryDate`" 
                    + (original.ServDeliveryDate.ToString()) 
                    + "`" 
                    + (modified.ServDeliveryDate.ToString()).Replace("`", "-"));
    if (modified.ServPhone != original.ServPhone)
        changes.Add("ServPhone`" 
                    + (original.ServPhone.ToString()) 
                    + "`" 
                    + (modified.ServPhone.ToString()).Replace("`", "-"));
    if (modified.ServInstruct != original.ServInstruct)
        changes.Add("ServInstruct`" 
                    + (original.ServInstruct.ToString()) 
                    + "`" 
                    + (modified.ServInstruct.ToString()).Replace("`", "-"));
    if (modified.ServRelease != original.ServRelease)
        changes.Add("ServRelease`" 
                    + (original.ServRelease.ToString()) 
                    + "`" 
                    + (modified.ServRelease.ToString()).Replace("`", "-"));
    if (modified.ServAuthNum != original.ServAuthNum)
        changes.Add("ServAuthNum`" 
                    + (original.ServAuthNum.ToString()) 
                    + "`" 
                    + (modified.ServAuthNum.ToString()).Replace("`", "-"));
    if (modified.ServRef1 != original.ServRef1)
        changes.Add("ServRef1`" 
                    + (original.ServRef1.ToString()) 
                    + "`" 
                    + (modified.ServRef1.ToString()).Replace("`", "-"));
    if (modified.ServRef2 != original.ServRef2)
        changes.Add("ServRef2`" 
                    + (original.ServRef2.ToString()) 
                    + "`" 
                    + (modified.ServRef2.ToString()).Replace("`", "-"));
    if (modified.ServRef3 != original.ServRef3)
        changes.Add("ServRef3`" 
                    + (original.ServRef3.ToString()) 
                    + "`" 
                    + (modified.ServRef3.ToString()).Replace("`", "-"));
    if (modified.ServRef4 != original.ServRef4)
        changes.Add("ServRef4`" 
                    + (original.ServRef4.ToString()) 
                    + "`" 
                    + (modified.ServRef4.ToString()).Replace("`", "-"));
    if (modified.ServRef5 != original.ServRef5)
        changes.Add("ServRef5`" 
                    + (original.ServRef5.ToString()) 
                    + "`" 
                    + (modified.ServRef5.ToString()).Replace("`", "-"));
    if (modified.EarlyBuffer != original.EarlyBuffer)
        changes.Add("EarlyBuffer`" 
                    + (original.EarlyBuffer.ToString()) 
                    + "`" 
                    + (modified.EarlyBuffer.ToString()).Replace("`", "-"));
    if (modified.LateBuffer != original.LateBuffer)
        changes.Add("LateBuffer`" 
                    + (original.LateBuffer.ToString()) 
                    + "`" 
                    + (modified.LateBuffer.ToString()).Replace("`", "-"));
    if (modified.DemandUnitPriceDiff != original.DemandUnitPriceDiff)
        changes.Add("DemandUnitPriceDiff`" 
                    + (original.DemandUnitPriceDiff.ToString()) 
                    + "`" 
                    + (modified.DemandUnitPriceDiff.ToString()).Replace("`", "-"));
    if (modified.DemandUnitPriceDiffAction != original.DemandUnitPriceDiffAction)
        changes.Add("DemandUnitPriceDiffAction`" 
                    + (original.DemandUnitPriceDiffAction.ToString()) 
                    + "`" 
                    + (modified.DemandUnitPriceDiffAction.ToString()).Replace("`", "-"));
    if (modified.ExcFromVal != original.ExcFromVal)
        changes.Add("ExcFromVal`" 
                    + (original.ExcFromVal.ToString()) 
                    + "`" 
                    + (modified.ExcFromVal.ToString()).Replace("`", "-"));
    if (modified.AddressVal != original.AddressVal)
        changes.Add("AddressVal`" 
                    + (original.AddressVal.ToString()) 
                    + "`" 
                    + (modified.AddressVal.ToString()).Replace("`", "-"));
    if (modified.RebateVendorNum != original.RebateVendorNum)
        changes.Add("RebateVendorNum`" 
                    + (original.RebateVendorNum.ToString()) 
                    + "`" 
                    + (modified.RebateVendorNum.ToString()).Replace("`", "-"));
    if (modified.RebateForm != original.RebateForm)
        changes.Add("RebateForm`" 
                    + (original.RebateForm.ToString()) 
                    + "`" 
                    + (modified.RebateForm.ToString()).Replace("`", "-"));
    if (modified.CreditCardOrder != original.CreditCardOrder)
        changes.Add("CreditCardOrder`" 
                    + (original.CreditCardOrder.ToString()) 
                    + "`" 
                    + (modified.CreditCardOrder.ToString()).Replace("`", "-"));
    if (modified.DemandCheckForPart != original.DemandCheckForPart)
        changes.Add("DemandCheckForPart`" 
                    + (original.DemandCheckForPart.ToString()) 
                    + "`" 
                    + (modified.DemandCheckForPart.ToString()).Replace("`", "-"));
    if (modified.DemandCheckForPartAction != original.DemandCheckForPartAction)
        changes.Add("DemandCheckForPartAction`" 
                    + (original.DemandCheckForPartAction.ToString()) 
                    + "`" 
                    + (modified.DemandCheckForPartAction.ToString()).Replace("`", "-"));
    if (modified.ChangedBy != original.ChangedBy)
        changes.Add("ChangedBy`" 
                    + (original.ChangedBy.ToString()) 
                    + "`" 
                    + (modified.ChangedBy.ToString()).Replace("`", "-"));
    if (modified.ChangeDate != original.ChangeDate)
        changes.Add("ChangeDate`" 
                    + (original.ChangeDate.ToString()) 
                    + "`" 
                    + (modified.ChangeDate.ToString()).Replace("`", "-"));
    if (modified.ChangeTime != original.ChangeTime)
        changes.Add("ChangeTime`" 
                    + (original.ChangeTime.ToString()) 
                    + "`" 
                    + (modified.ChangeTime.ToString()).Replace("`", "-"));
    if (modified.ChargeCode != original.ChargeCode)
        changes.Add("ChargeCode`" 
                    + (original.ChargeCode.ToString()) 
                    + "`" 
                    + (modified.ChargeCode.ToString()).Replace("`", "-"));
    if (modified.IndividualPackIDs != original.IndividualPackIDs)
        changes.Add("IndividualPackIDs`" 
                    + (original.IndividualPackIDs.ToString()) 
                    + "`" 
                    + (modified.IndividualPackIDs.ToString()).Replace("`", "-"));
    if (modified.IntrntlShip != original.IntrntlShip)
        changes.Add("IntrntlShip`" 
                    + (original.IntrntlShip.ToString()) 
                    + "`" 
                    + (modified.IntrntlShip.ToString()).Replace("`", "-"));
    if (modified.CertOfOrigin != original.CertOfOrigin)
        changes.Add("CertOfOrigin`" 
                    + (original.CertOfOrigin.ToString()) 
                    + "`" 
                    + (modified.CertOfOrigin.ToString()).Replace("`", "-"));
    if (modified.CommercialInvoice != original.CommercialInvoice)
        changes.Add("CommercialInvoice`" 
                    + (original.CommercialInvoice.ToString()) 
                    + "`" 
                    + (modified.CommercialInvoice.ToString()).Replace("`", "-"));
    if (modified.ShipExprtDeclartn != original.ShipExprtDeclartn)
        changes.Add("ShipExprtDeclartn`" 
                    + (original.ShipExprtDeclartn.ToString()) 
                    + "`" 
                    + (modified.ShipExprtDeclartn.ToString()).Replace("`", "-"));
    if (modified.LetterOfInstr != original.LetterOfInstr)
        changes.Add("LetterOfInstr`" 
                    + (original.LetterOfInstr.ToString()) 
                    + "`" 
                    + (modified.LetterOfInstr.ToString()).Replace("`", "-"));
    if (modified.FFID != original.FFID)
        changes.Add("FFID`" 
                    + (original.FFID.ToString()) 
                    + "`" 
                    + (modified.FFID.ToString()).Replace("`", "-"));
    if (modified.FFCompName != original.FFCompName)
        changes.Add("FFCompName`" 
                    + (original.FFCompName.ToString()) 
                    + "`" 
                    + (modified.FFCompName.ToString()).Replace("`", "-"));
    if (modified.FFAddress1 != original.FFAddress1)
        changes.Add("FFAddress1`" 
                    + (original.FFAddress1.ToString()) 
                    + "`" 
                    + (modified.FFAddress1.ToString()).Replace("`", "-"));
    if (modified.FFAddress2 != original.FFAddress2)
        changes.Add("FFAddress2`" 
                    + (original.FFAddress2.ToString()) 
                    + "`" 
                    + (modified.FFAddress2.ToString()).Replace("`", "-"));
    if (modified.FFAddress3 != original.FFAddress3)
        changes.Add("FFAddress3`" 
                    + (original.FFAddress3.ToString()) 
                    + "`" 
                    + (modified.FFAddress3.ToString()).Replace("`", "-"));
    if (modified.FFCity != original.FFCity)
        changes.Add("FFCity`" 
                    + (original.FFCity.ToString()) 
                    + "`" 
                    + (modified.FFCity.ToString()).Replace("`", "-"));
    if (modified.FFState != original.FFState)
        changes.Add("FFState`" 
                    + (original.FFState.ToString()) 
                    + "`" 
                    + (modified.FFState.ToString()).Replace("`", "-"));
    if (modified.FFZip != original.FFZip)
        changes.Add("FFZip`" 
                    + (original.FFZip.ToString()) 
                    + "`" 
                    + (modified.FFZip.ToString()).Replace("`", "-"));
    if (modified.FFCountry != original.FFCountry)
        changes.Add("FFCountry`" 
                    + (original.FFCountry.ToString()) 
                    + "`" 
                    + (modified.FFCountry.ToString()).Replace("`", "-"));
    if (modified.FFCountryNum != original.FFCountryNum)
        changes.Add("FFCountryNum`" 
                    + (original.FFCountryNum.ToString()) 
                    + "`" 
                    + (modified.FFCountryNum.ToString()).Replace("`", "-"));
    if (modified.FFPhoneNum != original.FFPhoneNum)
        changes.Add("FFPhoneNum`" 
                    + (original.FFPhoneNum.ToString()) 
                    + "`" 
                    + (modified.FFPhoneNum.ToString()).Replace("`", "-"));
    if (modified.NonStdPkg != original.NonStdPkg)
        changes.Add("NonStdPkg`" 
                    + (original.NonStdPkg.ToString()) 
                    + "`" 
                    + (modified.NonStdPkg.ToString()).Replace("`", "-"));
    if (modified.DeliveryConf != original.DeliveryConf)
        changes.Add("DeliveryConf`" 
                    + (original.DeliveryConf.ToString()) 
                    + "`" 
                    + (modified.DeliveryConf.ToString()).Replace("`", "-"));
    if (modified.AddlHdlgFlag != original.AddlHdlgFlag)
        changes.Add("AddlHdlgFlag`" 
                    + (original.AddlHdlgFlag.ToString()) 
                    + "`" 
                    + (modified.AddlHdlgFlag.ToString()).Replace("`", "-"));
    if (modified.UPSQuantumView != original.UPSQuantumView)
        changes.Add("UPSQuantumView`" 
                    + (original.UPSQuantumView.ToString()) 
                    + "`" 
                    + (modified.UPSQuantumView.ToString()).Replace("`", "-"));
    if (modified.UPSQVShipFromName != original.UPSQVShipFromName)
        changes.Add("UPSQVShipFromName`" 
                    + (original.UPSQVShipFromName.ToString()) 
                    + "`" 
                    + (modified.UPSQVShipFromName.ToString()).Replace("`", "-"));
    if (modified.UPSQVMemo != original.UPSQVMemo)
        changes.Add("UPSQVMemo`" 
                    + (original.UPSQVMemo.ToString()) 
                    + "`" 
                    + (modified.UPSQVMemo.ToString()).Replace("`", "-"));
    if (modified.UPSQVEmailType != original.UPSQVEmailType)
        changes.Add("UPSQVEmailType`" 
                    + (original.UPSQVEmailType.ToString()) 
                    + "`" 
                    + (modified.UPSQVEmailType.ToString()).Replace("`", "-"));
    if (modified.FFContact != original.FFContact)
        changes.Add("FFContact`" 
                    + (original.FFContact.ToString()) 
                    + "`" 
                    + (modified.FFContact.ToString()).Replace("`", "-"));
    if (modified.ETCAddrChg != original.ETCAddrChg)
        changes.Add("ETCAddrChg`" 
                    + (original.ETCAddrChg.ToString()) 
                    + "`" 
                    + (modified.ETCAddrChg.ToString()).Replace("`", "-"));
    if (modified.TaxRoundRule != original.TaxRoundRule)
        changes.Add("TaxRoundRule`" 
                    + (original.TaxRoundRule.ToString()) 
                    + "`" 
                    + (modified.TaxRoundRule.ToString()).Replace("`", "-"));
    if (modified.TaxMethod != original.TaxMethod)
        changes.Add("TaxMethod`" 
                    + (original.TaxMethod.ToString()) 
                    + "`" 
                    + (modified.TaxMethod.ToString()).Replace("`", "-"));
    if (modified.ValidPayer != original.ValidPayer)
        changes.Add("ValidPayer`" 
                    + (original.ValidPayer.ToString()) 
                    + "`" 
                    + (modified.ValidPayer.ToString()).Replace("`", "-"));
    if (modified.ValidSoldTo != original.ValidSoldTo)
        changes.Add("ValidSoldTo`" 
                    + (original.ValidSoldTo.ToString()) 
                    + "`" 
                    + (modified.ValidSoldTo.ToString()).Replace("`", "-"));
    if (modified.ValidShipTo != original.ValidShipTo)
        changes.Add("ValidShipTo`" 
                    + (original.ValidShipTo.ToString()) 
                    + "`" 
                    + (modified.ValidShipTo.ToString()).Replace("`", "-"));
    if (modified.OverrideRlsClass != original.OverrideRlsClass)
        changes.Add("OverrideRlsClass`" 
                    + (original.OverrideRlsClass.ToString()) 
                    + "`" 
                    + (modified.OverrideRlsClass.ToString()).Replace("`", "-"));
    if (modified.AcrossNatAcc != original.AcrossNatAcc)
        changes.Add("AcrossNatAcc`" 
                    + (original.AcrossNatAcc.ToString()) 
                    + "`" 
                    + (modified.AcrossNatAcc.ToString()).Replace("`", "-"));
    if (modified.AllowOTS != original.AllowOTS)
        changes.Add("AllowOTS`" 
                    + (original.AllowOTS.ToString()) 
                    + "`" 
                    + (modified.AllowOTS.ToString()).Replace("`", "-"));
    if (modified.ThirdPLCust != original.ThirdPLCust)
        changes.Add("ThirdPLCust`" 
                    + (original.ThirdPLCust.ToString()) 
                    + "`" 
                    + (modified.ThirdPLCust.ToString()).Replace("`", "-"));
    if (modified.ManagedVendID != original.ManagedVendID)
        changes.Add("ManagedVendID`" 
                    + (original.ManagedVendID.ToString()) 
                    + "`" 
                    + (modified.ManagedVendID.ToString()).Replace("`", "-"));
    if (modified.ManagedVendNum != original.ManagedVendNum)
        changes.Add("ManagedVendNum`" 
                    + (original.ManagedVendNum.ToString()) 
                    + "`" 
                    + (modified.ManagedVendNum.ToString()).Replace("`", "-"));
    if (modified.NARlsClassCode != original.NARlsClassCode)
        changes.Add("NARlsClassCode`" 
                    + (original.NARlsClassCode.ToString()) 
                    + "`" 
                    + (modified.NARlsClassCode.ToString()).Replace("`", "-"));
    if (modified.DirectDebiting != original.DirectDebiting)
        changes.Add("DirectDebiting`" 
                    + (original.DirectDebiting.ToString()) 
                    + "`" 
                    + (modified.DirectDebiting.ToString()).Replace("`", "-"));
    if (modified.ReminderCode != original.ReminderCode)
        changes.Add("ReminderCode`" 
                    + (original.ReminderCode.ToString()) 
                    + "`" 
                    + (modified.ReminderCode.ToString()).Replace("`", "-"));
    if (modified.AllowShipTo3 != original.AllowShipTo3)
        changes.Add("AllowShipTo3`" 
                    + (original.AllowShipTo3.ToString()) 
                    + "`" 
                    + (modified.AllowShipTo3.ToString()).Replace("`", "-"));
    if (modified.OTSSaveAs != original.OTSSaveAs)
        changes.Add("OTSSaveAs`" 
                    + (original.OTSSaveAs.ToString()) 
                    + "`" 
                    + (modified.OTSSaveAs.ToString()).Replace("`", "-"));
    if (modified.CustPartOpts != original.CustPartOpts)
        changes.Add("CustPartOpts`" 
                    + (original.CustPartOpts.ToString()) 
                    + "`" 
                    + (modified.CustPartOpts.ToString()).Replace("`", "-"));
    if (modified.HasBank != original.HasBank)
        changes.Add("HasBank`" 
                    + (original.HasBank.ToString()) 
                    + "`" 
                    + (modified.HasBank.ToString()).Replace("`", "-"));
    if (modified.PMUID != original.PMUID)
        changes.Add("PMUID`" 
                    + (original.PMUID.ToString()) 
                    + "`" 
                    + (modified.PMUID.ToString()).Replace("`", "-"));
    if (modified.DemandCheckForRev != original.DemandCheckForRev)
        changes.Add("DemandCheckForRev`" 
                    + (original.DemandCheckForRev.ToString()) 
                    + "`" 
                    + (modified.DemandCheckForRev.ToString()).Replace("`", "-"));
    if (modified.OrderHoldForReview != original.OrderHoldForReview)
        changes.Add("OrderHoldForReview`" 
                    + (original.OrderHoldForReview.ToString()) 
                    + "`" 
                    + (modified.OrderHoldForReview.ToString()).Replace("`", "-"));
    if (modified.DemandCheckForRevAction != original.DemandCheckForRevAction)
        changes.Add("DemandCheckForRevAction`" 
                    + (original.DemandCheckForRevAction.ToString()) 
                    + "`" 
                    + (modified.DemandCheckForRevAction.ToString()).Replace("`", "-"));
    if (modified.ShipToTerrList != original.ShipToTerrList)
        changes.Add("ShipToTerrList`" 
                    + (original.ShipToTerrList.ToString()) 
                    + "`" 
                    + (modified.ShipToTerrList.ToString()).Replace("`", "-"));
    if (modified.AcctRefNumber != original.AcctRefNumber)
        changes.Add("AcctRefNumber`" 
                    + (original.AcctRefNumber.ToString()) 
                    + "`" 
                    + (modified.AcctRefNumber.ToString()).Replace("`", "-"));
    if (modified.LegalName != original.LegalName)
        changes.Add("LegalName`" 
                    + (original.LegalName.ToString()) 
                    + "`" 
                    + (modified.LegalName.ToString()).Replace("`", "-"));
    if (modified.TaxRegReason != original.TaxRegReason)
        changes.Add("TaxRegReason`" 
                    + (original.TaxRegReason.ToString()) 
                    + "`" 
                    + (modified.TaxRegReason.ToString()).Replace("`", "-"));
    if (modified.InvPerPackLine != original.InvPerPackLine)
        changes.Add("InvPerPackLine`" 
                    + (original.InvPerPackLine.ToString()) 
                    + "`" 
                    + (modified.InvPerPackLine.ToString()).Replace("`", "-"));
    if (modified.OrgRegCode != original.OrgRegCode)
        changes.Add("OrgRegCode`" 
                    + (original.OrgRegCode.ToString()) 
                    + "`" 
                    + (modified.OrgRegCode.ToString()).Replace("`", "-"));
    if (modified.DemandCloseRejSkd != original.DemandCloseRejSkd)
        changes.Add("DemandCloseRejSkd`" 
                    + (original.DemandCloseRejSkd.ToString()) 
                    + "`" 
                    + (modified.DemandCloseRejSkd.ToString()).Replace("`", "-"));
    if (modified.OurBankCode != original.OurBankCode)
        changes.Add("OurBankCode`" 
                    + (original.OurBankCode.ToString()) 
                    + "`" 
                    + (modified.OurBankCode.ToString()).Replace("`", "-"));
    if (modified.DemandCloseNoMatch != original.DemandCloseNoMatch)
        changes.Add("DemandCloseNoMatch`" 
                    + (original.DemandCloseNoMatch.ToString()) 
                    + "`" 
                    + (modified.DemandCloseNoMatch.ToString()).Replace("`", "-"));
    if (modified.DmdCheckPartialShip != original.DmdCheckPartialShip)
        changes.Add("DmdCheckPartialShip`" 
                    + (original.DmdCheckPartialShip.ToString()) 
                    + "`" 
                    + (modified.DmdCheckPartialShip.ToString()).Replace("`", "-"));
    if (modified.DmdCheckShipAction != original.DmdCheckShipAction)
        changes.Add("DmdCheckShipAction`" 
                    + (original.DmdCheckShipAction.ToString()) 
                    + "`" 
                    + (modified.DmdCheckShipAction.ToString()).Replace("`", "-"));
    if (modified.DemandCheckCUMM != original.DemandCheckCUMM)
        changes.Add("DemandCheckCUMM`" 
                    + (original.DemandCheckCUMM.ToString()) 
                    + "`" 
                    + (modified.DemandCheckCUMM.ToString()).Replace("`", "-"));
    if (modified.DemandCheckCUMMAction != original.DemandCheckCUMMAction)
        changes.Add("DemandCheckCUMMAction`" 
                    + (original.DemandCheckCUMMAction.ToString()) 
                    + "`" 
                    + (modified.DemandCheckCUMMAction.ToString()).Replace("`", "-"));
    if (modified.DemandPricing != original.DemandPricing)
        changes.Add("DemandPricing`" 
                    + (original.DemandPricing.ToString()) 
                    + "`" 
                    + (modified.DemandPricing.ToString()).Replace("`", "-"));
    if (modified.PriceTolerance != original.PriceTolerance)
        changes.Add("PriceTolerance`" 
                    + (original.PriceTolerance.ToString()) 
                    + "`" 
                    + (modified.PriceTolerance.ToString()).Replace("`", "-"));
    if (modified.PreferredBank != original.PreferredBank)
        changes.Add("PreferredBank`" 
                    + (original.PreferredBank.ToString()) 
                    + "`" 
                    + (modified.PreferredBank.ToString()).Replace("`", "-"));
    if (modified.CheckDateCapPromise != original.CheckDateCapPromise)
        changes.Add("CheckDateCapPromise`" 
                    + (original.CheckDateCapPromise.ToString()) 
                    + "`" 
                    + (modified.CheckDateCapPromise.ToString()).Replace("`", "-"));
    if (modified.CheckConfirmCapPromise != original.CheckConfirmCapPromise)
        changes.Add("CheckConfirmCapPromise`" 
                    + (original.CheckConfirmCapPromise.ToString()) 
                    + "`" 
                    + (modified.CheckConfirmCapPromise.ToString()).Replace("`", "-"));
    if (modified.CheckUpdateCapPromise != original.CheckUpdateCapPromise)
        changes.Add("CheckUpdateCapPromise`" 
                    + (original.CheckUpdateCapPromise.ToString()) 
                    + "`" 
                    + (modified.CheckUpdateCapPromise.ToString()).Replace("`", "-"));
    if (modified.DemandCapPromiseDate != original.DemandCapPromiseDate)
        changes.Add("DemandCapPromiseDate`" 
                    + (original.DemandCapPromiseDate.ToString()) 
                    + "`" 
                    + (modified.DemandCapPromiseDate.ToString()).Replace("`", "-"));
    if (modified.DemandCapPromiseAction != original.DemandCapPromiseAction)
        changes.Add("DemandCapPromiseAction`" 
                    + (original.DemandCapPromiseAction.ToString()) 
                    + "`" 
                    + (modified.DemandCapPromiseAction.ToString()).Replace("`", "-"));
    if (modified.DemandCapPromiseUpdate != original.DemandCapPromiseUpdate)
        changes.Add("DemandCapPromiseUpdate`" 
                    + (original.DemandCapPromiseUpdate.ToString()) 
                    + "`" 
                    + (modified.DemandCapPromiseUpdate.ToString()).Replace("`", "-"));
    if (modified.PeriodicBilling != original.PeriodicBilling)
        changes.Add("PeriodicBilling`" 
                    + (original.PeriodicBilling.ToString()) 
                    + "`" 
                    + (modified.PeriodicBilling.ToString()).Replace("`", "-"));
    if (modified.DueDateCriteria != original.DueDateCriteria)
        changes.Add("DueDateCriteria`" 
                    + (original.DueDateCriteria.ToString()) 
                    + "`" 
                    + (modified.DueDateCriteria.ToString()).Replace("`", "-"));
    if (modified.PBTerms != original.PBTerms)
        changes.Add("PBTerms`" 
                    + (original.PBTerms.ToString()) 
                    + "`" 
                    + (modified.PBTerms.ToString()).Replace("`", "-"));
    if (modified.ERSOrder != original.ERSOrder)
        changes.Add("ERSOrder`" 
                    + (original.ERSOrder.ToString()) 
                    + "`" 
                    + (modified.ERSOrder.ToString()).Replace("`", "-"));
    if (modified.DemandSplitSched != original.DemandSplitSched)
        changes.Add("DemandSplitSched`" 
                    + (original.DemandSplitSched.ToString()) 
                    + "`" 
                    + (modified.DemandSplitSched.ToString()).Replace("`", "-"));
    if (modified.OTSmartString != original.OTSmartString)
        changes.Add("OTSmartString`" 
                    + (original.OTSmartString.ToString()) 
                    + "`" 
                    + (modified.OTSmartString.ToString()).Replace("`", "-"));
    if (modified.DeferredRev != original.DeferredRev)
        changes.Add("DeferredRev`" 
                    + (original.DeferredRev.ToString()) 
                    + "`" 
                    + (modified.DeferredRev.ToString()).Replace("`", "-"));
    if (modified.RACode != original.RACode)
        changes.Add("RACode`" 
                    + (original.RACode.ToString()) 
                    + "`" 
                    + (modified.RACode.ToString()).Replace("`", "-"));
    if (modified.DemandCheckConfig != original.DemandCheckConfig)
        changes.Add("DemandCheckConfig`" 
                    + (original.DemandCheckConfig.ToString()) 
                    + "`" 
                    + (modified.DemandCheckConfig.ToString()).Replace("`", "-"));
    if (modified.DemandCheckCfgAction != original.DemandCheckCfgAction)
        changes.Add("DemandCheckCfgAction`" 
                    + (original.DemandCheckCfgAction.ToString()) 
                    + "`" 
                    + (modified.DemandCheckCfgAction.ToString()).Replace("`", "-"));
    if (modified.AllowAsAltRemitTo != original.AllowAsAltRemitTo)
        changes.Add("AllowAsAltRemitTo`" 
                    + (original.AllowAsAltRemitTo.ToString()) 
                    + "`" 
                    + (modified.AllowAsAltRemitTo.ToString()).Replace("`", "-"));
    if (modified.FederalID != original.FederalID)
        changes.Add("FederalID`" 
                    + (original.FederalID.ToString()) 
                    + "`" 
                    + (modified.FederalID.ToString()).Replace("`", "-"));
    if (modified.WIApplication != original.WIApplication)
        changes.Add("WIApplication`" 
                    + (original.WIApplication.ToString()) 
                    + "`" 
                    + (modified.WIApplication.ToString()).Replace("`", "-"));
    if (modified.WICustomer != original.WICustomer)
        changes.Add("WICustomer`" 
                    + (original.WICustomer.ToString()) 
                    + "`" 
                    + (modified.WICustomer.ToString()).Replace("`", "-"));
    if (modified.WIShippingCosts != original.WIShippingCosts)
        changes.Add("WIShippingCosts`" 
                    + (original.WIShippingCosts.ToString()) 
                    + "`" 
                    + (modified.WIShippingCosts.ToString()).Replace("`", "-"));
    if (modified.LOQBookPCFinishing != original.LOQBookPCFinishing)
        changes.Add("LOQBookPCFinishing`" 
                    + (original.LOQBookPCFinishing.ToString()) 
                    + "`" 
                    + (modified.LOQBookPCFinishing.ToString()).Replace("`", "-"));
    if (modified.LOQBookPCPaper != original.LOQBookPCPaper)
        changes.Add("LOQBookPCPaper`" 
                    + (original.LOQBookPCPaper.ToString()) 
                    + "`" 
                    + (modified.LOQBookPCPaper.ToString()).Replace("`", "-"));
    if (modified.LOQBookPCPress != original.LOQBookPCPress)
        changes.Add("LOQBookPCPress`" 
                    + (original.LOQBookPCPress.ToString()) 
                    + "`" 
                    + (modified.LOQBookPCPress.ToString()).Replace("`", "-"));
    if (modified.LOQBookPCPlates != original.LOQBookPCPlates)
        changes.Add("LOQBookPCPlates`" 
                    + (original.LOQBookPCPlates.ToString()) 
                    + "`" 
                    + (modified.LOQBookPCPlates.ToString()).Replace("`", "-"));
    if (modified.Variations != original.Variations)
        changes.Add("Variations`" 
                    + (original.Variations.ToString()) 
                    + "`" 
                    + (modified.Variations.ToString()).Replace("`", "-"));
    if (modified.OversPct != original.OversPct)
        changes.Add("OversPct`" 
                    + (original.OversPct.ToString()) 
                    + "`" 
                    + (modified.OversPct.ToString()).Replace("`", "-"));
    if (modified.UndersPct != original.UndersPct)
        changes.Add("UndersPct`" 
                    + (original.UndersPct.ToString()) 
                    + "`" 
                    + (modified.UndersPct.ToString()).Replace("`", "-"));
    if (modified.DefaultLOQstyle != original.DefaultLOQstyle)
        changes.Add("DefaultLOQstyle`" 
                    + (original.DefaultLOQstyle.ToString()) 
                    + "`" 
                    + (modified.DefaultLOQstyle.ToString()).Replace("`", "-"));
    if (modified.DefaultOrderAcknowledgement != original.DefaultOrderAcknowledgement)
        changes.Add("DefaultOrderAcknowledgement`" 
                    + (original.DefaultOrderAcknowledgement.ToString()) 
                    + "`" 
                    + (modified.DefaultOrderAcknowledgement.ToString()).Replace("`", "-"));
    if (modified.DefaultPackSlip != original.DefaultPackSlip)
        changes.Add("DefaultPackSlip`" 
                    + (original.DefaultPackSlip.ToString()) 
                    + "`" 
                    + (modified.DefaultPackSlip.ToString()).Replace("`", "-"));
    if (modified.DefaultOversPricing != original.DefaultOversPricing)
        changes.Add("DefaultOversPricing`" 
                    + (original.DefaultOversPricing.ToString()) 
                    + "`" 
                    + (modified.DefaultOversPricing.ToString()).Replace("`", "-"));
    if (modified.LOQPrepressStyle != original.LOQPrepressStyle)
        changes.Add("LOQPrepressStyle`" 
                    + (original.LOQPrepressStyle.ToString()) 
                    + "`" 
                    + (modified.LOQPrepressStyle.ToString()).Replace("`", "-"));
    if (modified.CSR != original.CSR)
        changes.Add("CSR`" 
                    + (original.CSR.ToString()) 
                    + "`" 
                    + (modified.CSR.ToString()).Replace("`", "-"));
    if (modified.LOQBookCaFinishing != original.LOQBookCaFinishing)
        changes.Add("LOQBookCaFinishing`" 
                    + (original.LOQBookCaFinishing.ToString()) 
                    + "`" 
                    + (modified.LOQBookCaFinishing.ToString()).Replace("`", "-"));
    if (modified.LOQBookCapaper != original.LOQBookCapaper)
        changes.Add("LOQBookCapaper`" 
                    + (original.LOQBookCapaper.ToString()) 
                    + "`" 
                    + (modified.LOQBookCapaper.ToString()).Replace("`", "-"));
    if (modified.LOQBookCaPress != original.LOQBookCaPress)
        changes.Add("LOQBookCaPress`" 
                    + (original.LOQBookCaPress.ToString()) 
                    + "`" 
                    + (modified.LOQBookCaPress.ToString()).Replace("`", "-"));
    if (modified.LOQBookCatPlates != original.LOQBookCatPlates)
        changes.Add("LOQBookCatPlates`" 
                    + (original.LOQBookCatPlates.ToString()) 
                    + "`" 
                    + (modified.LOQBookCatPlates.ToString()).Replace("`", "-"));
    if (modified.LOQVariations != original.LOQVariations)
        changes.Add("LOQVariations`" 
                    + (original.LOQVariations.ToString()) 
                    + "`" 
                    + (modified.LOQVariations.ToString()).Replace("`", "-"));
    if (modified.DefaultInvoicestyle != original.DefaultInvoicestyle)
        changes.Add("DefaultInvoicestyle`" 
                    + (original.DefaultInvoicestyle.ToString()) 
                    + "`" 
                    + (modified.DefaultInvoicestyle.ToString()).Replace("`", "-"));
    if (modified.AEPLOQType != original.AEPLOQType)
        changes.Add("AEPLOQType`" 
                    + (original.AEPLOQType.ToString()) 
                    + "`" 
                    + (modified.AEPLOQType.ToString()).Replace("`", "-"));
    if (modified.BusinessCust != original.BusinessCust)
        changes.Add("BusinessCust`" 
                    + (original.BusinessCust.ToString()) 
                    + "`" 
                    + (modified.BusinessCust.ToString()).Replace("`", "-"));
    if (modified.THBranchID != original.THBranchID)
        changes.Add("THBranchID`" 
                    + (original.THBranchID.ToString()) 
                    + "`" 
                    + (modified.THBranchID.ToString()).Replace("`", "-"));
    if (modified.CustPricingSchema != original.CustPricingSchema)
        changes.Add("CustPricingSchema`" 
                    + (original.CustPricingSchema.ToString()) 
                    + "`" 
                    + (modified.CustPricingSchema.ToString()).Replace("`", "-"));
    if (modified.ParamCode != original.ParamCode)
        changes.Add("ParamCode`" 
                    + (original.ParamCode.ToString()) 
                    + "`" 
                    + (modified.ParamCode.ToString()).Replace("`", "-"));
    if (modified.AGAFIPResponsibilityCode != original.AGAFIPResponsibilityCode)
        changes.Add("AGAFIPResponsibilityCode`" 
                    + (original.AGAFIPResponsibilityCode.ToString()) 
                    + "`" 
                    + (modified.AGAFIPResponsibilityCode.ToString()).Replace("`", "-"));
    if (modified.AGBillToProvinceCode != original.AGBillToProvinceCode)
        changes.Add("AGBillToProvinceCode`" 
                    + (original.AGBillToProvinceCode.ToString()) 
                    + "`" 
                    + (modified.AGBillToProvinceCode.ToString()).Replace("`", "-"));
    if (modified.AGGrossIncomeTaxID != original.AGGrossIncomeTaxID)
        changes.Add("AGGrossIncomeTaxID`" 
                    + (original.AGGrossIncomeTaxID.ToString()) 
                    + "`" 
                    + (modified.AGGrossIncomeTaxID.ToString()).Replace("`", "-"));
    if (modified.AGIDDocTypeCode != original.AGIDDocTypeCode)
        changes.Add("AGIDDocTypeCode`" 
                    + (original.AGIDDocTypeCode.ToString()) 
                    + "`" 
                    + (modified.AGIDDocTypeCode.ToString()).Replace("`", "-"));
    if (modified.AGIDDocumentNumber != original.AGIDDocumentNumber)
        changes.Add("AGIDDocumentNumber`" 
                    + (original.AGIDDocumentNumber.ToString()) 
                    + "`" 
                    + (modified.AGIDDocumentNumber.ToString()).Replace("`", "-"));
    if (modified.AGProvinceCode != original.AGProvinceCode)
        changes.Add("AGProvinceCode`" 
                    + (original.AGProvinceCode.ToString()) 
                    + "`" 
                    + (modified.AGProvinceCode.ToString()).Replace("`", "-"));
    if (modified.AGUseGoodDefaultMark != original.AGUseGoodDefaultMark)
        changes.Add("AGUseGoodDefaultMark`" 
                    + (original.AGUseGoodDefaultMark.ToString()) 
                    + "`" 
                    + (modified.AGUseGoodDefaultMark.ToString()).Replace("`", "-"));
    if (modified.Collections != original.Collections)
        changes.Add("Collections`" 
                    + (original.Collections.ToString()) 
                    + "`" 
                    + (modified.Collections.ToString()).Replace("`", "-"));
    if (modified.CollectionsDate != original.CollectionsDate)
        changes.Add("CollectionsDate`" 
                    + (original.CollectionsDate.ToString()) 
                    + "`" 
                    + (modified.CollectionsDate.ToString()).Replace("`", "-"));
    if (modified.DateCollectionPosted != original.DateCollectionPosted)
        changes.Add("DateCollectionPosted`" 
                    + (original.DateCollectionPosted.ToString()) 
                    + "`" 
                    + (modified.DateCollectionPosted.ToString()).Replace("`", "-"));
    if (modified.MXMunicipio != original.MXMunicipio)
        changes.Add("MXMunicipio`" 
                    + (original.MXMunicipio.ToString()) 
                    + "`" 
                    + (modified.MXMunicipio.ToString()).Replace("`", "-"));
    if (modified.PEIdentityDocType != original.PEIdentityDocType)
        changes.Add("PEIdentityDocType`" 
                    + (original.PEIdentityDocType.ToString()) 
                    + "`" 
                    + (modified.PEIdentityDocType.ToString()).Replace("`", "-"));
    if (modified.PEDocumentID != original.PEDocumentID)
        changes.Add("PEDocumentID`" 
                    + (original.PEDocumentID.ToString()) 
                    + "`" 
                    + (modified.PEDocumentID.ToString()).Replace("`", "-"));
    if (modified.PEGoodsContributor != original.PEGoodsContributor)
        changes.Add("PEGoodsContributor`" 
                    + (original.PEGoodsContributor.ToString()) 
                    + "`" 
                    + (modified.PEGoodsContributor.ToString()).Replace("`", "-"));
    if (modified.PEWithholdAgent != original.PEWithholdAgent)
        changes.Add("PEWithholdAgent`" 
                    + (original.PEWithholdAgent.ToString()) 
                    + "`" 
                    + (modified.PEWithholdAgent.ToString()).Replace("`", "-"));
    if (modified.PECollectionAgent != original.PECollectionAgent)
        changes.Add("PECollectionAgent`" 
                    + (original.PECollectionAgent.ToString()) 
                    + "`" 
                    + (modified.PECollectionAgent.ToString()).Replace("`", "-"));
    if (modified.PENotFound != original.PENotFound)
        changes.Add("PENotFound`" 
                    + (original.PENotFound.ToString()) 
                    + "`" 
                    + (modified.PENotFound.ToString()).Replace("`", "-"));
    if (modified.PENoAddress != original.PENoAddress)
        changes.Add("PENoAddress`" 
                    + (original.PENoAddress.ToString()) 
                    + "`" 
                    + (modified.PENoAddress.ToString()).Replace("`", "-"));
    if (modified.EntityUseCode != original.EntityUseCode)
        changes.Add("EntityUseCode`" 
                    + (original.EntityUseCode.ToString()) 
                    + "`" 
                    + (modified.EntityUseCode.ToString()).Replace("`", "-"));
    if (modified.InvcOrderCmpDflt != original.InvcOrderCmpDflt)
        changes.Add("InvcOrderCmpDflt`" 
                    + (original.InvcOrderCmpDflt.ToString()) 
                    + "`" 
                    + (modified.InvcOrderCmpDflt.ToString()).Replace("`", "-"));
    if (modified.EInvoice != original.EInvoice)
        changes.Add("EInvoice`" 
                    + (original.EInvoice.ToString()) 
                    + "`" 
                    + (modified.EInvoice.ToString()).Replace("`", "-"));
    if (modified.RegistrationCode != original.RegistrationCode)
        changes.Add("RegistrationCode`" 
                    + (original.RegistrationCode.ToString()) 
                    + "`" 
                    + (modified.RegistrationCode.ToString()).Replace("`", "-"));
    if (modified.EAddress != original.EAddress)
        changes.Add("EAddress`" 
                    + (original.EAddress.ToString()) 
                    + "`" 
                    + (modified.EAddress.ToString()).Replace("`", "-"));
    if (modified.DemandCheckForRunOutPart != original.DemandCheckForRunOutPart)
        changes.Add("DemandCheckForRunOutPart`" 
                    + (original.DemandCheckForRunOutPart.ToString()) 
                    + "`" 
                    + (modified.DemandCheckForRunOutPart.ToString()).Replace("`", "-"));
    if (modified.DemandCheckForRunOutPartAction != original.DemandCheckForRunOutPartAction)
        changes.Add("DemandCheckForRunOutPartAction`" 
                    + (original.DemandCheckForRunOutPartAction.ToString()) 
                    + "`" 
                    + (modified.DemandCheckForRunOutPartAction.ToString()).Replace("`", "-"));
    if (modified.EInvCompanyIDAttr != original.EInvCompanyIDAttr)
        changes.Add("EInvCompanyIDAttr`" 
                    + (original.EInvCompanyIDAttr.ToString()) 
                    + "`" 
                    + (modified.EInvCompanyIDAttr.ToString()).Replace("`", "-"));
    if (modified.INCSTNumber != original.INCSTNumber)
        changes.Add("INCSTNumber`" 
                    + (original.INCSTNumber.ToString()) 
                    + "`" 
                    + (modified.INCSTNumber.ToString()).Replace("`", "-"));
    if (modified.INPANNumber != original.INPANNumber)
        changes.Add("INPANNumber`" 
                    + (original.INPANNumber.ToString()) 
                    + "`" 
                    + (modified.INPANNumber.ToString()).Replace("`", "-"));
    if (modified.COOneTimeID != original.COOneTimeID)
        changes.Add("COOneTimeID`" 
                    + (original.COOneTimeID.ToString()) 
                    + "`" 
                    + (modified.COOneTimeID.ToString()).Replace("`", "-"));
    if (modified.COIsOneTimeCust != original.COIsOneTimeCust)
        changes.Add("COIsOneTimeCust`" 
                    + (original.COIsOneTimeCust.ToString()) 
                    + "`" 
                    + (modified.COIsOneTimeCust.ToString()).Replace("`", "-"));
    if (modified.DEOrgType != original.DEOrgType)
        changes.Add("DEOrgType`" 
                    + (original.DEOrgType.ToString()) 
                    + "`" 
                    + (modified.DEOrgType.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeName != original.PEGuaranteeName)
        changes.Add("PEGuaranteeName`" 
                    + (original.PEGuaranteeName.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeName.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeAddress1 != original.PEGuaranteeAddress1)
        changes.Add("PEGuaranteeAddress1`" 
                    + (original.PEGuaranteeAddress1.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeAddress1.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeAddress2 != original.PEGuaranteeAddress2)
        changes.Add("PEGuaranteeAddress2`" 
                    + (original.PEGuaranteeAddress2.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeAddress2.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeAddress3 != original.PEGuaranteeAddress3)
        changes.Add("PEGuaranteeAddress3`" 
                    + (original.PEGuaranteeAddress3.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeAddress3.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeCity != original.PEGuaranteeCity)
        changes.Add("PEGuaranteeCity`" 
                    + (original.PEGuaranteeCity.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeCity.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeState != original.PEGuaranteeState)
        changes.Add("PEGuaranteeState`" 
                    + (original.PEGuaranteeState.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeState.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeZip != original.PEGuaranteeZip)
        changes.Add("PEGuaranteeZip`" 
                    + (original.PEGuaranteeZip.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeZip.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeCountry != original.PEGuaranteeCountry)
        changes.Add("PEGuaranteeCountry`" 
                    + (original.PEGuaranteeCountry.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeCountry.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteePhoneNum != original.PEGuaranteePhoneNum)
        changes.Add("PEGuaranteePhoneNum`" 
                    + (original.PEGuaranteePhoneNum.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteePhoneNum.ToString()).Replace("`", "-"));
    if (modified.PEGuaranteeTaxID != original.PEGuaranteeTaxID)
        changes.Add("PEGuaranteeTaxID`" 
                    + (original.PEGuaranteeTaxID.ToString()) 
                    + "`" 
                    + (modified.PEGuaranteeTaxID.ToString()).Replace("`", "-"));
    if (modified.OurSupplierCode != original.OurSupplierCode)
        changes.Add("OurSupplierCode`" 
                    + (original.OurSupplierCode.ToString()) 
                    + "`" 
                    + (modified.OurSupplierCode.ToString()).Replace("`", "-"));
    if (modified.ECCType != original.ECCType)
        changes.Add("ECCType`" 
                    + (original.ECCType.ToString()) 
                    + "`" 
                    + (modified.ECCType.ToString()).Replace("`", "-"));
    if (modified.MYIndustryCode != original.MYIndustryCode)
        changes.Add("MYIndustryCode`" 
                    + (original.MYIndustryCode.ToString()) 
                    + "`" 
                    + (modified.MYIndustryCode.ToString()).Replace("`", "-"));
    if (modified.SyncToExternalCRM != original.SyncToExternalCRM)
        changes.Add("SyncToExternalCRM`" 
                    + (original.SyncToExternalCRM.ToString()) 
                    + "`" 
                    + (modified.SyncToExternalCRM.ToString()).Replace("`", "-"));
    if (modified.ExternalCRMCustomerID != original.ExternalCRMCustomerID)
        changes.Add("ExternalCRMCustomerID`" 
                    + (original.ExternalCRMCustomerID.ToString()) 
                    + "`" 
                    + (modified.ExternalCRMCustomerID.ToString()).Replace("`", "-"));
    if (modified.ExternalCRMCustomerType != original.ExternalCRMCustomerType)
        changes.Add("ExternalCRMCustomerType`" 
                    + (original.ExternalCRMCustomerType.ToString()) 
                    + "`" 
                    + (modified.ExternalCRMCustomerType.ToString()).Replace("`", "-"));
    if (modified.ExternalCRMLastSync != original.ExternalCRMLastSync)
        changes.Add("ExternalCRMLastSync`" 
                    + (original.ExternalCRMLastSync.ToString()) 
                    + "`" 
                    + (modified.ExternalCRMLastSync.ToString()).Replace("`", "-"));
    if (modified.ExternalCRMSyncRequired != original.ExternalCRMSyncRequired)
        changes.Add("ExternalCRMSyncRequired`" 
                    + (original.ExternalCRMSyncRequired.ToString()) 
                    + "`" 
                    + (modified.ExternalCRMSyncRequired.ToString()).Replace("`", "-"));
    if (modified.Ownership != original.Ownership)
        changes.Add("Ownership`" 
                    + (original.Ownership.ToString()) 
                    + "`" 
                    + (modified.Ownership.ToString()).Replace("`", "-"));
    if (modified.Industry != original.Industry)
        changes.Add("Industry`" 
                    + (original.Industry.ToString()) 
                    + "`" 
                    + (modified.Industry.ToString()).Replace("`", "-"));
    if (modified.AnnualRevenue != original.AnnualRevenue)
        changes.Add("AnnualRevenue`" 
                    + (original.AnnualRevenue.ToString()) 
                    + "`" 
                    + (modified.AnnualRevenue.ToString()).Replace("`", "-"));
    if (modified.NumberOfEmployees != original.NumberOfEmployees)
        changes.Add("NumberOfEmployees`" 
                    + (original.NumberOfEmployees.ToString()) 
                    + "`" 
                    + (modified.NumberOfEmployees.ToString()).Replace("`", "-"));
    if (modified.TickerLocation != original.TickerLocation)
        changes.Add("TickerLocation`" 
                    + (original.TickerLocation.ToString()) 
                    + "`" 
                    + (modified.TickerLocation.ToString()).Replace("`", "-"));
    if (modified.TickerSymbol != original.TickerSymbol)
        changes.Add("TickerSymbol`" 
                    + (original.TickerSymbol.ToString()) 
                    + "`" 
                    + (modified.TickerSymbol.ToString()).Replace("`", "-"));
    if (modified.Rating != original.Rating)
        changes.Add("Rating`" 
                    + (original.Rating.ToString()) 
                    + "`" 
                    + (modified.Rating.ToString()).Replace("`", "-"));
    if (modified.TWGUIRegNum != original.TWGUIRegNum)
        changes.Add("TWGUIRegNum`" 
                    + (original.TWGUIRegNum.ToString()) 
                    + "`" 
                    + (modified.TWGUIRegNum.ToString()).Replace("`", "-"));
    if (modified.MXAccountNumber != original.MXAccountNumber)
        changes.Add("MXAccountNumber`" 
                    + (original.MXAccountNumber.ToString()) 
                    + "`" 
                    + (modified.MXAccountNumber.ToString()).Replace("`", "-"));
    if (modified.ConsolidateLinesPerPart != original.ConsolidateLinesPerPart)
        changes.Add("ConsolidateLinesPerPart`" 
                    + (original.ConsolidateLinesPerPart.ToString()) 
                    + "`" 
                    + (modified.ConsolidateLinesPerPart.ToString()).Replace("`", "-"));
    if (modified.TWTaxPayerType != original.TWTaxPayerType)
        changes.Add("TWTaxPayerType`" 
                    + (original.TWTaxPayerType.ToString()) 
                    + "`" 
                    + (modified.TWTaxPayerType.ToString()).Replace("`", "-"));
    if (modified.TWDeductGUIFormatCode != original.TWDeductGUIFormatCode)
        changes.Add("TWDeductGUIFormatCode`" 
                    + (original.TWDeductGUIFormatCode.ToString()) 
                    + "`" 
                    + (modified.TWDeductGUIFormatCode.ToString()).Replace("`", "-"));
    if (modified.MXCURP != original.MXCURP)
        changes.Add("MXCURP`" 
                    + (original.MXCURP.ToString()) 
                    + "`" 
                    + (modified.MXCURP.ToString()).Replace("`", "-"));
    if (modified.PEAddressID != original.PEAddressID)
        changes.Add("PEAddressID`" 
                    + (original.PEAddressID.ToString()) 
                    + "`" 
                    + (modified.PEAddressID.ToString()).Replace("`", "-"));
    if (modified.PEPerceptionRegime != original.PEPerceptionRegime)
        changes.Add("PEPerceptionRegime`" 
                    + (original.PEPerceptionRegime.ToString()) 
                    + "`" 
                    + (modified.PEPerceptionRegime.ToString()).Replace("`", "-"));
    if (modified.TaxEntityType != original.TaxEntityType)
        changes.Add("TaxEntityType`" 
                    + (original.TaxEntityType.ToString()) 
                    + "`" 
                    + (modified.TaxEntityType.ToString()).Replace("`", "-"));
    if (modified.INGSTComplianceRate != original.INGSTComplianceRate)
        changes.Add("INGSTComplianceRate`" 
                    + (original.INGSTComplianceRate.ToString()) 
                    + "`" 
                    + (modified.INGSTComplianceRate.ToString()).Replace("`", "-"));
    if (modified.INTaxRegistrationID != original.INTaxRegistrationID)
        changes.Add("INTaxRegistrationID`" 
                    + (original.INTaxRegistrationID.ToString()) 
                    + "`" 
                    + (modified.INTaxRegistrationID.ToString()).Replace("`", "-"));
    if (modified.MXPurchaseType != original.MXPurchaseType)
        changes.Add("MXPurchaseType`" 
                    + (original.MXPurchaseType.ToString()) 
                    + "`" 
                    + (modified.MXPurchaseType.ToString()).Replace("`", "-"));
    if (modified.SendToFSA != original.SendToFSA)
        changes.Add("SendToFSA`" 
                    + (original.SendToFSA.ToString()) 
                    + "`" 
                    + (modified.SendToFSA.ToString()).Replace("`", "-"));
    if (modified.MXGeneralPublic != original.MXGeneralPublic)
        changes.Add("MXGeneralPublic`" 
                    + (original.MXGeneralPublic.ToString()) 
                    + "`" 
                    + (modified.MXGeneralPublic.ToString()).Replace("`", "-"));
    if (modified.AgingCreditHold != original.AgingCreditHold)
        changes.Add("AgingCreditHold`" 
                    + (original.AgingCreditHold.ToString()) 
                    + "`" 
                    + (modified.AgingCreditHold.ToString()).Replace("`", "-"));
    if (modified.AgingCreditHoldDate != original.AgingCreditHoldDate)
        changes.Add("AgingCreditHoldDate`" 
                    + (original.AgingCreditHoldDate.ToString()) 
                    + "`" 
                    + (modified.AgingCreditHoldDate.ToString()).Replace("`", "-"));
    if (modified.AgingCreditHoldSource != original.AgingCreditHoldSource)
        changes.Add("AgingCreditHoldSource`" 
                    + (original.AgingCreditHoldSource.ToString()) 
                    + "`" 
                    + (modified.AgingCreditHoldSource.ToString()).Replace("`", "-"));
    if (modified.AgingCreditClearUserID != original.AgingCreditClearUserID)
        changes.Add("AgingCreditClearUserID`" 
                    + (original.AgingCreditClearUserID.ToString()) 
                    + "`" 
                    + (modified.AgingCreditClearUserID.ToString()).Replace("`", "-"));
    if (modified.AgingCreditClearDate != original.AgingCreditClearDate)
        changes.Add("AgingCreditClearDate`" 
                    + (original.AgingCreditClearDate.ToString()) 
                    + "`" 
                    + (modified.AgingCreditClearDate.ToString()).Replace("`", "-"));
    if (modified.AgingCreditCode != original.AgingCreditCode)
        changes.Add("AgingCreditCode`" 
                    + (original.AgingCreditCode.ToString()) 
                    + "`" 
                    + (modified.AgingCreditCode.ToString()).Replace("`", "-"));
    if (modified.ImporterOfRecord != original.ImporterOfRecord)
        changes.Add("ImporterOfRecord`" 
                    + (original.ImporterOfRecord.ToString()) 
                    + "`" 
                    + (modified.ImporterOfRecord.ToString()).Replace("`", "-"));
    if (modified.SEC != original.SEC)
        changes.Add("SEC`" 
                    + (original.SEC.ToString()) 
                    + "`" 
                    + (modified.SEC.ToString()).Replace("`", "-"));
    if (modified.EInvEndpointIDAttr != original.EInvEndpointIDAttr)
        changes.Add("EInvEndpointIDAttr`" 
                    + (original.EInvEndpointIDAttr.ToString()) 
                    + "`" 
                    + (modified.EInvEndpointIDAttr.ToString()).Replace("`", "-"));
    if (modified.UseBlindShipping != original.UseBlindShipping)
        changes.Add("UseBlindShipping`" 
                    + (original.UseBlindShipping.ToString()) 
                    + "`" 
                    + (modified.UseBlindShipping.ToString()).Replace("`", "-"));
    if (modified.ELIEinvoice != original.ELIEinvoice)
        changes.Add("ELIEinvoice`" 
                    + (original.ELIEinvoice.ToString()) 
                    + "`" 
                    + (modified.ELIEinvoice.ToString()).Replace("`", "-"));
    if (modified.ELIDefReportID != original.ELIDefReportID)
        changes.Add("ELIDefReportID`" 
                    + (original.ELIDefReportID.ToString()) 
                    + "`" 
                    + (modified.ELIDefReportID.ToString()).Replace("`", "-"));
    if (modified.ELIDefStyleNum != original.ELIDefStyleNum)
        changes.Add("ELIDefStyleNum`" 
                    + (original.ELIDefStyleNum.ToString()) 
                    + "`" 
                    + (modified.ELIDefStyleNum.ToString()).Replace("`", "-"));
    if (modified.ELIDefToMail != original.ELIDefToMail)
        changes.Add("ELIDefToMail`" 
                    + (original.ELIDefToMail.ToString()) 
                    + "`" 
                    + (modified.ELIDefToMail.ToString()).Replace("`", "-"));
    if (modified.ELIDefCCMail != original.ELIDefCCMail)
        changes.Add("ELIDefCCMail`" 
                    + (original.ELIDefCCMail.ToString()) 
                    + "`" 
                    + (modified.ELIDefCCMail.ToString()).Replace("`", "-"));
    if (modified.ELIDefMailTempID != original.ELIDefMailTempID)
        changes.Add("ELIDefMailTempID`" 
                    + (original.ELIDefMailTempID.ToString()) 
                    + "`" 
                    + (modified.ELIDefMailTempID.ToString()).Replace("`", "-"));
    if (modified.ELISendMail != original.ELISendMail)
        changes.Add("ELISendMail`" 
                    + (original.ELISendMail.ToString()) 
                    + "`" 
                    + (modified.ELISendMail.ToString()).Replace("`", "-"));
    if (modified.COFiscalResp1 != original.COFiscalResp1)
        changes.Add("COFiscalResp1`" 
                    + (original.COFiscalResp1.ToString()) 
                    + "`" 
                    + (modified.COFiscalResp1.ToString()).Replace("`", "-"));
    if (modified.COFiscalResp2 != original.COFiscalResp2)
        changes.Add("COFiscalResp2`" 
                    + (original.COFiscalResp2.ToString()) 
                    + "`" 
                    + (modified.COFiscalResp2.ToString()).Replace("`", "-"));
    if (modified.COFiscalResp3 != original.COFiscalResp3)
        changes.Add("COFiscalResp3`" 
                    + (original.COFiscalResp3.ToString()) 
                    + "`" 
                    + (modified.COFiscalResp3.ToString()).Replace("`", "-"));
    if (modified.COOperType != original.COOperType)
        changes.Add("COOperType`" 
                    + (original.COOperType.ToString()) 
                    + "`" 
                    + (modified.COOperType.ToString()).Replace("`", "-"));
    if (modified.CentralCollection != original.CentralCollection)
        changes.Add("CentralCollection`" 
                    + (original.CentralCollection.ToString()) 
                    + "`" 
                    + (modified.CentralCollection.ToString()).Replace("`", "-"));
    if (modified.NettingVendorNum != original.NettingVendorNum)
        changes.Add("NettingVendorNum`" 
                    + (original.NettingVendorNum.ToString()) 
                    + "`" 
                    + (modified.NettingVendorNum.ToString()).Replace("`", "-"));
    if (modified.EORINumber != original.EORINumber)
        changes.Add("EORINumber`" 
                    + (original.EORINumber.ToString()) 
                    + "`" 
                    + (modified.EORINumber.ToString()).Replace("`", "-"));
    if (modified.AGIsElectronicCreditInvEligible != original.AGIsElectronicCreditInvEligible)
        changes.Add("AGIsElectronicCreditInvEligible`" 
                    + (original.AGIsElectronicCreditInvEligible.ToString()) 
                    + "`" 
                    + (modified.AGIsElectronicCreditInvEligible.ToString()).Replace("`", "-"));
    if (modified.TaxValidationStatus != original.TaxValidationStatus)
        changes.Add("TaxValidationStatus`" 
                    + (original.TaxValidationStatus.ToString()) 
                    + "`" 
                    + (modified.TaxValidationStatus.ToString()).Replace("`", "-"));
    if (modified.TaxValidationDate != original.TaxValidationDate)
        changes.Add("TaxValidationDate`" 
                    + (original.TaxValidationDate.ToString()) 
                    + "`" 
                    + (modified.TaxValidationDate.ToString()).Replace("`", "-"));
    if (modified.HMRCTaxValidationLog != original.HMRCTaxValidationLog)
        changes.Add("HMRCTaxValidationLog`" 
                    + (original.HMRCTaxValidationLog.ToString()) 
                    + "`" 
                    + (modified.HMRCTaxValidationLog.ToString()).Replace("`", "-"));
    if (modified.Inactive != original.Inactive)
        changes.Add("Inactive`" 
                    + (original.Inactive.ToString()) 
                    + "`" 
                    + (modified.Inactive.ToString()).Replace("`", "-"));
    if (modified.ExternalSchemeID != original.ExternalSchemeID)
        changes.Add("ExternalSchemeID`" 
                    + (original.ExternalSchemeID.ToString()) 
                    + "`" 
                    + (modified.ExternalSchemeID.ToString()).Replace("`", "-"));
    if (modified.ELIOperatorCode != original.ELIOperatorCode)
        changes.Add("ELIOperatorCode`" 
                    + (original.ELIOperatorCode.ToString()) 
                    + "`" 
                    + (modified.ELIOperatorCode.ToString()).Replace("`", "-"));
    if (modified.ELISendingOption != original.ELISendingOption)
        changes.Add("ELISendingOption`" 
                    + (original.ELISendingOption.ToString()) 
                    + "`" 
                    + (modified.ELISendingOption.ToString()).Replace("`", "-"));
    if (modified.ELIOperatorID != original.ELIOperatorID)
        changes.Add("ELIOperatorID`" 
                    + (original.ELIOperatorID.ToString()) 
                    + "`" 
                    + (modified.ELIOperatorID.ToString()).Replace("`", "-"));
    if (modified.EInvExternalID != original.EInvExternalID)
        changes.Add("EInvExternalID`" 
                    + (original.EInvExternalID.ToString()) 
                    + "`" 
                    + (modified.EInvExternalID.ToString()).Replace("`", "-"));
    if (modified.MXTaxRegime != original.MXTaxRegime)
        changes.Add("MXTaxRegime`" 
                    + (original.MXTaxRegime.ToString()) 
                    + "`" 
                    + (modified.MXTaxRegime.ToString()).Replace("`", "-"));
    if (modified.ExclusionMonth != original.ExclusionMonth)
        changes.Add("ExclusionMonth`" 
                    + (original.ExclusionMonth.ToString()) 
                    + "`" 
                    + (modified.ExclusionMonth.ToString()).Replace("`", "-"));
    if (modified.FSMSendTo != original.FSMSendTo)
        changes.Add("FSMSendTo`" 
                    + (original.FSMSendTo.ToString()) 
                    + "`" 
                    + (modified.FSMSendTo.ToString()).Replace("`", "-"));
    if (modified.FSMRegionCode != original.FSMRegionCode)
        changes.Add("FSMRegionCode`" 
                    + (original.FSMRegionCode.ToString()) 
                    + "`" 
                    + (modified.FSMRegionCode.ToString()).Replace("`", "-"));
    if (modified.FSMArea != original.FSMArea)
        changes.Add("FSMArea`" 
                    + (original.FSMArea.ToString()) 
                    + "`" 
                    + (modified.FSMArea.ToString()).Replace("`", "-"));
    if (modified.ELIRcptDefStyleNum != original.ELIRcptDefStyleNum)
        changes.Add("ELIRcptDefStyleNum`" 
                    + (original.ELIRcptDefStyleNum.ToString()) 
                    + "`" 
                    + (modified.ELIRcptDefStyleNum.ToString()).Replace("`", "-"));
    if (modified.CovenantDiscPercent != original.CovenantDiscPercent)
        changes.Add("CovenantDiscPercent`" 
                    + (original.CovenantDiscPercent.ToString()) 
                    + "`" 
                    + (modified.CovenantDiscPercent.ToString()).Replace("`", "-"));
    if (modified.DistrictName != original.DistrictName)
        changes.Add("DistrictName`" 
                    + (original.DistrictName.ToString()) 
                    + "`" 
                    + (modified.DistrictName.ToString()).Replace("`", "-"));
    if (modified.StreetName != original.StreetName)
        changes.Add("StreetName`" 
                    + (original.StreetName.ToString()) 
                    + "`" 
                    + (modified.StreetName.ToString()).Replace("`", "-"));
    if (modified.BuildingNumber != original.BuildingNumber)
        changes.Add("BuildingNumber`" 
                    + (original.BuildingNumber.ToString()) 
                    + "`" 
                    + (modified.BuildingNumber.ToString()).Replace("`", "-"));
    if (modified.Floor != original.Floor)
        changes.Add("Floor`" 
                    + (original.Floor.ToString()) 
                    + "`" 
                    + (modified.Floor.ToString()).Replace("`", "-"));
    if (modified.Room != original.Room)
        changes.Add("Room`" 
                    + (original.Room.ToString()) 
                    + "`" 
                    + (modified.Room.ToString()).Replace("`", "-"));
    if (modified.PostBox != original.PostBox)
        changes.Add("PostBox`" 
                    + (original.PostBox.ToString()) 
                    + "`" 
                    + (modified.PostBox.ToString()).Replace("`", "-"));
    if (modified.BTDistrictName != original.BTDistrictName)
        changes.Add("BTDistrictName`" 
                    + (original.BTDistrictName.ToString()) 
                    + "`" 
                    + (modified.BTDistrictName.ToString()).Replace("`", "-"));
    if (modified.BTStreetName != original.BTStreetName)
        changes.Add("BTStreetName`" 
                    + (original.BTStreetName.ToString()) 
                    + "`" 
                    + (modified.BTStreetName.ToString()).Replace("`", "-"));
    if (modified.BTBuildingNumber != original.BTBuildingNumber)
        changes.Add("BTBuildingNumber`" 
                    + (original.BTBuildingNumber.ToString()) 
                    + "`" 
                    + (modified.BTBuildingNumber.ToString()).Replace("`", "-"));
    if (modified.BTFloor != original.BTFloor)
        changes.Add("BTFloor`" 
                    + (original.BTFloor.ToString()) 
                    + "`" 
                    + (modified.BTFloor.ToString()).Replace("`", "-"));
    if (modified.BTRoom != original.BTRoom)
        changes.Add("BTRoom`" 
                    + (original.BTRoom.ToString()) 
                    + "`" 
                    + (modified.BTRoom.ToString()).Replace("`", "-"));
    if (modified.BTPostBox != original.BTPostBox)
        changes.Add("BTPostBox`" 
                    + (original.BTPostBox.ToString()) 
                    + "`" 
                    + (modified.BTPostBox.ToString()).Replace("`", "-"));
    if (modified.MXISTMO != original.MXISTMO)
        changes.Add("MXISTMO`" 
                    + (original.MXISTMO.ToString()) 
                    + "`" 
                    + (modified.MXISTMO.ToString()).Replace("`", "-"));
    if (modified.MXBTReference != original.MXBTReference)
        changes.Add("MXBTReference`" 
                    + (original.MXBTReference.ToString()) 
                    + "`" 
                    + (modified.MXBTReference.ToString()).Replace("`", "-"));
    if (modified.ConsolidateMilestoneBilling != original.ConsolidateMilestoneBilling)
        changes.Add("ConsolidateMilestoneBilling`" 
                    + (original.ConsolidateMilestoneBilling.ToString()) 
                    + "`" 
                    + (modified.ConsolidateMilestoneBilling.ToString()).Replace("`", "-"));
    if (modified.DUNS != original.DUNS)
        changes.Add("DUNS`" 
                    + (original.DUNS.ToString()) 
                    + "`" 
                    + (modified.DUNS.ToString()).Replace("`", "-"));

    if (changes.Count > 0)
    {
        try
        {
            callFunc = true;

            string allChanges = string.Join("`~`", changes);
            test2 = $"Debug: Created allChanges string, length={allChanges.Length}";

            // Simple splitting without complex logic
            if (allChanges.Length <= 1000)
            {
                changesMade = allChanges;
                test2 = "Debug: Single chunk assigned to changesMade";
            }
            else
            {
                // Split into 1000-character chunks with overflow handling
                const int maxChunkSize = 1000;
                int totalLength = allChanges.Length;

                // Clear all changesMade variables first
                changesMade = "";
                changesMade2 = "";
                changesMade3 = "";
                changesMade4 = "";
                changesMade5 = "";
                changesMade6 = "";
                changesMade7 = "";
                changesMade8 = "";
                changesMade9 = "";
                changesMade10 = "";

                // Assign chunks based on position - each exactly 1000 chars (or remaining)
                if (totalLength > 0)
                {
                    changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
                }
                if (totalLength > 1000)
                {
                    changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
                }
                if (totalLength > 2000)
                {
                    changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
                }
                if (totalLength > 3000)
                {
                    changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
                }
                if (totalLength > 4000)
                {
                    changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
                }
                if (totalLength > 5000)
                {
                    changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
                }
                if (totalLength > 6000)
                {
                    changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
                }
                if (totalLength > 7000)
                {
                    changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
                }
                if (totalLength > 8000)
                {
                    changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
                }
                if (totalLength > 9000)
                {
                    changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
                }

                test2 = $"Debug: Split {totalLength} chars into chunks successfully";
            }
        }
        catch (System.Exception ex)
        {
            test2 = $"Debug: Exception in splitting logic: {ex.Message} - Type: {ex.GetType().Name}";
            changesMade = "Error in splitting logic";
        }
    }
}