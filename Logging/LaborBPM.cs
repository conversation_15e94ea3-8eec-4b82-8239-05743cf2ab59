// =================================================================
// Labor Change Tracking BPM
// =================================================================
// This BPM tracks changes to LaborHed and LaborDtl records and logs
// them for auditing purposes. It compares the current dataset with
// the original database records to identify what has changed.
// =================================================================

// =================================================================
// Initialize Variables
// =================================================================

// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;
LaborNumber = "";

// Validate dataset before processing
if (ds.LaborHed == null || ds.LaborHed.Count == 0)
{
    test1 = "Error: No labor data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.LaborHed.Count.ToString()} labor records";

// Track all changes in a list
List<string> changes = new List<string>();

// =================================================================
// Process LaborHed Changes
// =================================================================

// Get the modified labor record
var modifiedLaborHed = ds.LaborHed[0];
int laborHedSeq = modifiedLaborHed.LaborHedSeq;
LaborNumber = laborHedSeq.ToString();
test2 = "Debug: Successfully accessed ds.LaborHed[0]";

// Check if the labor row has been deleted (RowMod = "D" in original record)
if (ds.LaborHed[0].RowMod == "D")
{
    changes.Add("LaborHed deleted");
    callFunc = true;
}
else
{
    // Get the original labor record from database
    var originalLaborHed = (from dbLaborHed in Db.LaborHed
                           where dbLaborHed.Company == companyID
                              && dbLaborHed.LaborHedSeq == laborHedSeq
                           select dbLaborHed).FirstOrDefault();

    // Handle new labor creation
    if (originalLaborHed == null)
    {
        changes.Add($"New labor record created: {modifiedLaborHed.LaborHedSeq.ToString()}");
        callFunc = true;
    }
    else
    {
        // Determine which record to use as the modified version
        // Check if there's a second record (modified version)
        if (ds.LaborHed.Count > 1)
        {
            try
            {
                modifiedLaborHed = ds.LaborHed[1];
                test2 = "Debug: Successfully accessed ds.LaborHed[1]";
            }
            catch (System.Exception ex)
            {
                test2 = $"Error accessing ds.LaborHed[1]: {ex.Message}";
                return;
            }
        }
        else
        {
            test2 = "Debug: Using ds.LaborHed[0] as modified (only 1 record)";
        }

        // =================================================================
        // LaborHed Field Comparisons
        // =================================================================

        // Company
        if (modifiedLaborHed.Company != originalLaborHed.Company)
        {
            changes.Add("LaborHed.Company`"
                       + originalLaborHed.Company.ToString()
                       + "`"
                       + modifiedLaborHed.Company.ToString().Replace("`", "-"));
        }

        // Employee Number
        if (modifiedLaborHed.EmployeeNum != originalLaborHed.EmployeeNum)
        {
            changes.Add("LaborHed.EmployeeNum`"
                       + originalLaborHed.EmployeeNum.ToString()
                       + "`"
                       + modifiedLaborHed.EmployeeNum.ToString().Replace("`", "-"));
        }

        // Labor Head Sequence
        if (modifiedLaborHed.LaborHedSeq != originalLaborHed.LaborHedSeq)
        {
            changes.Add("LaborHed.LaborHedSeq`"
                       + originalLaborHed.LaborHedSeq.ToString()
                       + "`"
                       + modifiedLaborHed.LaborHedSeq.ToString().Replace("`", "-"));
        }

        // Payroll Date
        if (modifiedLaborHed.PayrollDate != originalLaborHed.PayrollDate)
        {
            changes.Add("LaborHed.PayrollDate`"
                       + originalLaborHed.PayrollDate.ToString()
                       + "`"
                       + modifiedLaborHed.PayrollDate.ToString().Replace("`", "-"));
        }

        // Shift
        if (modifiedLaborHed.Shift != originalLaborHed.Shift)
        {
            changes.Add("LaborHed.Shift`"
                       + originalLaborHed.Shift.ToString()
                       + "`"
                       + modifiedLaborHed.Shift.ToString().Replace("`", "-"));
        }

        // Clock In Date
        if (modifiedLaborHed.ClockInDate != originalLaborHed.ClockInDate)
        {
            changes.Add("LaborHed.ClockInDate`"
                       + originalLaborHed.ClockInDate.ToString()
                       + "`"
                       + modifiedLaborHed.ClockInDate.ToString().Replace("`", "-"));
        }

        // Clock In Time
        if (modifiedLaborHed.ClockInTime != originalLaborHed.ClockInTime)
        {
            changes.Add("LaborHed.ClockInTime`"
                       + originalLaborHed.ClockInTime.ToString()
                       + "`"
                       + modifiedLaborHed.ClockInTime.ToString().Replace("`", "-"));
        }

        // Display Clock In Time
        if (modifiedLaborHed.DspClockInTime != originalLaborHed.DspClockInTime)
        {
            changes.Add("LaborHed.DspClockInTime`"
                       + originalLaborHed.DspClockInTime.ToString()
                       + "`"
                       + modifiedLaborHed.DspClockInTime.ToString().Replace("`", "-"));
        }

        // Actual Clock In Time
        if (modifiedLaborHed.ActualClockInTime != originalLaborHed.ActualClockInTime)
        {
            changes.Add("LaborHed.ActualClockInTime`"
                       + originalLaborHed.ActualClockInTime.ToString()
                       + "`"
                       + modifiedLaborHed.ActualClockInTime.ToString().Replace("`", "-"));
        }

        // Actual Clock In Date
        if (modifiedLaborHed.ActualClockinDate != originalLaborHed.ActualClockinDate)
        {
            changes.Add("LaborHed.ActualClockinDate`"
                       + originalLaborHed.ActualClockinDate.ToString()
                       + "`"
                       + modifiedLaborHed.ActualClockinDate.ToString().Replace("`", "-"));
        }

        // Lunch Status
        if (modifiedLaborHed.LunchStatus != originalLaborHed.LunchStatus)
        {
            changes.Add("LaborHed.LunchStatus`"
                       + originalLaborHed.LunchStatus.ToString()
                       + "`"
                       + modifiedLaborHed.LunchStatus.ToString().Replace("`", "-"));
        }

        // Actual Lunch Out Time
        if (modifiedLaborHed.ActLunchOutTime != originalLaborHed.ActLunchOutTime)
        {
            changes.Add("LaborHed.ActLunchOutTime`"
                       + originalLaborHed.ActLunchOutTime.ToString()
                       + "`"
                       + modifiedLaborHed.ActLunchOutTime.ToString().Replace("`", "-"));
        }

        // Lunch Out Time
        if (modifiedLaborHed.LunchOutTime != originalLaborHed.LunchOutTime)
        {
            changes.Add("LaborHed.LunchOutTime`"
                       + originalLaborHed.LunchOutTime.ToString()
                       + "`"
                       + modifiedLaborHed.LunchOutTime.ToString().Replace("`", "-"));
        }

        // Actual Lunch In Time
        if (modifiedLaborHed.ActLunchInTime != originalLaborHed.ActLunchInTime)
        {
            changes.Add("LaborHed.ActLunchInTime`"
                       + originalLaborHed.ActLunchInTime.ToString()
                       + "`"
                       + modifiedLaborHed.ActLunchInTime.ToString().Replace("`", "-"));
        }

        // Lunch In Time
        if (modifiedLaborHed.LunchInTime != originalLaborHed.LunchInTime)
        {
            changes.Add("LaborHed.LunchInTime`"
                       + originalLaborHed.LunchInTime.ToString()
                       + "`"
                       + modifiedLaborHed.LunchInTime.ToString().Replace("`", "-"));
        }

        // Clock Out Time
        if (modifiedLaborHed.ClockOutTime != originalLaborHed.ClockOutTime)
        {
            changes.Add("LaborHed.ClockOutTime`"
                       + originalLaborHed.ClockOutTime.ToString()
                       + "`"
                       + modifiedLaborHed.ClockOutTime.ToString().Replace("`", "-"));
        }

        // Display Clock Out Time
        if (modifiedLaborHed.DspClockOutTime != originalLaborHed.DspClockOutTime)
        {
            changes.Add("LaborHed.DspClockOutTime`"
                       + originalLaborHed.DspClockOutTime.ToString()
                       + "`"
                       + modifiedLaborHed.DspClockOutTime.ToString().Replace("`", "-"));
        }

        // Actual Clock Out Time
        if (modifiedLaborHed.ActualClockOutTime != originalLaborHed.ActualClockOutTime)
        {
            changes.Add("LaborHed.ActualClockOutTime`"
                       + originalLaborHed.ActualClockOutTime.ToString()
                       + "`"
                       + modifiedLaborHed.ActualClockOutTime.ToString().Replace("`", "-"));
        }

        // Pay Hours
        if (modifiedLaborHed.PayHours != originalLaborHed.PayHours)
        {
            changes.Add("LaborHed.PayHours`"
                       + originalLaborHed.PayHours.ToString()
                       + "`"
                       + modifiedLaborHed.PayHours.ToString().Replace("`", "-"));
        }

        // Feed Payroll
        if (modifiedLaborHed.FeedPayroll != originalLaborHed.FeedPayroll)
        {
            changes.Add("LaborHed.FeedPayroll`"
                       + originalLaborHed.FeedPayroll.ToString()
                       + "`"
                       + modifiedLaborHed.FeedPayroll.ToString().Replace("`", "-"));
        }

        // Transferred To Payroll
        if (modifiedLaborHed.TransferredToPayroll != originalLaborHed.TransferredToPayroll)
        {
            changes.Add("LaborHed.TransferredToPayroll`"
                       + originalLaborHed.TransferredToPayroll.ToString()
                       + "`"
                       + modifiedLaborHed.TransferredToPayroll.ToString().Replace("`", "-"));
        }

        // Labor Collection
        if (modifiedLaborHed.LaborCollection != originalLaborHed.LaborCollection)
        {
            changes.Add("LaborHed.LaborCollection`"
                       + originalLaborHed.LaborCollection.ToString()
                       + "`"
                       + modifiedLaborHed.LaborCollection.ToString().Replace("`", "-"));
        }

        // Transaction Set
        if (modifiedLaborHed.TranSet != originalLaborHed.TranSet)
        {
            changes.Add("LaborHed.TranSet`"
                       + originalLaborHed.TranSet.ToString()
                       + "`"
                       + modifiedLaborHed.TranSet.ToString().Replace("`", "-"));
        }

        // Active Transaction
        if (modifiedLaborHed.ActiveTrans != originalLaborHed.ActiveTrans)
        {
            changes.Add("LaborHed.ActiveTrans`"
                       + originalLaborHed.ActiveTrans.ToString()
                       + "`"
                       + modifiedLaborHed.ActiveTrans.ToString().Replace("`", "-"));
        }

        // Check Link
        if (modifiedLaborHed.ChkLink != originalLaborHed.ChkLink)
        {
            changes.Add("LaborHed.ChkLink`"
                       + originalLaborHed.ChkLink.ToString()
                       + "`"
                       + modifiedLaborHed.ChkLink.ToString().Replace("`", "-"));
        }

        // Batch Total Hours Display
        if (modifiedLaborHed.BatchTotalHrsDisp != originalLaborHed.BatchTotalHrsDisp)
        {
            changes.Add("LaborHed.BatchTotalHrsDisp`"
                       + originalLaborHed.BatchTotalHrsDisp.ToString()
                       + "`"
                       + modifiedLaborHed.BatchTotalHrsDisp.ToString().Replace("`", "-"));
        }

        // Batch Hours Remain Display
        if (modifiedLaborHed.BatchHrsRemainDisp != originalLaborHed.BatchHrsRemainDisp)
        {
            changes.Add("LaborHed.BatchHrsRemainDisp`"
                       + originalLaborHed.BatchHrsRemainDisp.ToString()
                       + "`"
                       + modifiedLaborHed.BatchHrsRemainDisp.ToString().Replace("`", "-"));
        }

        // Batch Hours Remain Percent Display
        if (modifiedLaborHed.BatchHrsRemainPctDisp != originalLaborHed.BatchHrsRemainPctDisp)
        {
            changes.Add("LaborHed.BatchHrsRemainPctDisp`"
                       + originalLaborHed.BatchHrsRemainPctDisp.ToString()
                       + "`"
                       + modifiedLaborHed.BatchHrsRemainPctDisp.ToString().Replace("`", "-"));
        }

        // Batch Split Hours Method
        if (modifiedLaborHed.BatchSplitHrsMethod != originalLaborHed.BatchSplitHrsMethod)
        {
            changes.Add("LaborHed.BatchSplitHrsMethod`"
                       + originalLaborHed.BatchSplitHrsMethod.ToString()
                       + "`"
                       + modifiedLaborHed.BatchSplitHrsMethod.ToString().Replace("`", "-"));
        }

        // Batch Assign To
        if (modifiedLaborHed.BatchAssignTo != originalLaborHed.BatchAssignTo)
        {
            changes.Add("LaborHed.BatchAssignTo`"
                       + originalLaborHed.BatchAssignTo.ToString()
                       + "`"
                       + modifiedLaborHed.BatchAssignTo.ToString().Replace("`", "-"));
        }

        // Batch Complete
        if (modifiedLaborHed.BatchComplete != originalLaborHed.BatchComplete)
        {
            changes.Add("LaborHed.BatchComplete`"
                       + originalLaborHed.BatchComplete.ToString()
                       + "`"
                       + modifiedLaborHed.BatchComplete.ToString().Replace("`", "-"));
        }

        // Batch Start Hours
        if (modifiedLaborHed.BatchStartHrs != originalLaborHed.BatchStartHrs)
        {
            changes.Add("LaborHed.BatchStartHrs`"
                       + originalLaborHed.BatchStartHrs.ToString()
                       + "`"
                       + modifiedLaborHed.BatchStartHrs.ToString().Replace("`", "-"));
        }

        // Batch End Hours
        if (modifiedLaborHed.BatchEndHrs != originalLaborHed.BatchEndHrs)
        {
            changes.Add("LaborHed.BatchEndHrs`"
                       + originalLaborHed.BatchEndHrs.ToString()
                       + "`"
                       + modifiedLaborHed.BatchEndHrs.ToString().Replace("`", "-"));
        }

        // Batch Total Hours
        if (modifiedLaborHed.BatchTotalHrs != originalLaborHed.BatchTotalHrs)
        {
            changes.Add("LaborHed.BatchTotalHrs`"
                       + originalLaborHed.BatchTotalHrs.ToString()
                       + "`"
                       + modifiedLaborHed.BatchTotalHrs.ToString().Replace("`", "-"));
        }

        // Batch Hours Remain
        if (modifiedLaborHed.BatchHrsRemain != originalLaborHed.BatchHrsRemain)
        {
            changes.Add("LaborHed.BatchHrsRemain`"
                       + originalLaborHed.BatchHrsRemain.ToString()
                       + "`"
                       + modifiedLaborHed.BatchHrsRemain.ToString().Replace("`", "-"));
        }

        // Batch Hours Remain Percent
        if (modifiedLaborHed.BatchHrsRemainPct != originalLaborHed.BatchHrsRemainPct)
        {
            changes.Add("LaborHed.BatchHrsRemainPct`"
                       + originalLaborHed.BatchHrsRemainPct.ToString()
                       + "`"
                       + modifiedLaborHed.BatchHrsRemainPct.ToString().Replace("`", "-"));
        }

        // Imported
        if (modifiedLaborHed.Imported != originalLaborHed.Imported)
        {
            changes.Add("LaborHed.Imported`"
                       + originalLaborHed.Imported.ToString()
                       + "`"
                       + modifiedLaborHed.Imported.ToString().Replace("`", "-"));
        }

        // Import Date
        if (modifiedLaborHed.ImportDate != originalLaborHed.ImportDate)
        {
            changes.Add("LaborHed.ImportDate`"
                       + originalLaborHed.ImportDate.ToString()
                       + "`"
                       + modifiedLaborHed.ImportDate.ToString().Replace("`", "-"));
        }

        // Batch Mode
        if (modifiedLaborHed.BatchMode != originalLaborHed.BatchMode)
        {
            changes.Add("LaborHed.BatchMode`"
                       + originalLaborHed.BatchMode.ToString()
                       + "`"
                       + modifiedLaborHed.BatchMode.ToString().Replace("`", "-"));
        }

        // HCM Pay Hours Calculation Type
        if (modifiedLaborHed.HCMPayHoursCalcType != originalLaborHed.HCMPayHoursCalcType)
        {
            changes.Add("LaborHed.HCMPayHoursCalcType`"
                       + originalLaborHed.HCMPayHoursCalcType.ToString()
                       + "`"
                       + modifiedLaborHed.HCMPayHoursCalcType.ToString().Replace("`", "-"));
        }

        // Integration Source
        if (modifiedLaborHed.IntegrationSource != originalLaborHed.IntegrationSource)
        {
            changes.Add("LaborHed.IntegrationSource`"
                       + originalLaborHed.IntegrationSource.ToString()
                       + "`"
                       + modifiedLaborHed.IntegrationSource.ToString().Replace("`", "-"));
        }



    }
}

// =================================================================
// Process LaborDtl Changes
// =================================================================

// Check if LaborDtl data exists
if (ds.LaborDtl != null && ds.LaborDtl.Count > 0)
{
    // Process each LaborDtl record
    foreach (var modifiedDtl in ds.LaborDtl)
    {
        int laborDtlSeq = modifiedDtl.LaborDtlSeq;

        // Check if the detail row has been deleted
        if (modifiedDtl.RowMod == "D")
        {
            changes.Add($"LaborDtl deleted: Sequence {laborDtlSeq}");
            callFunc = true;
            continue;
        }

        // Get the original detail record from database
        var originalDtl = (from dbLaborDtl in Db.LaborDtl
                          where dbLaborDtl.Company == companyID
                             && dbLaborDtl.LaborDtlSeq == laborDtlSeq
                          select dbLaborDtl).FirstOrDefault();

        // Handle new detail creation
        if (originalDtl == null)
        {
            changes.Add($"New LaborDtl created: Sequence {laborDtlSeq}");
            callFunc = true;
            continue;
        }

        // =================================================================
        // LaborDtl Field Comparisons
        // =================================================================

        // Company
        if (modifiedDtl.Company != originalDtl.Company)
        {
            changes.Add($"LaborDtl.Company[Seq {laborDtlSeq}]`"
                       + originalDtl.Company.ToString()
                       + "`"
                       + modifiedDtl.Company.ToString().Replace("`", "-"));
        }

        // Employee Number
        if (modifiedDtl.EmployeeNum != originalDtl.EmployeeNum)
        {
            changes.Add($"LaborDtl.EmployeeNum[Seq {laborDtlSeq}]`"
                       + originalDtl.EmployeeNum.ToString()
                       + "`"
                       + modifiedDtl.EmployeeNum.ToString().Replace("`", "-"));
        }

        // Labor Head Sequence
        if (modifiedDtl.LaborHedSeq != originalDtl.LaborHedSeq)
        {
            changes.Add($"LaborDtl.LaborHedSeq[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborHedSeq.ToString()
                       + "`"
                       + modifiedDtl.LaborHedSeq.ToString().Replace("`", "-"));
        }

        // Labor Detail Sequence
        if (modifiedDtl.LaborDtlSeq != originalDtl.LaborDtlSeq)
        {
            changes.Add($"LaborDtl.LaborDtlSeq[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborDtlSeq.ToString()
                       + "`"
                       + modifiedDtl.LaborDtlSeq.ToString().Replace("`", "-"));
        }

        // Labor Type
        if (modifiedDtl.LaborType != originalDtl.LaborType)
        {
            changes.Add($"LaborDtl.LaborType[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborType.ToString()
                       + "`"
                       + modifiedDtl.LaborType.ToString().Replace("`", "-"));
        }

        // Labor Type Pseudo
        if (modifiedDtl.LaborTypePseudo != originalDtl.LaborTypePseudo)
        {
            changes.Add($"LaborDtl.LaborTypePseudo[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborTypePseudo.ToString()
                       + "`"
                       + modifiedDtl.LaborTypePseudo.ToString().Replace("`", "-"));
        }

        // Rework
        if (modifiedDtl.ReWork != originalDtl.ReWork)
        {
            changes.Add($"LaborDtl.ReWork[Seq {laborDtlSeq}]`"
                       + originalDtl.ReWork.ToString()
                       + "`"
                       + modifiedDtl.ReWork.ToString().Replace("`", "-"));
        }

        // Rework Reason Code
        if (modifiedDtl.ReworkReasonCode != originalDtl.ReworkReasonCode)
        {
            changes.Add($"LaborDtl.ReworkReasonCode[Seq {laborDtlSeq}]`"
                       + originalDtl.ReworkReasonCode.ToString()
                       + "`"
                       + modifiedDtl.ReworkReasonCode.ToString().Replace("`", "-"));
        }

        // Job Number
        if (modifiedDtl.JobNum != originalDtl.JobNum)
        {
            changes.Add($"LaborDtl.JobNum[Seq {laborDtlSeq}]`"
                       + originalDtl.JobNum.ToString()
                       + "`"
                       + modifiedDtl.JobNum.ToString().Replace("`", "-"));
        }

        // Assembly Sequence
        if (modifiedDtl.AssemblySeq != originalDtl.AssemblySeq)
        {
            changes.Add($"LaborDtl.AssemblySeq[Seq {laborDtlSeq}]`"
                       + originalDtl.AssemblySeq.ToString()
                       + "`"
                       + modifiedDtl.AssemblySeq.ToString().Replace("`", "-"));
        }

        // Operation Sequence
        if (modifiedDtl.OprSeq != originalDtl.OprSeq)
        {
            changes.Add($"LaborDtl.OprSeq[Seq {laborDtlSeq}]`"
                       + originalDtl.OprSeq.ToString()
                       + "`"
                       + modifiedDtl.OprSeq.ToString().Replace("`", "-"));
        }

        // Job Control Department
        if (modifiedDtl.JCDept != originalDtl.JCDept)
        {
            changes.Add($"LaborDtl.JCDept[Seq {laborDtlSeq}]`"
                       + originalDtl.JCDept.ToString()
                       + "`"
                       + modifiedDtl.JCDept.ToString().Replace("`", "-"));
        }

        // Resource Group ID
        if (modifiedDtl.ResourceGrpID != originalDtl.ResourceGrpID)
        {
            changes.Add($"LaborDtl.ResourceGrpID[Seq {laborDtlSeq}]`"
                       + originalDtl.ResourceGrpID.ToString()
                       + "`"
                       + modifiedDtl.ResourceGrpID.ToString().Replace("`", "-"));
        }

        // Operation Code
        if (modifiedDtl.OpCode != originalDtl.OpCode)
        {
            changes.Add($"LaborDtl.OpCode[Seq {laborDtlSeq}]`"
                       + originalDtl.OpCode.ToString()
                       + "`"
                       + modifiedDtl.OpCode.ToString().Replace("`", "-"));
        }

        // Labor Hours
        if (modifiedDtl.LaborHrs != originalDtl.LaborHrs)
        {
            changes.Add($"LaborDtl.LaborHrs[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborHrs.ToString()
                       + "`"
                       + modifiedDtl.LaborHrs.ToString().Replace("`", "-"));
        }

        // Burden Hours
        if (modifiedDtl.BurdenHrs != originalDtl.BurdenHrs)
        {
            changes.Add($"LaborDtl.BurdenHrs[Seq {laborDtlSeq}]`"
                       + originalDtl.BurdenHrs.ToString()
                       + "`"
                       + modifiedDtl.BurdenHrs.ToString().Replace("`", "-"));
        }

        // Labor Quantity
        if (modifiedDtl.LaborQty != originalDtl.LaborQty)
        {
            changes.Add($"LaborDtl.LaborQty[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborQty.ToString()
                       + "`"
                       + modifiedDtl.LaborQty.ToString().Replace("`", "-"));
        }

        // Scrap Quantity
        if (modifiedDtl.ScrapQty != originalDtl.ScrapQty)
        {
            changes.Add($"LaborDtl.ScrapQty[Seq {laborDtlSeq}]`"
                       + originalDtl.ScrapQty.ToString()
                       + "`"
                       + modifiedDtl.ScrapQty.ToString().Replace("`", "-"));
        }

        // Scrap Reason Code
        if (modifiedDtl.ScrapReasonCode != originalDtl.ScrapReasonCode)
        {
            changes.Add($"LaborDtl.ScrapReasonCode[Seq {laborDtlSeq}]`"
                       + originalDtl.ScrapReasonCode.ToString()
                       + "`"
                       + modifiedDtl.ScrapReasonCode.ToString().Replace("`", "-"));
        }

        // Setup Percent Complete
        if (modifiedDtl.SetupPctComplete != originalDtl.SetupPctComplete)
        {
            changes.Add($"LaborDtl.SetupPctComplete[Seq {laborDtlSeq}]`"
                       + originalDtl.SetupPctComplete.ToString()
                       + "`"
                       + modifiedDtl.SetupPctComplete.ToString().Replace("`", "-"));
        }

        // Complete
        if (modifiedDtl.Complete != originalDtl.Complete)
        {
            changes.Add($"LaborDtl.Complete[Seq {laborDtlSeq}]`"
                       + originalDtl.Complete.ToString()
                       + "`"
                       + modifiedDtl.Complete.ToString().Replace("`", "-"));
        }

        // Indirect Code
        if (modifiedDtl.IndirectCode != originalDtl.IndirectCode)
        {
            changes.Add($"LaborDtl.IndirectCode[Seq {laborDtlSeq}]`"
                       + originalDtl.IndirectCode.ToString()
                       + "`"
                       + modifiedDtl.IndirectCode.ToString().Replace("`", "-"));
        }

        // Labor Note
        if (modifiedDtl.LaborNote != originalDtl.LaborNote)
        {
            changes.Add($"LaborDtl.LaborNote[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborNote.ToString()
                       + "`"
                       + modifiedDtl.LaborNote.ToString().Replace("`", "-"));
        }

        // Expense Code
        if (modifiedDtl.ExpenseCode != originalDtl.ExpenseCode)
        {
            changes.Add($"LaborDtl.ExpenseCode[Seq {laborDtlSeq}]`"
                       + originalDtl.ExpenseCode.ToString()
                       + "`"
                       + modifiedDtl.ExpenseCode.ToString().Replace("`", "-"));
        }

        // Labor Collection
        if (modifiedDtl.LaborCollection != originalDtl.LaborCollection)
        {
            changes.Add($"LaborDtl.LaborCollection[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborCollection.ToString()
                       + "`"
                       + modifiedDtl.LaborCollection.ToString().Replace("`", "-"));
        }

        // Applied To Schedule
        if (modifiedDtl.AppliedToSchedule != originalDtl.AppliedToSchedule)
        {
            changes.Add($"LaborDtl.AppliedToSchedule[Seq {laborDtlSeq}]`"
                       + originalDtl.AppliedToSchedule.ToString()
                       + "`"
                       + modifiedDtl.AppliedToSchedule.ToString().Replace("`", "-"));
        }

        // Clock In Date
        if (modifiedDtl.ClockInDate != originalDtl.ClockInDate)
        {
            changes.Add($"LaborDtl.ClockInDate[Seq {laborDtlSeq}]`"
                       + originalDtl.ClockInDate.ToString()
                       + "`"
                       + modifiedDtl.ClockInDate.ToString().Replace("`", "-"));
        }

        // Clock In Time
        if (modifiedDtl.ClockinTime != originalDtl.ClockinTime)
        {
            changes.Add($"LaborDtl.ClockinTime[Seq {laborDtlSeq}]`"
                       + originalDtl.ClockinTime.ToString()
                       + "`"
                       + modifiedDtl.ClockinTime.ToString().Replace("`", "-"));
        }

        // Clock Out Time
        if (modifiedDtl.ClockOutTime != originalDtl.ClockOutTime)
        {
            changes.Add($"LaborDtl.ClockOutTime[Seq {laborDtlSeq}]`"
                       + originalDtl.ClockOutTime.ToString()
                       + "`"
                       + modifiedDtl.ClockOutTime.ToString().Replace("`", "-"));
        }

        // Active Transaction
        if (modifiedDtl.ActiveTrans != originalDtl.ActiveTrans)
        {
            changes.Add($"LaborDtl.ActiveTrans[Seq {laborDtlSeq}]`"
                       + originalDtl.ActiveTrans.ToString()
                       + "`"
                       + modifiedDtl.ActiveTrans.ToString().Replace("`", "-"));
        }

        // Override Pay Rate
        if (modifiedDtl.OverRidePayRate != originalDtl.OverRidePayRate)
        {
            changes.Add($"LaborDtl.OverRidePayRate[Seq {laborDtlSeq}]`"
                       + originalDtl.OverRidePayRate.ToString()
                       + "`"
                       + modifiedDtl.OverRidePayRate.ToString().Replace("`", "-"));
        }

        // Labor Rate
        if (modifiedDtl.LaborRate != originalDtl.LaborRate)
        {
            changes.Add($"LaborDtl.LaborRate[Seq {laborDtlSeq}]`"
                       + originalDtl.LaborRate.ToString()
                       + "`"
                       + modifiedDtl.LaborRate.ToString().Replace("`", "-"));
        }

        // Burden Rate
        if (modifiedDtl.BurdenRate != originalDtl.BurdenRate)
        {
            changes.Add($"LaborDtl.BurdenRate[Seq {laborDtlSeq}]`"
                       + originalDtl.BurdenRate.ToString()
                       + "`"
                       + modifiedDtl.BurdenRate.ToString().Replace("`", "-"));
        }

        // Resource ID
        if (modifiedDtl.ResourceID != originalDtl.ResourceID)
        {
            changes.Add($"LaborDtl.ResourceID[Seq {laborDtlSeq}]`"
                       + originalDtl.ResourceID.ToString()
                       + "`"
                       + modifiedDtl.ResourceID.ToString().Replace("`", "-"));
        }

        // Operation Complete
        if (modifiedDtl.OpComplete != originalDtl.OpComplete)
        {
            changes.Add($"LaborDtl.OpComplete[Seq {laborDtlSeq}]`"
                       + originalDtl.OpComplete.ToString()
                       + "`"
                       + modifiedDtl.OpComplete.ToString().Replace("`", "-"));
        }

        // Earned Hours
        if (modifiedDtl.EarnedHrs != originalDtl.EarnedHrs)
        {
            changes.Add($"LaborDtl.EarnedHrs[Seq {laborDtlSeq}]`"
                       + originalDtl.EarnedHrs.ToString()
                       + "`"
                       + modifiedDtl.EarnedHrs.ToString().Replace("`", "-"));
        }

        // Added Operation
        if (modifiedDtl.AddedOper != originalDtl.AddedOper)
        {
            changes.Add($"LaborDtl.AddedOper[Seq {laborDtlSeq}]`"
                       + originalDtl.AddedOper.ToString()
                       + "`"
                       + modifiedDtl.AddedOper.ToString().Replace("`", "-"));
        }

        // Payroll Date
        if (modifiedDtl.PayrollDate != originalDtl.PayrollDate)
        {
            changes.Add($"LaborDtl.PayrollDate[Seq {laborDtlSeq}]`"
                       + originalDtl.PayrollDate.ToString()
                       + "`"
                       + modifiedDtl.PayrollDate.ToString().Replace("`", "-"));
        }

        // Posted To GL
        if (modifiedDtl.PostedToGL != originalDtl.PostedToGL)
        {
            changes.Add($"LaborDtl.PostedToGL[Seq {laborDtlSeq}]`"
                       + originalDtl.PostedToGL.ToString()
                       + "`"
                       + modifiedDtl.PostedToGL.ToString().Replace("`", "-"));
        }

        // Fiscal Year
        if (modifiedDtl.FiscalYear != originalDtl.FiscalYear)
        {
            changes.Add($"LaborDtl.FiscalYear[Seq {laborDtlSeq}]`"
                       + originalDtl.FiscalYear.ToString()
                       + "`"
                       + modifiedDtl.FiscalYear.ToString().Replace("`", "-"));
        }

        // Fiscal Period
        if (modifiedDtl.FiscalPeriod != originalDtl.FiscalPeriod)
        {
            changes.Add($"LaborDtl.FiscalPeriod[Seq {laborDtlSeq}]`"
                       + originalDtl.FiscalPeriod.ToString()
                       + "`"
                       + modifiedDtl.FiscalPeriod.ToString().Replace("`", "-"));
        }

        // Journal Number
        if (modifiedDtl.JournalNum != originalDtl.JournalNum)
        {
            changes.Add($"LaborDtl.JournalNum[Seq {laborDtlSeq}]`"
                       + originalDtl.JournalNum.ToString()
                       + "`"
                       + modifiedDtl.JournalNum.ToString().Replace("`", "-"));
        }

        // GL Transaction
        if (modifiedDtl.GLTrans != originalDtl.GLTrans)
        {
            changes.Add($"LaborDtl.GLTrans[Seq {laborDtlSeq}]`"
                       + originalDtl.GLTrans.ToString()
                       + "`"
                       + modifiedDtl.GLTrans.ToString().Replace("`", "-"));
        }

        // Journal Code
        if (modifiedDtl.JournalCode != originalDtl.JournalCode)
        {
            changes.Add($"LaborDtl.JournalCode[Seq {laborDtlSeq}]`"
                       + originalDtl.JournalCode.ToString()
                       + "`"
                       + modifiedDtl.JournalCode.ToString().Replace("`", "-"));
        }

        // Inspection Pending
        if (modifiedDtl.InspectionPending != originalDtl.InspectionPending)
        {
            changes.Add($"LaborDtl.InspectionPending[Seq {laborDtlSeq}]`"
                       + originalDtl.InspectionPending.ToString()
                       + "`"
                       + modifiedDtl.InspectionPending.ToString().Replace("`", "-"));
        }

        // Call Number
        if (modifiedDtl.CallNum != originalDtl.CallNum)
        {
            changes.Add($"LaborDtl.CallNum[Seq {laborDtlSeq}]`"
                       + originalDtl.CallNum.ToString()
                       + "`"
                       + modifiedDtl.CallNum.ToString().Replace("`", "-"));
        }

        // Call Line
        if (modifiedDtl.CallLine != originalDtl.CallLine)
        {
            changes.Add($"LaborDtl.CallLine[Seq {laborDtlSeq}]`"
                       + originalDtl.CallLine.ToString()
                       + "`"
                       + modifiedDtl.CallLine.ToString().Replace("`", "-"));
        }

        // Service Number
        if (modifiedDtl.ServNum != originalDtl.ServNum)
        {
            changes.Add($"LaborDtl.ServNum[Seq {laborDtlSeq}]`"
                       + originalDtl.ServNum.ToString()
                       + "`"
                       + modifiedDtl.ServNum.ToString().Replace("`", "-"));
        }

        // Service Code
        if (modifiedDtl.ServCode != originalDtl.ServCode)
        {
            changes.Add($"LaborDtl.ServCode[Seq {laborDtlSeq}]`"
                       + originalDtl.ServCode.ToString()
                       + "`"
                       + modifiedDtl.ServCode.ToString().Replace("`", "-"));
        }

    }
}

// =================================================================
// Final Processing
// =================================================================

// If changes were detected, call the function and log the changes
if (changes.Count > 0)
{
    callFunc = true;

    // Join all changes with the delimiter
    string allChanges = string.Join("`~`", changes);
    int totalLength = allChanges.Length;
    int maxChunkSize = 1000;

    // Assign chunks based on position - each exactly 1000 chars (or remaining)
    if (totalLength > 0)
    {
        changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
    }
    if (totalLength > 1000)
    {
        changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
    }
    if (totalLength > 2000)
    {
        changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
    }
    if (totalLength > 3000)
    {
        changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
    }
    if (totalLength > 4000)
    {
        changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
    }
    if (totalLength > 5000)
    {
        changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
    }
    if (totalLength > 6000)
    {
        changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
    }
    if (totalLength > 7000)
    {
        changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
    }
    if (totalLength > 8000)
    {
        changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
    }
    if (totalLength > 9000)
    {
        changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
    }

    // Set appropriate debug message
    if (totalLength > 10000)
    {
        test2 = $"Warning: {totalLength} characters of changes detected, only first 10000 logged";
    }
    else
    {
        test2 = $"Debug: {changes.Count} changes detected, {totalLength} characters logged";
    }
}
else
{
    test2 = "Debug: No changes detected";
}

// =================================================================
// End of BPM Script
// =================================================================
