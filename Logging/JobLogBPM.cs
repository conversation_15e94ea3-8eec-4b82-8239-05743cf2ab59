// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;

// Validate dataset before processing
if (ds.JobHead == null || ds.JobHead.Count == 0)
{
    test1 = "Error: No job data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.JobHead.Count.ToString()} job records";

// Get the modified job record
var modified = ds.JobHead[0];
jobNum = modified.JobNum.ToString();
test2 = "Debug: Successfully accessed ds.JobHead[0]";

// Check if the row has been deleted (RowMod = "D" in original record)
if (ds.JobHead[0].RowMod == "D")
{
	changesMade = "Job deleted";
	callFunc = true;
}

// Get the original job record from database
var original = (from dbJob in Db.JobHead
                where dbJob.Company == companyID
                   && dbJob.JobNum.ToString() == jobNum
                select dbJob).FirstOrDefault();

// Handle new job creation
if (original == null)
{
    changesMade = $"New job created: {modified.JobNum.ToString()}";
    callFunc = true;
}
else
{
    // Track all changes in a list
    List<string> changes = new List<string>();

    // Determine which record to use as the modified version
    // Check if there's a second record (modified version)
    if (ds.JobHead.Count > 1)
    {
        try
        {
            modified = ds.JobHead[1];
            test2 = "Debug: Successfully accessed ds.JobHead[1]";
        }
        catch (System.Exception ex)
        {
            test2 = $"Error accessing ds.JobHead[1]: {ex.Message}";
            return;
        }
    }
    else
    {
        test2 = "Debug: Using ds.JobHead[0] as modified (only 1 record)";
    }

    // =================================================================
    // JobHead Field Comparisons
    // =================================================================

    // Company
    if (modified.Company != original.Company)
    {
        changes.Add("Company`"
                   + original.Company.ToString()
                   + "`"
                   + modified.Company.ToString().Replace("`", "-"));
    }

    // Job Closed
    if (modified.JobClosed != original.JobClosed)
    {
        changes.Add("JobClosed`"
                   + original.JobClosed.ToString()
                   + "`"
                   + modified.JobClosed.ToString().Replace("`", "-"));
    }

    // Closed Date
    if (modified.ClosedDate != original.ClosedDate)
    {
        changes.Add("ClosedDate`"
                   + original.ClosedDate.ToString()
                   + "`"
                   + modified.ClosedDate.ToString().Replace("`", "-"));
    }

    // Job Complete
    if (modified.JobComplete != original.JobComplete)
    {
        changes.Add("JobComplete`"
                   + original.JobComplete.ToString()
                   + "`"
                   + modified.JobComplete.ToString().Replace("`", "-"));
    }

    // Job Completion Date
    if (modified.JobCompletionDate != original.JobCompletionDate)
    {
        changes.Add("JobCompletionDate`"
                   + original.JobCompletionDate.ToString()
                   + "`"
                   + modified.JobCompletionDate.ToString().Replace("`", "-"));
    }

    // Job Engineered
    if (modified.JobEngineered != original.JobEngineered)
    {
        changes.Add("JobEngineered`"
                   + original.JobEngineered.ToString()
                   + "`"
                   + modified.JobEngineered.ToString().Replace("`", "-"));
    }

    // Check Off 1
    if (modified.CheckOff1 != original.CheckOff1)
    {
        changes.Add("CheckOff1`"
                   + original.CheckOff1.ToString()
                   + "`"
                   + modified.CheckOff1.ToString().Replace("`", "-"));
    }

    // Check Off 2
    if (modified.CheckOff2 != original.CheckOff2)
    {
        changes.Add("CheckOff2`"
                   + original.CheckOff2.ToString()
                   + "`"
                   + modified.CheckOff2.ToString().Replace("`", "-"));
    }

    // Check Off 3
    if (modified.CheckOff3 != original.CheckOff3)
    {
        changes.Add("CheckOff3`"
                   + original.CheckOff3.ToString()
                   + "`"
                   + modified.CheckOff3.ToString().Replace("`", "-"));
    }

    // Check Off 4
    if (modified.CheckOff4 != original.CheckOff4)
    {
        changes.Add("CheckOff4`"
                   + original.CheckOff4.ToString()
                   + "`"
                   + modified.CheckOff4.ToString().Replace("`", "-"));
    }

    // Check Off 5
    if (modified.CheckOff5 != original.CheckOff5)
    {
        changes.Add("CheckOff5`"
                   + original.CheckOff5.ToString()
                   + "`"
                   + modified.CheckOff5.ToString().Replace("`", "-"));
    }

    // Job Released
    if (modified.JobReleased != original.JobReleased)
    {
        changes.Add("JobReleased`"
                   + original.JobReleased.ToString()
                   + "`"
                   + modified.JobReleased.ToString().Replace("`", "-"));
    }

    // Job Held
    if (modified.JobHeld != original.JobHeld)
    {
        changes.Add("JobHeld`"
                   + original.JobHeld.ToString()
                   + "`"
                   + modified.JobHeld.ToString().Replace("`", "-"));
    }

    // Schedule Status
    if (modified.SchedStatus != original.SchedStatus)
    {
        changes.Add("SchedStatus`"
                   + original.SchedStatus.ToString()
                   + "`"
                   + modified.SchedStatus.ToString().Replace("`", "-"));
    }

    // Job Number
    if (modified.JobNum != original.JobNum)
    {
        changes.Add("JobNum`"
                   + original.JobNum.ToString()
                   + "`"
                   + modified.JobNum.ToString().Replace("`", "-"));
    }

    // Part Number
    if (modified.PartNum != original.PartNum)
    {
        changes.Add("PartNum`"
                   + original.PartNum.ToString()
                   + "`"
                   + modified.PartNum.ToString().Replace("`", "-"));
    }

    // Revision Number
    if (modified.RevisionNum != original.RevisionNum)
    {
        changes.Add("RevisionNum`"
                   + original.RevisionNum.ToString()
                   + "`"
                   + modified.RevisionNum.ToString().Replace("`", "-"));
    }

    // Drawing Number
    if (modified.DrawNum != original.DrawNum)
    {
        changes.Add("DrawNum`"
                   + original.DrawNum.ToString()
                   + "`"
                   + modified.DrawNum.ToString().Replace("`", "-"));
    }

    // Part Description
    if (modified.PartDescription != original.PartDescription)
    {
        changes.Add("PartDescription`"
                   + original.PartDescription.ToString()
                   + "`"
                   + modified.PartDescription.ToString().Replace("`", "-"));
    }

    // Production Quantity
    if (modified.ProdQty != original.ProdQty)
    {
        changes.Add("ProdQty`"
                   + original.ProdQty.ToString()
                   + "`"
                   + modified.ProdQty.ToString().Replace("`", "-"));
    }

    // Inventory Unit of Measure
    if (modified.IUM != original.IUM)
    {
        changes.Add("IUM`"
                   + original.IUM.ToString()
                   + "`"
                   + modified.IUM.ToString().Replace("`", "-"));
    }

    // Start Date
    if (modified.StartDate != original.StartDate)
    {
        changes.Add("StartDate`"
                   + original.StartDate.ToString()
                   + "`"
                   + modified.StartDate.ToString().Replace("`", "-"));
    }

    // Start Hour
    if (modified.StartHour != original.StartHour)
    {
        changes.Add("StartHour`"
                   + original.StartHour.ToString()
                   + "`"
                   + modified.StartHour.ToString().Replace("`", "-"));
    }

    // Due Date
    if (modified.DueDate != original.DueDate)
    {
        changes.Add("DueDate`"
                   + original.DueDate.ToString()
                   + "`"
                   + modified.DueDate.ToString().Replace("`", "-"));
    }

    // Due Hour
    if (modified.DueHour != original.DueHour)
    {
        changes.Add("DueHour`"
                   + original.DueHour.ToString()
                   + "`"
                   + modified.DueHour.ToString().Replace("`", "-"));
    }

    // Required Due Date
    if (modified.ReqDueDate != original.ReqDueDate)
    {
        changes.Add("ReqDueDate`"
                   + original.ReqDueDate.ToString()
                   + "`"
                   + modified.ReqDueDate.ToString().Replace("`", "-"));
    }

    // Job Code
    if (modified.JobCode != original.JobCode)
    {
        changes.Add("JobCode`"
                   + original.JobCode.ToString()
                   + "`"
                   + modified.JobCode.ToString().Replace("`", "-"));
    }

    // Quote Number
    if (modified.QuoteNum != original.QuoteNum)
    {
        changes.Add("QuoteNum`"
                   + original.QuoteNum.ToString()
                   + "`"
                   + modified.QuoteNum.ToString().Replace("`", "-"));
    }

    // Quote Line
    if (modified.QuoteLine != original.QuoteLine)
    {
        changes.Add("QuoteLine`"
                   + original.QuoteLine.ToString()
                   + "`"
                   + modified.QuoteLine.ToString().Replace("`", "-"));
    }

    // Production Code
    if (modified.ProdCode != original.ProdCode)
    {
        changes.Add("ProdCode`"
                   + original.ProdCode.ToString()
                   + "`"
                   + modified.ProdCode.ToString().Replace("`", "-"));
    }

    // User Character 1
    if (modified.UserChar1 != original.UserChar1)
    {
        changes.Add("UserChar1`"
                   + original.UserChar1.ToString()
                   + "`"
                   + modified.UserChar1.ToString().Replace("`", "-"));
    }

    // User Character 2
    if (modified.UserChar2 != original.UserChar2)
    {
        changes.Add("UserChar2`"
                   + original.UserChar2.ToString()
                   + "`"
                   + modified.UserChar2.ToString().Replace("`", "-"));
    }

    // User Character 3
    if (modified.UserChar3 != original.UserChar3)
    {
        changes.Add("UserChar3`"
                   + original.UserChar3.ToString()
                   + "`"
                   + modified.UserChar3.ToString().Replace("`", "-"));
    }

    // User Character 4
    if (modified.UserChar4 != original.UserChar4)
    {
        changes.Add("UserChar4`"
                   + original.UserChar4.ToString()
                   + "`"
                   + modified.UserChar4.ToString().Replace("`", "-"));
    }

    // User Date 1
    if (modified.UserDate1 != original.UserDate1)
    {
        changes.Add("UserDate1`"
                   + original.UserDate1.ToString()
                   + "`"
                   + modified.UserDate1.ToString().Replace("`", "-"));
    }

    // User Date 2
    if (modified.UserDate2 != original.UserDate2)
    {
        changes.Add("UserDate2`"
                   + original.UserDate2.ToString()
                   + "`"
                   + modified.UserDate2.ToString().Replace("`", "-"));
    }

    // User Date 3
    if (modified.UserDate3 != original.UserDate3)
    {
        changes.Add("UserDate3`"
                   + original.UserDate3.ToString()
                   + "`"
                   + modified.UserDate3.ToString().Replace("`", "-"));
    }

    // User Date 4
    if (modified.UserDate4 != original.UserDate4)
    {
        changes.Add("UserDate4`"
                   + original.UserDate4.ToString()
                   + "`"
                   + modified.UserDate4.ToString().Replace("`", "-"));
    }

    // User Decimal 1
    if (modified.UserDecimal1 != original.UserDecimal1)
    {
        changes.Add("UserDecimal1`"
                   + original.UserDecimal1.ToString()
                   + "`"
                   + modified.UserDecimal1.ToString().Replace("`", "-"));
    }

    // User Decimal 2
    if (modified.UserDecimal2 != original.UserDecimal2)
    {
        changes.Add("UserDecimal2`"
                   + original.UserDecimal2.ToString()
                   + "`"
                   + modified.UserDecimal2.ToString().Replace("`", "-"));
    }

    // User Integer 1
    if (modified.UserInteger1 != original.UserInteger1)
    {
        changes.Add("UserInteger1`"
                   + original.UserInteger1.ToString()
                   + "`"
                   + modified.UserInteger1.ToString().Replace("`", "-"));
    }

    // User Integer 2
    if (modified.UserInteger2 != original.UserInteger2)
    {
        changes.Add("UserInteger2`"
                   + original.UserInteger2.ToString()
                   + "`"
                   + modified.UserInteger2.ToString().Replace("`", "-"));
    }

    // Comment Text
    if (modified.CommentText != original.CommentText)
    {
        changes.Add("CommentText`"
                   + original.CommentText.ToString()
                   + "`"
                   + modified.CommentText.ToString().Replace("`", "-"));
    }

    // Expense Code
    if (modified.ExpenseCode != original.ExpenseCode)
    {
        changes.Add("ExpenseCode`"
                   + original.ExpenseCode.ToString()
                   + "`"
                   + modified.ExpenseCode.ToString().Replace("`", "-"));
    }

    // In Copy List
    if (modified.InCopyList != original.InCopyList)
    {
        changes.Add("InCopyList`"
                   + original.InCopyList.ToString()
                   + "`"
                   + modified.InCopyList.ToString().Replace("`", "-"));
    }

    // WI Name
    if (modified.WIName != original.WIName)
    {
        changes.Add("WIName`"
                   + original.WIName.ToString()
                   + "`"
                   + modified.WIName.ToString().Replace("`", "-"));
    }

    // WI Start Date
    if (modified.WIStartDate != original.WIStartDate)
    {
        changes.Add("WIStartDate`"
                   + original.WIStartDate.ToString()
                   + "`"
                   + modified.WIStartDate.ToString().Replace("`", "-"));
    }

    // WI Start Hour
    if (modified.WIStartHour != original.WIStartHour)
    {
        changes.Add("WIStartHour`"
                   + original.WIStartHour.ToString()
                   + "`"
                   + modified.WIStartHour.ToString().Replace("`", "-"));
    }

    // WI Due Date
    if (modified.WIDueDate != original.WIDueDate)
    {
        changes.Add("WIDueDate`"
                   + original.WIDueDate.ToString()
                   + "`"
                   + modified.WIDueDate.ToString().Replace("`", "-"));
    }

    // WI Due Hour
    if (modified.WIDueHour != original.WIDueHour)
    {
        changes.Add("WIDueHour`"
                   + original.WIDueHour.ToString()
                   + "`"
                   + modified.WIDueHour.ToString().Replace("`", "-"));
    }

    // Candidate
    if (modified.Candidate != original.Candidate)
    {
        changes.Add("Candidate`"
                   + original.Candidate.ToString()
                   + "`"
                   + modified.Candidate.ToString().Replace("`", "-"));
    }

    // Schedule Code
    if (modified.SchedCode != original.SchedCode)
    {
        changes.Add("SchedCode`"
                   + original.SchedCode.ToString()
                   + "`"
                   + modified.SchedCode.ToString().Replace("`", "-"));
    }

    // Schedule Locked
    if (modified.SchedLocked != original.SchedLocked)
    {
        changes.Add("SchedLocked`"
                   + original.SchedLocked.ToString()
                   + "`"
                   + modified.SchedLocked.ToString().Replace("`", "-"));
    }

    // Project ID
    if (modified.ProjectID != original.ProjectID)
    {
        changes.Add("ProjectID`"
                   + original.ProjectID.ToString()
                   + "`"
                   + modified.ProjectID.ToString().Replace("`", "-"));
    }

    // WIP Cleared
    if (modified.WIPCleared != original.WIPCleared)
    {
        changes.Add("WIPCleared`"
                   + original.WIPCleared.ToString()
                   + "`"
                   + modified.WIPCleared.ToString().Replace("`", "-"));
    }

    // Job Firm
    if (modified.JobFirm != original.JobFirm)
    {
        changes.Add("JobFirm`"
                   + original.JobFirm.ToString()
                   + "`"
                   + modified.JobFirm.ToString().Replace("`", "-"));
    }

    // Person List
    if (modified.PersonList != original.PersonList)
    {
        changes.Add("PersonList`"
                   + original.PersonList.ToString()
                   + "`"
                   + modified.PersonList.ToString().Replace("`", "-"));
    }

    // Person ID
    if (modified.PersonID != original.PersonID)
    {
        changes.Add("PersonID`"
                   + original.PersonID.ToString()
                   + "`"
                   + modified.PersonID.ToString().Replace("`", "-"));
    }

    // Production Team ID
    if (modified.ProdTeamID != original.ProdTeamID)
    {
        changes.Add("ProdTeamID`"
                   + original.ProdTeamID.ToString()
                   + "`"
                   + modified.ProdTeamID.ToString().Replace("`", "-"));
    }

    // Quantity Completed
    if (modified.QtyCompleted != original.QtyCompleted)
    {
        changes.Add("QtyCompleted`"
                   + original.QtyCompleted.ToString()
                   + "`"
                   + modified.QtyCompleted.ToString().Replace("`", "-"));
    }

    // Plant
    if (modified.Plant != original.Plant)
    {
        changes.Add("Plant`"
                   + original.Plant.ToString()
                   + "`"
                   + modified.Plant.ToString().Replace("`", "-"));
    }

    // Date Purged
    if (modified.DatePurged != original.DatePurged)
    {
        changes.Add("DatePurged`"
                   + original.DatePurged.ToString()
                   + "`"
                   + modified.DatePurged.ToString().Replace("`", "-"));
    }

    // Traveler Ready To Print
    if (modified.TravelerReadyToPrint != original.TravelerReadyToPrint)
    {
        changes.Add("TravelerReadyToPrint`"
                   + original.TravelerReadyToPrint.ToString()
                   + "`"
                   + modified.TravelerReadyToPrint.ToString().Replace("`", "-"));
    }

    // Traveler Last Printed
    if (modified.TravelerLastPrinted != original.TravelerLastPrinted)
    {
        changes.Add("TravelerLastPrinted`"
                   + original.TravelerLastPrinted.ToString()
                   + "`"
                   + modified.TravelerLastPrinted.ToString().Replace("`", "-"));
    }

    // Status Ready To Print
    if (modified.StatusReadyToPrint != original.StatusReadyToPrint)
    {
        changes.Add("StatusReadyToPrint`"
                   + original.StatusReadyToPrint.ToString()
                   + "`"
                   + modified.StatusReadyToPrint.ToString().Replace("`", "-"));
    }

    // Status Last Printed
    if (modified.StatusLastPrinted != original.StatusLastPrinted)
    {
        changes.Add("StatusLastPrinted`"
                   + original.StatusLastPrinted.ToString()
                   + "`"
                   + modified.StatusLastPrinted.ToString().Replace("`", "-"));
    }

    // Call Number
    if (modified.CallNum != original.CallNum)
    {
        changes.Add("CallNum`"
                   + original.CallNum.ToString()
                   + "`"
                   + modified.CallNum.ToString().Replace("`", "-"));
    }

    // Call Line
    if (modified.CallLine != original.CallLine)
    {
        changes.Add("CallLine`"
                   + original.CallLine.ToString()
                   + "`"
                   + modified.CallLine.ToString().Replace("`", "-"));
    }

    // Job Type
    if (modified.JobType != original.JobType)
    {
        changes.Add("JobType`"
                   + original.JobType.ToString()
                   + "`"
                   + modified.JobType.ToString().Replace("`", "-"));
    }

    // Restore Flag
    if (modified.RestoreFlag != original.RestoreFlag)
    {
        changes.Add("RestoreFlag`"
                   + original.RestoreFlag.ToString()
                   + "`"
                   + modified.RestoreFlag.ToString().Replace("`", "-"));
    }

    // Phase ID
    if (modified.PhaseID != original.PhaseID)
    {
        changes.Add("PhaseID`"
                   + original.PhaseID.ToString()
                   + "`"
                   + modified.PhaseID.ToString().Replace("`", "-"));
    }

    // Analysis Code
    if (modified.AnalysisCode != original.AnalysisCode)
    {
        changes.Add("AnalysisCode`"
                   + original.AnalysisCode.ToString()
                   + "`"
                   + modified.AnalysisCode.ToString().Replace("`", "-"));
    }

    // Lock Quantity
    if (modified.LockQty != original.LockQty)
    {
        changes.Add("LockQty`"
                   + original.LockQty.ToString()
                   + "`"
                   + modified.LockQty.ToString().Replace("`", "-"));
    }

    // HD Case Number
    if (modified.HDCaseNum != original.HDCaseNum)
    {
        changes.Add("HDCaseNum`"
                   + original.HDCaseNum.ToString()
                   + "`"
                   + modified.HDCaseNum.ToString().Replace("`", "-"));
    }

    // Process Mode
    if (modified.ProcessMode != original.ProcessMode)
    {
        changes.Add("ProcessMode`"
                   + original.ProcessMode.ToString()
                   + "`"
                   + modified.ProcessMode.ToString().Replace("`", "-"));
    }

    // Planned Action Date
    if (modified.PlannedActionDate != original.PlannedActionDate)
    {
        changes.Add("PlannedActionDate`"
                   + original.PlannedActionDate.ToString()
                   + "`"
                   + modified.PlannedActionDate.ToString().Replace("`", "-"));
    }

    // Planned Kit Date
    if (modified.PlannedKitDate != original.PlannedKitDate)
    {
        changes.Add("PlannedKitDate`"
                   + original.PlannedKitDate.ToString()
                   + "`"
                   + modified.PlannedKitDate.ToString().Replace("`", "-"));
    }

    // MSP Task ID
    if (modified.MSPTaskID != original.MSPTaskID)
    {
        changes.Add("MSPTaskID`"
                   + original.MSPTaskID.ToString()
                   + "`"
                   + modified.MSPTaskID.ToString().Replace("`", "-"));
    }

    // MSP Predecessor
    if (modified.MSPPredecessor != original.MSPPredecessor)
    {
        changes.Add("MSPPredecessor`"
                   + original.MSPPredecessor.ToString()
                   + "`"
                   + modified.MSPPredecessor.ToString().Replace("`", "-"));
    }

    // User Map Data
    if (modified.UserMapData != original.UserMapData)
    {
        changes.Add("UserMapData`"
                   + original.UserMapData.ToString()
                   + "`"
                   + modified.UserMapData.ToString().Replace("`", "-"));
    }

    // Production Yield
    if (modified.ProductionYield != original.ProductionYield)
    {
        changes.Add("ProductionYield`"
                   + original.ProductionYield.ToString()
                   + "`"
                   + modified.ProductionYield.ToString().Replace("`", "-"));
    }

    // Original Production Quantity
    if (modified.OrigProdQty != original.OrigProdQty)
    {
        changes.Add("OrigProdQty`"
                   + original.OrigProdQty.ToString()
                   + "`"
                   + modified.OrigProdQty.ToString().Replace("`", "-"));
    }

    // Preserve Original Quantities
    if (modified.PreserveOrigQtys != original.PreserveOrigQtys)
    {
        changes.Add("PreserveOrigQtys`"
                   + original.PreserveOrigQtys.ToString()
                   + "`"
                   + modified.PreserveOrigQtys.ToString().Replace("`", "-"));
    }

    // No Auto Completion
    if (modified.NoAutoCompletion != original.NoAutoCompletion)
    {
        changes.Add("NoAutoCompletion`"
                   + original.NoAutoCompletion.ToString()
                   + "`"
                   + modified.NoAutoCompletion.ToString().Replace("`", "-"));
    }

    // No Auto Closing
    if (modified.NoAutoClosing != original.NoAutoClosing)
    {
        changes.Add("NoAutoClosing`"
                   + original.NoAutoClosing.ToString()
                   + "`"
                   + modified.NoAutoClosing.ToString().Replace("`", "-"));
    }

    // Created By
    if (modified.CreatedBy != original.CreatedBy)
    {
        changes.Add("CreatedBy`"
                   + original.CreatedBy.ToString()
                   + "`"
                   + modified.CreatedBy.ToString().Replace("`", "-"));
    }

    // Create Date
    if (modified.CreateDate != original.CreateDate)
    {
        changes.Add("CreateDate`"
                   + original.CreateDate.ToString()
                   + "`"
                   + modified.CreateDate.ToString().Replace("`", "-"));
    }

    // Warehouse Allocation Flag
    if (modified.WhseAllocFlag != original.WhseAllocFlag)
    {
        changes.Add("WhseAllocFlag`"
                   + original.WhseAllocFlag.ToString()
                   + "`"
                   + modified.WhseAllocFlag.ToString().Replace("`", "-"));
    }

    // Ownership Status
    if (modified.OwnershipStatus != original.OwnershipStatus)
    {
        changes.Add("OwnershipStatus`"
                   + original.OwnershipStatus.ToString()
                   + "`"
                   + modified.OwnershipStatus.ToString().Replace("`", "-"));
    }

    // PDM Object ID
    if (modified.PDMObjID != original.PDMObjID)
    {
        changes.Add("PDMObjID`"
                   + original.PDMObjID.ToString()
                   + "`"
                   + modified.PDMObjID.ToString().Replace("`", "-"));
    }

    // Export Requested
    if (modified.ExportRequested != original.ExportRequested)
    {
        changes.Add("ExportRequested`"
                   + original.ExportRequested.ToString()
                   + "`"
                   + modified.ExportRequested.ToString().Replace("`", "-"));
    }

    // Split Manufacturing Cost Elements
    if (modified.SplitMfgCostElements != original.SplitMfgCostElements)
    {
        changes.Add("SplitMfgCostElements`"
                   + original.SplitMfgCostElements.ToString()
                   + "`"
                   + modified.SplitMfgCostElements.ToString().Replace("`", "-"));
    }

    // Cross Reference Part Number
    if (modified.XRefPartNum != original.XRefPartNum)
    {
        changes.Add("XRefPartNum`"
                   + original.XRefPartNum.ToString()
                   + "`"
                   + modified.XRefPartNum.ToString().Replace("`", "-"));
    }

    // Cross Reference Part Type
    if (modified.XRefPartType != original.XRefPartType)
    {
        changes.Add("XRefPartType`"
                   + original.XRefPartType.ToString()
                   + "`"
                   + modified.XRefPartType.ToString().Replace("`", "-"));
    }

    // Cross Reference Customer Number
    if (modified.XRefCustNum != original.XRefCustNum)
    {
        changes.Add("XRefCustNum`"
                   + original.XRefCustNum.ToString()
                   + "`"
                   + modified.XRefCustNum.ToString().Replace("`", "-"));
    }

    // Base Part Number
    if (modified.BasePartNum != original.BasePartNum)
    {
        changes.Add("BasePartNum`"
                   + original.BasePartNum.ToString()
                   + "`"
                   + modified.BasePartNum.ToString().Replace("`", "-"));
    }

    // Base Revision Number
    if (modified.BaseRevisionNum != original.BaseRevisionNum)
    {
        changes.Add("BaseRevisionNum`"
                   + original.BaseRevisionNum.ToString()
                   + "`"
                   + modified.BaseRevisionNum.ToString().Replace("`", "-"));
    }

    // Rough Cut Scheduled
    if (modified.RoughCutScheduled != original.RoughCutScheduled)
    {
        changes.Add("RoughCutScheduled`"
                   + original.RoughCutScheduled.ToString()
                   + "`"
                   + modified.RoughCutScheduled.ToString().Replace("`", "-"));
    }

    // Equipment ID
    if (modified.EquipID != original.EquipID)
    {
        changes.Add("EquipID`"
                   + original.EquipID.ToString()
                   + "`"
                   + modified.EquipID.ToString().Replace("`", "-"));
    }

    // Plan Number
    if (modified.PlanNum != original.PlanNum)
    {
        changes.Add("PlanNum`"
                   + original.PlanNum.ToString()
                   + "`"
                   + modified.PlanNum.ToString().Replace("`", "-"));
    }

    // Maintenance Priority
    if (modified.MaintPriority != original.MaintPriority)
    {
        changes.Add("MaintPriority`"
                   + original.MaintPriority.ToString()
                   + "`"
                   + modified.MaintPriority.ToString().Replace("`", "-"));
    }

    // Split Job
    if (modified.SplitJob != original.SplitJob)
    {
        changes.Add("SplitJob`"
                   + original.SplitJob.ToString()
                   + "`"
                   + modified.SplitJob.ToString().Replace("`", "-"));
    }

    // Number Source
    if (modified.NumberSource != original.NumberSource)
    {
        changes.Add("NumberSource`"
                   + original.NumberSource.ToString()
                   + "`"
                   + modified.NumberSource.ToString().Replace("`", "-"));
    }

    // Close Meter Reading
    if (modified.CloseMeterReading != original.CloseMeterReading)
    {
        changes.Add("CloseMeterReading`"
                   + original.CloseMeterReading.ToString()
                   + "`"
                   + modified.CloseMeterReading.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 1
    if (modified.IssueTopicID1 != original.IssueTopicID1)
    {
        changes.Add("IssueTopicID1`"
                   + original.IssueTopicID1.ToString()
                   + "`"
                   + modified.IssueTopicID1.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 2
    if (modified.IssueTopicID2 != original.IssueTopicID2)
    {
        changes.Add("IssueTopicID2`"
                   + original.IssueTopicID2.ToString()
                   + "`"
                   + modified.IssueTopicID2.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 3
    if (modified.IssueTopicID3 != original.IssueTopicID3)
    {
        changes.Add("IssueTopicID3`"
                   + original.IssueTopicID3.ToString()
                   + "`"
                   + modified.IssueTopicID3.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 4
    if (modified.IssueTopicID4 != original.IssueTopicID4)
    {
        changes.Add("IssueTopicID4`"
                   + original.IssueTopicID4.ToString()
                   + "`"
                   + modified.IssueTopicID4.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 5
    if (modified.IssueTopicID5 != original.IssueTopicID5)
    {
        changes.Add("IssueTopicID5`"
                   + original.IssueTopicID5.ToString()
                   + "`"
                   + modified.IssueTopicID5.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 6
    if (modified.IssueTopicID6 != original.IssueTopicID6)
    {
        changes.Add("IssueTopicID6`"
                   + original.IssueTopicID6.ToString()
                   + "`"
                   + modified.IssueTopicID6.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 7
    if (modified.IssueTopicID7 != original.IssueTopicID7)
    {
        changes.Add("IssueTopicID7`"
                   + original.IssueTopicID7.ToString()
                   + "`"
                   + modified.IssueTopicID7.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 8
    if (modified.IssueTopicID8 != original.IssueTopicID8)
    {
        changes.Add("IssueTopicID8`"
                   + original.IssueTopicID8.ToString()
                   + "`"
                   + modified.IssueTopicID8.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 9
    if (modified.IssueTopicID9 != original.IssueTopicID9)
    {
        changes.Add("IssueTopicID9`"
                   + original.IssueTopicID9.ToString()
                   + "`"
                   + modified.IssueTopicID9.ToString().Replace("`", "-"));
    }

    // Issue Topic ID 10
    if (modified.IssueTopicID10 != original.IssueTopicID10)
    {
        changes.Add("IssueTopicID10`"
                   + original.IssueTopicID10.ToString()
                   + "`"
                   + modified.IssueTopicID10.ToString().Replace("`", "-"));
    }

    // Issue Topics
    if (modified.IssueTopics != original.IssueTopics)
    {
        changes.Add("IssueTopics`"
                   + original.IssueTopics.ToString()
                   + "`"
                   + modified.IssueTopics.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 1
    if (modified.ResTopicID1 != original.ResTopicID1)
    {
        changes.Add("ResTopicID1`"
                   + original.ResTopicID1.ToString()
                   + "`"
                   + modified.ResTopicID1.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 2
    if (modified.ResTopicID2 != original.ResTopicID2)
    {
        changes.Add("ResTopicID2`"
                   + original.ResTopicID2.ToString()
                   + "`"
                   + modified.ResTopicID2.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 3
    if (modified.ResTopicID3 != original.ResTopicID3)
    {
        changes.Add("ResTopicID3`"
                   + original.ResTopicID3.ToString()
                   + "`"
                   + modified.ResTopicID3.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 4
    if (modified.ResTopicID4 != original.ResTopicID4)
    {
        changes.Add("ResTopicID4`"
                   + original.ResTopicID4.ToString()
                   + "`"
                   + modified.ResTopicID4.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 5
    if (modified.ResTopicID5 != original.ResTopicID5)
    {
        changes.Add("ResTopicID5`"
                   + original.ResTopicID5.ToString()
                   + "`"
                   + modified.ResTopicID5.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 6
    if (modified.ResTopicID6 != original.ResTopicID6)
    {
        changes.Add("ResTopicID6`"
                   + original.ResTopicID6.ToString()
                   + "`"
                   + modified.ResTopicID6.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 7
    if (modified.ResTopicID7 != original.ResTopicID7)
    {
        changes.Add("ResTopicID7`"
                   + original.ResTopicID7.ToString()
                   + "`"
                   + modified.ResTopicID7.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 8
    if (modified.ResTopicID8 != original.ResTopicID8)
    {
        changes.Add("ResTopicID8`"
                   + original.ResTopicID8.ToString()
                   + "`"
                   + modified.ResTopicID8.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 9
    if (modified.ResTopicID9 != original.ResTopicID9)
    {
        changes.Add("ResTopicID9`"
                   + original.ResTopicID9.ToString()
                   + "`"
                   + modified.ResTopicID9.ToString().Replace("`", "-"));
    }

    // Resolution Topic ID 10
    if (modified.ResTopicID10 != original.ResTopicID10)
    {
        changes.Add("ResTopicID10`"
                   + original.ResTopicID10.ToString()
                   + "`"
                   + modified.ResTopicID10.ToString().Replace("`", "-"));
    }

    // Resolution Topics
    if (modified.ResTopics != original.ResTopics)
    {
        changes.Add("ResTopics`"
                   + original.ResTopics.ToString()
                   + "`"
                   + modified.ResTopics.ToString().Replace("`", "-"));
    }

    // Forward
    if (modified.Forward != original.Forward)
    {
        changes.Add("Forward`"
                   + original.Forward.ToString()
                   + "`"
                   + modified.Forward.ToString().Replace("`", "-"));
    }

    // Schedule Sequence
    if (modified.SchedSeq != original.SchedSeq)
    {
        changes.Add("SchedSeq`"
                   + original.SchedSeq.ToString()
                   + "`"
                   + modified.SchedSeq.ToString().Replace("`", "-"));
    }

    // PAA Exists
    if (modified.PAAExists != original.PAAExists)
    {
        changes.Add("PAAExists`"
                   + original.PAAExists.ToString()
                   + "`"
                   + modified.PAAExists.ToString().Replace("`", "-"));
    }

    // Details Within Lead Time
    if (modified.DtlsWithinLeadTime != original.DtlsWithinLeadTime)
    {
        changes.Add("DtlsWithinLeadTime`"
                   + original.DtlsWithinLeadTime.ToString()
                   + "`"
                   + modified.DtlsWithinLeadTime.ToString().Replace("`", "-"));
    }

    // Group Sequence
    if (modified.GroupSeq != original.GroupSeq)
    {
        changes.Add("GroupSeq`"
                   + original.GroupSeq.ToString()
                   + "`"
                   + modified.GroupSeq.ToString().Replace("`", "-"));
    }

    // Rough Cut
    if (modified.RoughCut != original.RoughCut)
    {
        changes.Add("RoughCut`"
                   + original.RoughCut.ToString()
                   + "`"
                   + modified.RoughCut.ToString().Replace("`", "-"));
    }

    // Plan GUID
    if (modified.PlanGUID != original.PlanGUID)
    {
        changes.Add("PlanGUID`"
                   + original.PlanGUID.ToString()
                   + "`"
                   + modified.PlanGUID.ToString().Replace("`", "-"));
    }

    // Plan User ID
    if (modified.PlanUserID != original.PlanUserID)
    {
        changes.Add("PlanUserID`"
                   + original.PlanUserID.ToString()
                   + "`"
                   + modified.PlanUserID.ToString().Replace("`", "-"));
    }

    // Last Changed By
    if (modified.LastChangedBy != original.LastChangedBy)
    {
        changes.Add("LastChangedBy`"
                   + original.LastChangedBy.ToString()
                   + "`"
                   + modified.LastChangedBy.ToString().Replace("`", "-"));
    }

    // Last Changed On
    if (modified.LastChangedOn != original.LastChangedOn)
    {
        changes.Add("LastChangedOn`"
                   + original.LastChangedOn.ToString()
                   + "`"
                   + modified.LastChangedOn.ToString().Replace("`", "-"));
    }

    // EPM Export Level
    if (modified.EPMExportLevel != original.EPMExportLevel)
    {
        changes.Add("EPMExportLevel`"
                   + original.EPMExportLevel.ToString()
                   + "`"
                   + modified.EPMExportLevel.ToString().Replace("`", "-"));
    }

    // Job Workflow State
    if (modified.JobWorkflowState != original.JobWorkflowState)
    {
        changes.Add("JobWorkflowState`"
                   + original.JobWorkflowState.ToString()
                   + "`"
                   + modified.JobWorkflowState.ToString().Replace("`", "-"));
    }

    // Job CSR
    if (modified.JobCSR != original.JobCSR)
    {
        changes.Add("JobCSR`"
                   + original.JobCSR.ToString()
                   + "`"
                   + modified.JobCSR.ToString().Replace("`", "-"));
    }

    // External MES
    if (modified.ExternalMES != original.ExternalMES)
    {
        changes.Add("ExternalMES`"
                   + original.ExternalMES.ToString()
                   + "`"
                   + modified.ExternalMES.ToString().Replace("`", "-"));
    }

    // Last External MES Date
    if (modified.LastExternalMESDate != original.LastExternalMESDate)
    {
        changes.Add("LastExternalMESDate`"
                   + original.LastExternalMESDate.ToString()
                   + "`"
                   + modified.LastExternalMESDate.ToString().Replace("`", "-"));
    }

    // Last Schedule Date
    if (modified.LastScheduleDate != original.LastScheduleDate)
    {
        changes.Add("LastScheduleDate`"
                   + original.LastScheduleDate.ToString()
                   + "`"
                   + modified.LastScheduleDate.ToString().Replace("`", "-"));
    }

    // Last Schedule Process
    if (modified.LastScheduleProc != original.LastScheduleProc)
    {
        changes.Add("LastScheduleProc`"
                   + original.LastScheduleProc.ToString()
                   + "`"
                   + modified.LastScheduleProc.ToString().Replace("`", "-"));
    }

    // Schedule Priority
    if (modified.SchedPriority != original.SchedPriority)
    {
        changes.Add("SchedPriority`"
                   + original.SchedPriority.ToString()
                   + "`"
                   + modified.SchedPriority.ToString().Replace("`", "-"));
    }

    // Days Late
    if (modified.DaysLate != original.DaysLate)
    {
        changes.Add("DaysLate`"
                   + original.DaysLate.ToString()
                   + "`"
                   + modified.DaysLate.ToString().Replace("`", "-"));
    }

    // Contract ID
    if (modified.ContractID != original.ContractID)
    {
        changes.Add("ContractID`"
                   + original.ContractID.ToString()
                   + "`"
                   + modified.ContractID.ToString().Replace("`", "-"));
    }

    // Project Processed
    if (modified.ProjProcessed != original.ProjProcessed)
    {
        changes.Add("ProjProcessed`"
                   + original.ProjProcessed.ToString()
                   + "`"
                   + modified.ProjProcessed.ToString().Replace("`", "-"));
    }

    // Sync Required By
    if (modified.SyncReqBy != original.SyncReqBy)
    {
        changes.Add("SyncReqBy`"
                   + original.SyncReqBy.ToString()
                   + "`"
                   + modified.SyncReqBy.ToString().Replace("`", "-"));
    }

    // Is CSR Set
    if (modified.IsCSRSet != original.IsCSRSet)
    {
        changes.Add("IsCSRSet`"
                   + original.IsCSRSet.ToString()
                   + "`"
                   + modified.IsCSRSet.ToString().Replace("`", "-"));
    }

    // Unready Cost Process
    if (modified.UnReadyCostProcess != original.UnReadyCostProcess)
    {
        changes.Add("UnReadyCostProcess`"
                   + original.UnReadyCostProcess.ToString()
                   + "`"
                   + modified.UnReadyCostProcess.ToString().Replace("`", "-"));
    }

    // Process Suspended Updates
    if (modified.ProcSuspendedUpdates != original.ProcSuspendedUpdates)
    {
        changes.Add("ProcSuspendedUpdates`"
                   + original.ProcSuspendedUpdates.ToString()
                   + "`"
                   + modified.ProcSuspendedUpdates.ToString().Replace("`", "-"));
    }

    // Project Processed Date
    if (modified.ProjProcessedDate != original.ProjProcessedDate)
    {
        changes.Add("ProjProcessedDate`"
                   + original.ProjProcessedDate.ToString()
                   + "`"
                   + modified.ProjProcessedDate.ToString().Replace("`", "-"));
    }

    // PC Link Removed
    if (modified.PCLinkRemoved != original.PCLinkRemoved)
    {
        changes.Add("PCLinkRemoved`"
                   + original.PCLinkRemoved.ToString()
                   + "`"
                   + modified.PCLinkRemoved.ToString().Replace("`", "-"));
    }

    // External MES Sync Required
    if (modified.ExternalMESSyncRequired != original.ExternalMESSyncRequired)
    {
        changes.Add("ExternalMESSyncRequired`"
                   + original.ExternalMESSyncRequired.ToString()
                   + "`"
                   + modified.ExternalMESSyncRequired.ToString().Replace("`", "-"));
    }

    // External MES Last Sync
    if (modified.ExternalMESLastSync != original.ExternalMESLastSync)
    {
        changes.Add("ExternalMESLastSync`"
                   + original.ExternalMESLastSync.ToString()
                   + "`"
                   + modified.ExternalMESLastSync.ToString().Replace("`", "-"));
    }

    // Epicor FSA
    if (modified.EpicorFSA != original.EpicorFSA)
    {
        changes.Add("EpicorFSA`"
                   + original.EpicorFSA.ToString()
                   + "`"
                   + modified.EpicorFSA.ToString().Replace("`", "-"));
    }

    // KB Config Product ID
    if (modified.KBConfigProdID != original.KBConfigProdID)
    {
        changes.Add("KBConfigProdID`"
                   + original.KBConfigProdID.ToString()
                   + "`"
                   + modified.KBConfigProdID.ToString().Replace("`", "-"));
    }

    // Use Advanced Staging
    if (modified.UseAdvancedStaging != original.UseAdvancedStaging)
    {
        changes.Add("UseAdvancedStaging`"
                   + original.UseAdvancedStaging.ToString()
                   + "`"
                   + modified.UseAdvancedStaging.ToString().Replace("`", "-"));
    }

    // Attribute Set ID
    if (modified.AttributeSetID != original.AttributeSetID)
    {
        changes.Add("AttributeSetID`"
                   + original.AttributeSetID.ToString()
                   + "`"
                   + modified.AttributeSetID.ToString().Replace("`", "-"));
    }

    // Person ID Name
    if (modified.PersonIDName != original.PersonIDName)
    {
        changes.Add("PersonIDName`"
                   + original.PersonIDName.ToString()
                   + "`"
                   + modified.PersonIDName.ToString().Replace("`", "-"));
    }

    // Ready To Fulfill
    if (modified.ReadyToFulfill != original.ReadyToFulfill)
    {
        changes.Add("ReadyToFulfill`"
                   + original.ReadyToFulfill.ToString()
                   + "`"
                   + modified.ReadyToFulfill.ToString().Replace("`", "-"));
    }

    // FSM Send To
    if (modified.FSMSendTo != original.FSMSendTo)
    {
        changes.Add("FSMSendTo`"
                   + original.FSMSendTo.ToString()
                   + "`"
                   + modified.FSMSendTo.ToString().Replace("`", "-"));
    }

    // FSM Service Report ID
    if (modified.FSMServiceReportID != original.FSMServiceReportID)
    {
        changes.Add("FSMServiceReportID`"
                   + original.FSMServiceReportID.ToString()
                   + "`"
                   + modified.FSMServiceReportID.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Process Changes
    // =================================================================

    // If any changes were found, set the flag to call the function
    if (changes.Count > 0)
    {
        callFunc = true;

        // Join all changes with the delimiter
        string allChanges = string.Join("`~`", changes);
        int totalLength = allChanges.Length;
        int maxChunkSize = 1000;

        // Assign chunks based on position - each exactly 1000 chars (or remaining)
        if (totalLength > 0)
        {
            changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
        }
        if (totalLength > 1000)
        {
            changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
        }
        if (totalLength > 2000)
        {
            changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
        }
        if (totalLength > 3000)
        {
            changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
        }
        if (totalLength > 4000)
        {
            changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
        }
        if (totalLength > 5000)
        {
            changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
        }
        if (totalLength > 6000)
        {
            changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
        }
        if (totalLength > 7000)
        {
            changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
        }
        if (totalLength > 8000)
        {
            changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
        }
        if (totalLength > 9000)
        {
            changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
        }

        // Set appropriate debug message
        if (totalLength > 10000)
        {
            test2 = $"Warning: {totalLength} characters of changes detected, only first 10000 logged";
        }
        else
        {
            test2 = $"Debug: {changes.Count} changes detected, {totalLength} characters logged";
        }
    }
    else
    {
        test2 = "Debug: No changes detected";
    }
}
