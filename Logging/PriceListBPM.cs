// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;
PriceListCode = "";

// Validate dataset before processing
if (ds.PriceLst == null || ds.PriceLst.Count == 0)
{
    test1 = "Error: No price list data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.PriceLst.Count.ToString()} price list records";

// Track all changes in a list
List<string> changes = new List<string>();

// =================================================================
// Process PriceLst Changes
// =================================================================

// Get the modified price list record
var modifiedPriceLst = ds.PriceLst[0];
string listCode = modifiedPriceLst.ListCode;
PriceListCode = listCode;
test2 = "Debug: Successfully accessed ds.PriceLst[0]";

// Check if the price list row has been deleted (RowMod = "D" in original record)
if (ds.PriceLst[0].RowMod == "D")
{
    changes.Add("PriceLst deleted");
    callFunc = true;
}
else
{
    // Get the original price list record from database
    var originalPriceLst = (from dbPriceLst in Db.PriceLst
                           where dbPriceLst.Company == companyID
                              && dbPriceLst.ListCode.ToString() == listCode
                           select dbPriceLst).FirstOrDefault();

    // Handle new price list creation
    if (originalPriceLst == null)
    {
        changes.Add($"New price list created: {modifiedPriceLst.ListCode.ToString()}");
        callFunc = true;
    }
    else
    {
        // Determine which record to use as the modified version
        // Check if there's a second record (modified version)
        if (ds.PriceLst.Count > 1)
        {
            try
            {
                modifiedPriceLst = ds.PriceLst[1];
                test2 = "Debug: Successfully accessed ds.PriceLst[1]";
            }
            catch (System.Exception ex)
            {
                test2 = $"Error accessing ds.PriceLst[1]: {ex.Message}";
                return;
            }
        }
        else
        {
            test2 = "Debug: Using ds.PriceLst[0] as modified (only 1 record)";
        }

        // =================================================================
        // PriceLst Field Comparisons
        // =================================================================

        // Company
        if (modifiedPriceLst.Company != originalPriceLst.Company)
        {
            changes.Add("PriceLst.Company`"
                       + originalPriceLst.Company.ToString()
                       + "`"
                       + modifiedPriceLst.Company.ToString().Replace("`", "-"));
        }

        // List Code
        if (modifiedPriceLst.ListCode != originalPriceLst.ListCode)
        {
            changes.Add("PriceLst.ListCode`"
                       + originalPriceLst.ListCode.ToString()
                       + "`"
                       + modifiedPriceLst.ListCode.ToString().Replace("`", "-"));
        }

        // Currency Code
        if (modifiedPriceLst.CurrencyCode != originalPriceLst.CurrencyCode)
        {
            changes.Add("PriceLst.CurrencyCode`"
                       + originalPriceLst.CurrencyCode.ToString()
                       + "`"
                       + modifiedPriceLst.CurrencyCode.ToString().Replace("`", "-"));
        }

        // List Description
        if (modifiedPriceLst.ListDescription != originalPriceLst.ListDescription)
        {
            changes.Add("PriceLst.ListDescription`"
                       + originalPriceLst.ListDescription.ToString()
                       + "`"
                       + modifiedPriceLst.ListDescription.ToString().Replace("`", "-"));
        }

        // Start Date
        if (modifiedPriceLst.StartDate != originalPriceLst.StartDate)
        {
            changes.Add("PriceLst.StartDate`"
                       + originalPriceLst.StartDate.ToString()
                       + "`"
                       + modifiedPriceLst.StartDate.ToString().Replace("`", "-"));
        }

        // End Date
        if (modifiedPriceLst.EndDate != originalPriceLst.EndDate)
        {
            changes.Add("PriceLst.EndDate`"
                       + originalPriceLst.EndDate.ToString()
                       + "`"
                       + modifiedPriceLst.EndDate.ToString().Replace("`", "-"));
        }

        // Warehouse List
        if (modifiedPriceLst.WarehouseList != originalPriceLst.WarehouseList)
        {
            changes.Add("PriceLst.WarehouseList`"
                       + originalPriceLst.WarehouseList.ToString()
                       + "`"
                       + modifiedPriceLst.WarehouseList.ToString().Replace("`", "-"));
        }

        // Global Price List
        if (modifiedPriceLst.GlobalPriceLst != originalPriceLst.GlobalPriceLst)
        {
            changes.Add("PriceLst.GlobalPriceLst`"
                       + originalPriceLst.GlobalPriceLst.ToString()
                       + "`"
                       + modifiedPriceLst.GlobalPriceLst.ToString().Replace("`", "-"));
        }

        // Global Lock
        if (modifiedPriceLst.GlobalLock != originalPriceLst.GlobalLock)
        {
            changes.Add("PriceLst.GlobalLock`"
                       + originalPriceLst.GlobalLock.ToString()
                       + "`"
                       + modifiedPriceLst.GlobalLock.ToString().Replace("`", "-"));
        }

        // List Type
        if (modifiedPriceLst.ListType != originalPriceLst.ListType)
        {
            changes.Add("PriceLst.ListType`"
                       + originalPriceLst.ListType.ToString()
                       + "`"
                       + modifiedPriceLst.ListType.ToString().Replace("`", "-"));
        }

        // Use Zero Price
        if (modifiedPriceLst.UseZeroPrice != originalPriceLst.UseZeroPrice)
        {
            changes.Add("PriceLst.UseZeroPrice`"
                       + originalPriceLst.UseZeroPrice.ToString()
                       + "`"
                       + modifiedPriceLst.UseZeroPrice.ToString().Replace("`", "-"));
        }

        // FSM Send To
        if (modifiedPriceLst.FSMSendTo != originalPriceLst.FSMSendTo)
        {
            changes.Add("PriceLst.FSMSendTo`"
                       + originalPriceLst.FSMSendTo.ToString()
                       + "`"
                       + modifiedPriceLst.FSMSendTo.ToString().Replace("`", "-"));
        }
    }
}

// =================================================================
// Process PriceLstParts Changes
// =================================================================

// Check if PriceLstParts data exists
if (ds.PriceLstParts != null && ds.PriceLstParts.Count > 0)
{
    // Process each PriceLstParts record
    for (int i = 0; i < ds.PriceLstParts.Count; i++)
    {
        var modifiedPart = ds.PriceLstParts[i];
        string partNum = modifiedPart.PartNum.ToString();
        string partListCode = modifiedPart.ListCode.ToString();

        // Check if the part row has been deleted
        if (modifiedPart.RowMod == "D")
        {
            changes.Add($"PriceLstParts deleted: {partNum}");
            callFunc = true;
            continue;
        }

        // Get the original part record from database
        var originalPart = (from dbPart in Db.PriceLstParts
                           where dbPart.Company == companyID
                              && dbPart.ListCode.ToString() == partListCode
                              && dbPart.PartNum.ToString() == partNum
                           select dbPart).FirstOrDefault();

        // Handle new part creation
        if (originalPart == null)
        {
            changes.Add($"New price list part created: {partNum}");
            callFunc = true;
            continue;
        }

        // =================================================================
        // PriceLstParts Field Comparisons
        // =================================================================

        // Company
        if (modifiedPart.Company != originalPart.Company)
        {
            changes.Add($"PriceLstParts.Company[{partNum}]`"
                       + originalPart.Company.ToString()
                       + "`"
                       + modifiedPart.Company.ToString().Replace("`", "-"));
        }

        // List Code
        if (modifiedPart.ListCode != originalPart.ListCode)
        {
            changes.Add($"PriceLstParts.ListCode[{partNum}]`"
                       + originalPart.ListCode.ToString()
                       + "`"
                       + modifiedPart.ListCode.ToString().Replace("`", "-"));
        }

        // Part Number
        if (modifiedPart.PartNum != originalPart.PartNum)
        {
            changes.Add($"PriceLstParts.PartNum[{partNum}]`"
                       + originalPart.PartNum.ToString()
                       + "`"
                       + modifiedPart.PartNum.ToString().Replace("`", "-"));
        }

        // Base Price
        if (modifiedPart.BasePrice != originalPart.BasePrice)
        {
            changes.Add($"PriceLstParts.BasePrice[{partNum}]`"
                       + originalPart.BasePrice.ToString()
                       + "`"
                       + modifiedPart.BasePrice.ToString().Replace("`", "-"));
        }

        // Discount Percent 1
        if (modifiedPart.DiscountPercent1 != originalPart.DiscountPercent1)
        {
            changes.Add($"PriceLstParts.DiscountPercent1[{partNum}]`"
                       + originalPart.DiscountPercent1.ToString()
                       + "`"
                       + modifiedPart.DiscountPercent1.ToString().Replace("`", "-"));
        }

        // Discount Percent 2
        if (modifiedPart.DiscountPercent2 != originalPart.DiscountPercent2)
        {
            changes.Add($"PriceLstParts.DiscountPercent2[{partNum}]`"
                       + originalPart.DiscountPercent2.ToString()
                       + "`"
                       + modifiedPart.DiscountPercent2.ToString().Replace("`", "-"));
        }

        // Discount Percent 3
        if (modifiedPart.DiscountPercent3 != originalPart.DiscountPercent3)
        {
            changes.Add($"PriceLstParts.DiscountPercent3[{partNum}]`"
                       + originalPart.DiscountPercent3.ToString()
                       + "`"
                       + modifiedPart.DiscountPercent3.ToString().Replace("`", "-"));
        }

        // Discount Percent 4
        if (modifiedPart.DiscountPercent4 != originalPart.DiscountPercent4)
        {
            changes.Add($"PriceLstParts.DiscountPercent4[{partNum}]`"
                       + originalPart.DiscountPercent4.ToString()
                       + "`"
                       + modifiedPart.DiscountPercent4.ToString().Replace("`", "-"));
        }

        // Discount Percent 5
        if (modifiedPart.DiscountPercent5 != originalPart.DiscountPercent5)
        {
            changes.Add($"PriceLstParts.DiscountPercent5[{partNum}]`"
                       + originalPart.DiscountPercent5.ToString()
                       + "`"
                       + modifiedPart.DiscountPercent5.ToString().Replace("`", "-"));
        }

        // Quantity Break 1
        if (modifiedPart.QtyBreak1 != originalPart.QtyBreak1)
        {
            changes.Add($"PriceLstParts.QtyBreak1[{partNum}]`"
                       + originalPart.QtyBreak1.ToString()
                       + "`"
                       + modifiedPart.QtyBreak1.ToString().Replace("`", "-"));
        }

        // Quantity Break 2
        if (modifiedPart.QtyBreak2 != originalPart.QtyBreak2)
        {
            changes.Add($"PriceLstParts.QtyBreak2[{partNum}]`"
                       + originalPart.QtyBreak2.ToString()
                       + "`"
                       + modifiedPart.QtyBreak2.ToString().Replace("`", "-"));
        }

        // Quantity Break 3
        if (modifiedPart.QtyBreak3 != originalPart.QtyBreak3)
        {
            changes.Add($"PriceLstParts.QtyBreak3[{partNum}]`"
                       + originalPart.QtyBreak3.ToString()
                       + "`"
                       + modifiedPart.QtyBreak3.ToString().Replace("`", "-"));
        }

        // Quantity Break 4
        if (modifiedPart.QtyBreak4 != originalPart.QtyBreak4)
        {
            changes.Add($"PriceLstParts.QtyBreak4[{partNum}]`"
                       + originalPart.QtyBreak4.ToString()
                       + "`"
                       + modifiedPart.QtyBreak4.ToString().Replace("`", "-"));
        }

        // Quantity Break 5
        if (modifiedPart.QtyBreak5 != originalPart.QtyBreak5)
        {
            changes.Add($"PriceLstParts.QtyBreak5[{partNum}]`"
                       + originalPart.QtyBreak5.ToString()
                       + "`"
                       + modifiedPart.QtyBreak5.ToString().Replace("`", "-"));
        }

        // Unit Price 1
        if (modifiedPart.UnitPrice1 != originalPart.UnitPrice1)
        {
            changes.Add($"PriceLstParts.UnitPrice1[{partNum}]`"
                       + originalPart.UnitPrice1.ToString()
                       + "`"
                       + modifiedPart.UnitPrice1.ToString().Replace("`", "-"));
        }

        // Unit Price 2
        if (modifiedPart.UnitPrice2 != originalPart.UnitPrice2)
        {
            changes.Add($"PriceLstParts.UnitPrice2[{partNum}]`"
                       + originalPart.UnitPrice2.ToString()
                       + "`"
                       + modifiedPart.UnitPrice2.ToString().Replace("`", "-"));
        }

        // Unit Price 3
        if (modifiedPart.UnitPrice3 != originalPart.UnitPrice3)
        {
            changes.Add($"PriceLstParts.UnitPrice3[{partNum}]`"
                       + originalPart.UnitPrice3.ToString()
                       + "`"
                       + modifiedPart.UnitPrice3.ToString().Replace("`", "-"));
        }

        // Unit Price 4
        if (modifiedPart.UnitPrice4 != originalPart.UnitPrice4)
        {
            changes.Add($"PriceLstParts.UnitPrice4[{partNum}]`"
                       + originalPart.UnitPrice4.ToString()
                       + "`"
                       + modifiedPart.UnitPrice4.ToString().Replace("`", "-"));
        }

        // Unit Price 5
        if (modifiedPart.UnitPrice5 != originalPart.UnitPrice5)
        {
            changes.Add($"PriceLstParts.UnitPrice5[{partNum}]`"
                       + originalPart.UnitPrice5.ToString()
                       + "`"
                       + modifiedPart.UnitPrice5.ToString().Replace("`", "-"));
        }

        // Comment Text
        if (modifiedPart.CommentText != originalPart.CommentText)
        {
            changes.Add($"PriceLstParts.CommentText[{partNum}]`"
                       + originalPart.CommentText.ToString()
                       + "`"
                       + modifiedPart.CommentText.ToString().Replace("`", "-"));
        }

        // UOM Code
        if (modifiedPart.UOMCode != originalPart.UOMCode)
        {
            changes.Add($"PriceLstParts.UOMCode[{partNum}]`"
                       + originalPart.UOMCode.ToString()
                       + "`"
                       + modifiedPart.UOMCode.ToString().Replace("`", "-"));
        }

        // Global Price List Parts
        if (modifiedPart.GlobalPriceLstParts != originalPart.GlobalPriceLstParts)
        {
            changes.Add($"PriceLstParts.GlobalPriceLstParts[{partNum}]`"
                       + originalPart.GlobalPriceLstParts.ToString()
                       + "`"
                       + modifiedPart.GlobalPriceLstParts.ToString().Replace("`", "-"));
        }

        // Global Lock
        if (modifiedPart.GlobalLock != originalPart.GlobalLock)
        {
            changes.Add($"PriceLstParts.GlobalLock[{partNum}]`"
                       + originalPart.GlobalLock.ToString()
                       + "`"
                       + modifiedPart.GlobalLock.ToString().Replace("`", "-"));
        }
    }
}

// =================================================================
// Process Changes
// =================================================================

// If any changes were found, set the flag to call the function
if (changes.Count > 0)
{
    callFunc = true;

    // Join all changes with the delimiter
    string allChanges = string.Join("`~`", changes);
    int totalLength = allChanges.Length;
    int maxChunkSize = 1000;

    // Assign chunks based on position - each exactly 1000 chars (or remaining)
    if (totalLength > 0)
    {
        changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
    }
    if (totalLength > 1000)
    {
        changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
    }
    if (totalLength > 2000)
    {
        changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
    }
    if (totalLength > 3000)
    {
        changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
    }
    if (totalLength > 4000)
    {
        changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
    }
    if (totalLength > 5000)
    {
        changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
    }
    if (totalLength > 6000)
    {
        changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
    }
    if (totalLength > 7000)
    {
        changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
    }
    if (totalLength > 8000)
    {
        changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
    }
    if (totalLength > 9000)
    {
        changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
    }

    // Set appropriate debug message
    if (totalLength > 10000)
    {
        test2 = $"Warning: {totalLength} characters of changes detected, only first 10000 logged";
    }
    else
    {
        test2 = $"Debug: {changes.Count} changes detected, {totalLength} characters logged";
    }
}
else
{
    test2 = "Debug: No changes detected";
}
