result = "Error";

this.CallService<Ice.Contracts.UD03SvcContract>(svc =>
{
    try
    {
        // Init Keys
        string Key1 = "", Key4 = "", Key5 = "";
        var ts = new Ice.Tablesets.UD03Tableset();
        Ice.Tablesets.UD03Row row;

        try
        {
            // Try to get existing
			// Use userID (short) + "_" + current UTC ticks (as string) truncated to fit 50 chars
			string userIdStr = Session.UserID.ToString(); // assuming it's short (like 5-10 chars)
			string ticksStr = DateTime.UtcNow.Ticks.ToString();

			// Combine and truncate if needed
			string combinedKey = userIdStr + "_" + ticksStr;

			if (combinedKey.Length > 50)
				combinedKey = combinedKey.Substring(0, 50);

			Key1 = combinedKey;

            ts = svc.GetByID(Key1, Key2, Key3, Key4, Key5);

            // Check if UD03 collection has any records
            if (ts.UD03 == null || ts.UD03.Count == 0)
            {
                result = "Error: No UD03 records found in GetByID";
                return;
            }

            row = ts.UD03[0];
            row.RowMod = "U";
        }
        catch
        {
            // Record not found: create new
            svc.GetaNewUD03(ref ts);

            // Check if UD03 collection was created properly
            if (ts.UD03 == null || ts.UD03.Count == 0)
            {
                result = "Error: Failed to create new UD03 record";
                return;
            }

            row = ts.UD03[0];
            row.Key1 = Key1;
            row.Key2 = Key2;
            row.Key3 = Key3;
            row.Key4 = Key4;
            row.Key5 = Key5;
            row.RowMod = "A";
        }
		
        // Combine all changesMade fields into one string
        string allChangesData = "";
        allChangesData += (changesMade ?? "");
        allChangesData += (changesMade2 ?? "");
        allChangesData += (changesMade3 ?? "");
        allChangesData += (changesMade4 ?? "");
        allChangesData += (changesMade5 ?? "");
        allChangesData += (changesMade6 ?? "");
        allChangesData += (changesMade7 ?? "");
        allChangesData += (changesMade8 ?? "");
        allChangesData += (changesMade9 ?? "");
        allChangesData += (changesMade10 ?? "");

        // Parse the changes data into structured format
        var changesList = new List<Dictionary<string, string>>();

        if (!string.IsNullOrEmpty(allChangesData))
        {
            // Split by `~` to get individual changes
            string[] changes = allChangesData.Split(new string[] { "`~`" }, StringSplitOptions.RemoveEmptyEntries);

            foreach (string change in changes)
            {
                // Split by ` to get field, oldValue, newValue
                string[] parts = change.Split('`');

                if (parts.Length >= 3)
                {
                    var changeRecord = new Dictionary<string, string>
                    {
                        ["Field"] = parts[0],
                        ["OldValue"] = parts[1],
                        ["NewValue"] = parts[2]
                    };
                    changesList.Add(changeRecord);
                }
                else if (parts.Length == 2)
                {
                    // Handle case where there might be only field and new value
                    var changeRecord = new Dictionary<string, string>
                    {
                        ["Field"] = parts[0],
                        ["OldValue"] = "",
                        ["NewValue"] = parts[1]
                    };
                    changesList.Add(changeRecord);
                }
            }
        }

        // Create a summary of changes for storage
        string changesSummary = "";
        // Check if this is a new quote creation
		if ((changesMade.Contains("New", StringComparison.OrdinalIgnoreCase) && changesMade.Contains("created", StringComparison.OrdinalIgnoreCase)) || changesMade.Contains("deleted", StringComparison.OrdinalIgnoreCase))
		{
		    changesSummary = changesMade;
		}
		else
		{
		    foreach (var change in changesList)
		    {
		        changesSummary += $"Field: {change["Field"]}, From: {change["OldValue"]}, To: {change["NewValue"]}\n";
		    }
		}

        // Assign values
        row.Company = companyID;

		// Store the original chunked data in Character fields (for backup/reference)
		row.Character01 = (changesMade ?? "").Replace("'", "").Replace("\"", "");
		row.Character02 = (changesMade2 ?? "").Replace("'", "").Replace("\"", "");
		row.Character03 = (changesMade3 ?? "").Replace("'", "").Replace("\"", "");
		row.Character04 = (changesMade4 ?? "").Replace("'", "").Replace("\"", "");
		row.Character05 = (changesMade5 ?? "").Replace("'", "").Replace("\"", "");
		row.Character06 = (changesMade6 ?? "").Replace("'", "").Replace("\"", "");
		row.Character07 = (changesMade7 ?? "").Replace("'", "").Replace("\"", "");
		row.Character08 = (changesMade8 ?? "").Replace("'", "").Replace("\"", "");
		row.Character09 = (changesMade9 ?? "").Replace("'", "").Replace("\"", "");
		row.Character10 = (changesMade10 ?? "").Replace("'", "").Replace("\"", "");

		// Store parsed summary (first 1000 characters only)
		if (!string.IsNullOrEmpty(changesSummary))
		{
		    row.Character10 = changesSummary.Substring(0, Math.Min(1000, changesSummary.Length));
		}

		// Store count of changes
		row.Number01 = changesList.Count;
		row.Number02 = 0;
		row.Number03 = 0;
		row.Number04 = 0;
		row.Number05 = 0;
		row.Number06 = 0;
		row.Number07 = 0;
		row.Number08 = 0;
		row.Number09 = 0;
		row.Number10 = 0;
		row.Number11 = 0;
		row.Number12 = 0;
		row.Number13 = 0;
		row.Number14 = 0;
		row.Number15 = 0;
		row.Number16 = 0;
		row.Number17 = 0;
		row.Number18 = 0;
		row.Number19 = 0;
		row.Number20 = 0;
		
		row.CheckBox01 = false;
		row.CheckBox02 = false;
		row.CheckBox03 = false;
		row.CheckBox04 = false;
		row.CheckBox05 = false;
		row.CheckBox06 = false;
		row.CheckBox07 = false;
		row.CheckBox08 = false;
		row.CheckBox09 = false;
		row.CheckBox10 = false;
		row.CheckBox11 = false;
		row.CheckBox12 = false;
		row.CheckBox13 = false;
		row.CheckBox14 = false;
		row.CheckBox15 = false;
		row.CheckBox16 = false;
		row.CheckBox17 = false;
		row.CheckBox18 = false;
		row.CheckBox19 = false;
		row.CheckBox20 = false;
		
		row.ShortChar01 = Session.UserID.ToString();
		row.ShortChar02 = currentPlant;
		row.ShortChar03 = DateTime.UtcNow.Ticks.ToString();
		row.ShortChar04 = "";
		row.ShortChar05 = "";
		row.ShortChar06 = "";
		row.ShortChar07 = "";
		row.ShortChar08 = "";
		row.ShortChar09 = "";
		row.ShortChar10 = "";
		row.ShortChar11 = "";
		row.ShortChar12 = "";
		row.ShortChar13 = "";
		row.ShortChar14 = "";
		row.ShortChar15 = "";
		row.ShortChar16 = "";
		row.ShortChar17 = "";
		row.ShortChar18 = "";
		row.ShortChar19 = "";
		row.ShortChar20 = "";
		
		row.Date01 = DateTime.Now;


        // Save changes
        svc.Update(ref ts);

        result = "OK";
    }
    catch (Exception ex)
    {
        result = "Error: " + ex.Message;
    }
});