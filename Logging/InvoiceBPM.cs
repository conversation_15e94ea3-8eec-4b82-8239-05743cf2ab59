// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;
InvoiceNumber = "";

// Validate dataset before processing
if (ds.InvcHead == null || ds.InvcHead.Count == 0)
{
    test1 = "Error: No invoice data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.InvcHead.Count.ToString()} invoice records";

// Track all changes in a list
List<string> changes = new List<string>();

// =================================================================
// Process InvcHead Changes
// =================================================================

// Get the modified invoice record
var modifiedInvcHead = ds.InvcHead[0];
int invoiceNum = modifiedInvcHead.InvoiceNum;
InvoiceNumber = invoiceNum.ToString();
test2 = "Debug: Successfully accessed ds.InvcHead[0]";

// Check if the invoice row has been deleted (RowMod = "D" in original record)
if (ds.InvcHead[0].RowMod == "D")
{
    changes.Add("InvcHead deleted");
    callFunc = true;
}
else
{
    // Get the original invoice record from database
    var originalInvcHead = (from dbInvcHead in Db.InvcHead
                           where dbInvcHead.Company == companyID
                              && dbInvcHead.InvoiceNum == invoiceNum
                           select dbInvcHead).FirstOrDefault();

    // Handle new invoice creation
    if (originalInvcHead == null)
    {
        changes.Add($"New invoice created: {modifiedInvcHead.InvoiceNum.ToString()}");
        callFunc = true;
    }
    else
    {
        // Determine which record to use as the modified version
        // Check if there's a second record (modified version)
        if (ds.InvcHead.Count > 1)
        {
            try
            {
                modifiedInvcHead = ds.InvcHead[1];
                test2 = "Debug: Successfully accessed ds.InvcHead[1]";
            }
            catch (System.Exception ex)
            {
                test2 = $"Error accessing ds.InvcHead[1]: {ex.Message}";
                return;
            }
        }
        else
        {
            test2 = "Debug: Using ds.InvcHead[0] as modified (only 1 record)";
        }

        // =================================================================
        // InvcHead Field Comparisons
        // =================================================================

        // Company
        if (modifiedInvcHead.Company != originalInvcHead.Company)
        {
            changes.Add("InvcHead.Company`"
                       + originalInvcHead.Company.ToString()
                       + "`"
                       + modifiedInvcHead.Company.ToString().Replace("`", "-"));
        }

        // Open Invoice
        if (modifiedInvcHead.OpenInvoice != originalInvcHead.OpenInvoice)
        {
            changes.Add("InvcHead.OpenInvoice`"
                       + originalInvcHead.OpenInvoice.ToString()
                       + "`"
                       + modifiedInvcHead.OpenInvoice.ToString().Replace("`", "-"));
        }

        // Closed Date
        if (modifiedInvcHead.ClosedDate != originalInvcHead.ClosedDate)
        {
            changes.Add("InvcHead.ClosedDate`"
                       + originalInvcHead.ClosedDate.ToString()
                       + "`"
                       + modifiedInvcHead.ClosedDate.ToString().Replace("`", "-"));
        }

        // Credit Memo
        if (modifiedInvcHead.CreditMemo != originalInvcHead.CreditMemo)
        {
            changes.Add("InvcHead.CreditMemo`"
                       + originalInvcHead.CreditMemo.ToString()
                       + "`"
                       + modifiedInvcHead.CreditMemo.ToString().Replace("`", "-"));
        }

        // Unapplied Cash
        if (modifiedInvcHead.UnappliedCash != originalInvcHead.UnappliedCash)
        {
            changes.Add("InvcHead.UnappliedCash`"
                       + originalInvcHead.UnappliedCash.ToString()
                       + "`"
                       + modifiedInvcHead.UnappliedCash.ToString().Replace("`", "-"));
        }

        // Check Reference
        if (modifiedInvcHead.CheckRef != originalInvcHead.CheckRef)
        {
            changes.Add("InvcHead.CheckRef`"
                       + originalInvcHead.CheckRef.ToString()
                       + "`"
                       + modifiedInvcHead.CheckRef.ToString().Replace("`", "-"));
        }

        // Invoice Suffix
        if (modifiedInvcHead.InvoiceSuffix != originalInvcHead.InvoiceSuffix)
        {
            changes.Add("InvcHead.InvoiceSuffix`"
                       + originalInvcHead.InvoiceSuffix.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceSuffix.ToString().Replace("`", "-"));
        }

        // Group ID
        if (modifiedInvcHead.GroupID != originalInvcHead.GroupID)
        {
            changes.Add("InvcHead.GroupID`"
                       + originalInvcHead.GroupID.ToString()
                       + "`"
                       + modifiedInvcHead.GroupID.ToString().Replace("`", "-"));
        }

        // Posted
        if (modifiedInvcHead.Posted != originalInvcHead.Posted)
        {
            changes.Add("InvcHead.Posted`"
                       + originalInvcHead.Posted.ToString()
                       + "`"
                       + modifiedInvcHead.Posted.ToString().Replace("`", "-"));
        }

        // Invoice Number
        if (modifiedInvcHead.InvoiceNum != originalInvcHead.InvoiceNum)
        {
            changes.Add("InvcHead.InvoiceNum`"
                       + originalInvcHead.InvoiceNum.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceNum.ToString().Replace("`", "-"));
        }

        // Invoice Type
        if (modifiedInvcHead.InvoiceType != originalInvcHead.InvoiceType)
        {
            changes.Add("InvcHead.InvoiceType`"
                       + originalInvcHead.InvoiceType.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceType.ToString().Replace("`", "-"));
        }

        // Deferred Revenue
        if (modifiedInvcHead.DeferredRevenue != originalInvcHead.DeferredRevenue)
        {
            changes.Add("InvcHead.DeferredRevenue`"
                       + originalInvcHead.DeferredRevenue.ToString()
                       + "`"
                       + modifiedInvcHead.DeferredRevenue.ToString().Replace("`", "-"));
        }

        // Order Number
        if (modifiedInvcHead.OrderNum != originalInvcHead.OrderNum)
        {
            changes.Add("InvcHead.OrderNum`"
                       + originalInvcHead.OrderNum.ToString()
                       + "`"
                       + modifiedInvcHead.OrderNum.ToString().Replace("`", "-"));
        }

        // Customer Number
        if (modifiedInvcHead.CustNum != originalInvcHead.CustNum)
        {
            changes.Add("InvcHead.CustNum`"
                       + originalInvcHead.CustNum.ToString()
                       + "`"
                       + modifiedInvcHead.CustNum.ToString().Replace("`", "-"));
        }

        // PO Number
        if (modifiedInvcHead.PONum != originalInvcHead.PONum)
        {
            changes.Add("InvcHead.PONum`"
                       + originalInvcHead.PONum.ToString()
                       + "`"
                       + modifiedInvcHead.PONum.ToString().Replace("`", "-"));
        }

        // Entry Person
        if (modifiedInvcHead.EntryPerson != originalInvcHead.EntryPerson)
        {
            changes.Add("InvcHead.EntryPerson`"
                       + originalInvcHead.EntryPerson.ToString()
                       + "`"
                       + modifiedInvcHead.EntryPerson.ToString().Replace("`", "-"));
        }

        // FOB
        if (modifiedInvcHead.FOB != originalInvcHead.FOB)
        {
            changes.Add("InvcHead.FOB`"
                       + originalInvcHead.FOB.ToString()
                       + "`"
                       + modifiedInvcHead.FOB.ToString().Replace("`", "-"));
        }

        // Terms Code
        if (modifiedInvcHead.TermsCode != originalInvcHead.TermsCode)
        {
            changes.Add("InvcHead.TermsCode`"
                       + originalInvcHead.TermsCode.ToString()
                       + "`"
                       + modifiedInvcHead.TermsCode.ToString().Replace("`", "-"));
        }

        // Invoice Date
        if (modifiedInvcHead.InvoiceDate != originalInvcHead.InvoiceDate)
        {
            changes.Add("InvcHead.InvoiceDate`"
                       + originalInvcHead.InvoiceDate.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceDate.ToString().Replace("`", "-"));
        }

        // Due Date
        if (modifiedInvcHead.DueDate != originalInvcHead.DueDate)
        {
            changes.Add("InvcHead.DueDate`"
                       + originalInvcHead.DueDate.ToString()
                       + "`"
                       + modifiedInvcHead.DueDate.ToString().Replace("`", "-"));
        }

        // Fiscal Year
        if (modifiedInvcHead.FiscalYear != originalInvcHead.FiscalYear)
        {
            changes.Add("InvcHead.FiscalYear`"
                       + originalInvcHead.FiscalYear.ToString()
                       + "`"
                       + modifiedInvcHead.FiscalYear.ToString().Replace("`", "-"));
        }

        // Fiscal Period
        if (modifiedInvcHead.FiscalPeriod != originalInvcHead.FiscalPeriod)
        {
            changes.Add("InvcHead.FiscalPeriod`"
                       + originalInvcHead.FiscalPeriod.ToString()
                       + "`"
                       + modifiedInvcHead.FiscalPeriod.ToString().Replace("`", "-"));
        }

        // GL Posted
        if (modifiedInvcHead.GLPosted != originalInvcHead.GLPosted)
        {
            changes.Add("InvcHead.GLPosted`"
                       + originalInvcHead.GLPosted.ToString()
                       + "`"
                       + modifiedInvcHead.GLPosted.ToString().Replace("`", "-"));
        }

        // Invoice Comment
        if (modifiedInvcHead.InvoiceComment != originalInvcHead.InvoiceComment)
        {
            changes.Add("InvcHead.InvoiceComment`"
                       + originalInvcHead.InvoiceComment.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceComment.ToString().Replace("`", "-"));
        }

        // Invoice Amount
        if (modifiedInvcHead.InvoiceAmt != originalInvcHead.InvoiceAmt)
        {
            changes.Add("InvcHead.InvoiceAmt`"
                       + originalInvcHead.InvoiceAmt.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Doc Invoice Amount
        if (modifiedInvcHead.DocInvoiceAmt != originalInvcHead.DocInvoiceAmt)
        {
            changes.Add("InvcHead.DocInvoiceAmt`"
                       + originalInvcHead.DocInvoiceAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Invoice Balance
        if (modifiedInvcHead.InvoiceBal != originalInvcHead.InvoiceBal)
        {
            changes.Add("InvcHead.InvoiceBal`"
                       + originalInvcHead.InvoiceBal.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceBal.ToString().Replace("`", "-"));
        }

        // Doc Invoice Balance
        if (modifiedInvcHead.DocInvoiceBal != originalInvcHead.DocInvoiceBal)
        {
            changes.Add("InvcHead.DocInvoiceBal`"
                       + originalInvcHead.DocInvoiceBal.ToString()
                       + "`"
                       + modifiedInvcHead.DocInvoiceBal.ToString().Replace("`", "-"));
        }

        // Unposted Balance
        if (modifiedInvcHead.UnpostedBal != originalInvcHead.UnpostedBal)
        {
            changes.Add("InvcHead.UnpostedBal`"
                       + originalInvcHead.UnpostedBal.ToString()
                       + "`"
                       + modifiedInvcHead.UnpostedBal.ToString().Replace("`", "-"));
        }

        // Doc Unposted Balance
        if (modifiedInvcHead.DocUnpostedBal != originalInvcHead.DocUnpostedBal)
        {
            changes.Add("InvcHead.DocUnpostedBal`"
                       + originalInvcHead.DocUnpostedBal.ToString()
                       + "`"
                       + modifiedInvcHead.DocUnpostedBal.ToString().Replace("`", "-"));
        }

        // Deposit Credit
        if (modifiedInvcHead.DepositCredit != originalInvcHead.DepositCredit)
        {
            changes.Add("InvcHead.DepositCredit`"
                       + originalInvcHead.DepositCredit.ToString()
                       + "`"
                       + modifiedInvcHead.DepositCredit.ToString().Replace("`", "-"));
        }

        // Doc Deposit Credit
        if (modifiedInvcHead.DocDepositCredit != originalInvcHead.DocDepositCredit)
        {
            changes.Add("InvcHead.DocDepositCredit`"
                       + originalInvcHead.DocDepositCredit.ToString()
                       + "`"
                       + modifiedInvcHead.DocDepositCredit.ToString().Replace("`", "-"));
        }

        // Sales Rep List
        if (modifiedInvcHead.SalesRepList != originalInvcHead.SalesRepList)
        {
            changes.Add("InvcHead.SalesRepList`"
                       + originalInvcHead.SalesRepList.ToString()
                       + "`"
                       + modifiedInvcHead.SalesRepList.ToString().Replace("`", "-"));
        }

        // Invoice Reference
        if (modifiedInvcHead.InvoiceRef != originalInvcHead.InvoiceRef)
        {
            changes.Add("InvcHead.InvoiceRef`"
                       + originalInvcHead.InvoiceRef.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceRef.ToString().Replace("`", "-"));
        }

        // Reference Cancelled
        if (modifiedInvcHead.RefCancelled != originalInvcHead.RefCancelled)
        {
            changes.Add("InvcHead.RefCancelled`"
                       + originalInvcHead.RefCancelled.ToString()
                       + "`"
                       + modifiedInvcHead.RefCancelled.ToString().Replace("`", "-"));
        }

        // Reference Cancelled By
        if (modifiedInvcHead.RefCancelledBy != originalInvcHead.RefCancelledBy)
        {
            changes.Add("InvcHead.RefCancelledBy`"
                       + originalInvcHead.RefCancelledBy.ToString()
                       + "`"
                       + modifiedInvcHead.RefCancelledBy.ToString().Replace("`", "-"));
        }

        // Start Up
        if (modifiedInvcHead.StartUp != originalInvcHead.StartUp)
        {
            changes.Add("InvcHead.StartUp`"
                       + originalInvcHead.StartUp.ToString()
                       + "`"
                       + modifiedInvcHead.StartUp.ToString().Replace("`", "-"));
        }

        // Pay Dates
        if (modifiedInvcHead.PayDates != originalInvcHead.PayDates)
        {
            changes.Add("InvcHead.PayDates`"
                       + originalInvcHead.PayDates.ToString()
                       + "`"
                       + modifiedInvcHead.PayDates.ToString().Replace("`", "-"));
        }

        // Pay Amounts
        if (modifiedInvcHead.PayAmounts != originalInvcHead.PayAmounts)
        {
            changes.Add("InvcHead.PayAmounts`"
                       + originalInvcHead.PayAmounts.ToString()
                       + "`"
                       + modifiedInvcHead.PayAmounts.ToString().Replace("`", "-"));
        }

        // Doc Pay Amounts
        if (modifiedInvcHead.DocPayAmounts != originalInvcHead.DocPayAmounts)
        {
            changes.Add("InvcHead.DocPayAmounts`"
                       + originalInvcHead.DocPayAmounts.ToString()
                       + "`"
                       + modifiedInvcHead.DocPayAmounts.ToString().Replace("`", "-"));
        }

        // Pay Discount Date
        if (modifiedInvcHead.PayDiscDate != originalInvcHead.PayDiscDate)
        {
            changes.Add("InvcHead.PayDiscDate`"
                       + originalInvcHead.PayDiscDate.ToString()
                       + "`"
                       + modifiedInvcHead.PayDiscDate.ToString().Replace("`", "-"));
        }

        // Pay Discount Amount
        if (modifiedInvcHead.PayDiscAmt != originalInvcHead.PayDiscAmt)
        {
            changes.Add("InvcHead.PayDiscAmt`"
                       + originalInvcHead.PayDiscAmt.ToString()
                       + "`"
                       + modifiedInvcHead.PayDiscAmt.ToString().Replace("`", "-"));
        }

        // Doc Pay Discount Amount
        if (modifiedInvcHead.DocPayDiscAmt != originalInvcHead.DocPayDiscAmt)
        {
            changes.Add("InvcHead.DocPayDiscAmt`"
                       + originalInvcHead.DocPayDiscAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocPayDiscAmt.ToString().Replace("`", "-"));
        }

        // Bill Contract Number
        if (modifiedInvcHead.BillConNum != originalInvcHead.BillConNum)
        {
            changes.Add("InvcHead.BillConNum`"
                       + originalInvcHead.BillConNum.ToString()
                       + "`"
                       + modifiedInvcHead.BillConNum.ToString().Replace("`", "-"));
        }

        // Invoice Held
        if (modifiedInvcHead.InvoiceHeld != originalInvcHead.InvoiceHeld)
        {
            changes.Add("InvcHead.InvoiceHeld`"
                       + originalInvcHead.InvoiceHeld.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceHeld.ToString().Replace("`", "-"));
        }

        // Currency Code
        if (modifiedInvcHead.CurrencyCode != originalInvcHead.CurrencyCode)
        {
            changes.Add("InvcHead.CurrencyCode`"
                       + originalInvcHead.CurrencyCode.ToString()
                       + "`"
                       + modifiedInvcHead.CurrencyCode.ToString().Replace("`", "-"));
        }

        // Exchange Rate
        if (modifiedInvcHead.ExchangeRate != originalInvcHead.ExchangeRate)
        {
            changes.Add("InvcHead.ExchangeRate`"
                       + originalInvcHead.ExchangeRate.ToString()
                       + "`"
                       + modifiedInvcHead.ExchangeRate.ToString().Replace("`", "-"));
        }

        // Lock Rate
        if (modifiedInvcHead.LockRate != originalInvcHead.LockRate)
        {
            changes.Add("InvcHead.LockRate`"
                       + originalInvcHead.LockRate.ToString()
                       + "`"
                       + modifiedInvcHead.LockRate.ToString().Replace("`", "-"));
        }

        // Journal Number
        if (modifiedInvcHead.JournalNum != originalInvcHead.JournalNum)
        {
            changes.Add("InvcHead.JournalNum`"
                       + originalInvcHead.JournalNum.ToString()
                       + "`"
                       + modifiedInvcHead.JournalNum.ToString().Replace("`", "-"));
        }

        // Journal Code
        if (modifiedInvcHead.JournalCode != originalInvcHead.JournalCode)
        {
            changes.Add("InvcHead.JournalCode`"
                       + originalInvcHead.JournalCode.ToString()
                       + "`"
                       + modifiedInvcHead.JournalCode.ToString().Replace("`", "-"));
        }

        // Line Type
        if (modifiedInvcHead.LineType != originalInvcHead.LineType)
        {
            changes.Add("InvcHead.LineType`"
                       + originalInvcHead.LineType.ToString()
                       + "`"
                       + modifiedInvcHead.LineType.ToString().Replace("`", "-"));
        }

        // RMA Number
        if (modifiedInvcHead.RMANum != originalInvcHead.RMANum)
        {
            changes.Add("InvcHead.RMANum`"
                       + originalInvcHead.RMANum.ToString()
                       + "`"
                       + modifiedInvcHead.RMANum.ToString().Replace("`", "-"));
        }

        // Plant
        if (modifiedInvcHead.Plant != originalInvcHead.Plant)
        {
            changes.Add("InvcHead.Plant`"
                       + originalInvcHead.Plant.ToString()
                       + "`"
                       + modifiedInvcHead.Plant.ToString().Replace("`", "-"));
        }

        // Card Member Name
        if (modifiedInvcHead.CardMemberName != originalInvcHead.CardMemberName)
        {
            changes.Add("InvcHead.CardMemberName`"
                       + originalInvcHead.CardMemberName.ToString()
                       + "`"
                       + modifiedInvcHead.CardMemberName.ToString().Replace("`", "-"));
        }

        // Card Number
        if (modifiedInvcHead.CardNumber != originalInvcHead.CardNumber)
        {
            changes.Add("InvcHead.CardNumber`"
                       + originalInvcHead.CardNumber.ToString()
                       + "`"
                       + modifiedInvcHead.CardNumber.ToString().Replace("`", "-"));
        }

        // Card Type
        if (modifiedInvcHead.CardType != originalInvcHead.CardType)
        {
            changes.Add("InvcHead.CardType`"
                       + originalInvcHead.CardType.ToString()
                       + "`"
                       + modifiedInvcHead.CardType.ToString().Replace("`", "-"));
        }

        // Expiration Month
        if (modifiedInvcHead.ExpirationMonth != originalInvcHead.ExpirationMonth)
        {
            changes.Add("InvcHead.ExpirationMonth`"
                       + originalInvcHead.ExpirationMonth.ToString()
                       + "`"
                       + modifiedInvcHead.ExpirationMonth.ToString().Replace("`", "-"));
        }

        // Expiration Year
        if (modifiedInvcHead.ExpirationYear != originalInvcHead.ExpirationYear)
        {
            changes.Add("InvcHead.ExpirationYear`"
                       + originalInvcHead.ExpirationYear.ToString()
                       + "`"
                       + modifiedInvcHead.ExpirationYear.ToString().Replace("`", "-"));
        }

        // Card ID
        if (modifiedInvcHead.CardID != originalInvcHead.CardID)
        {
            changes.Add("InvcHead.CardID`"
                       + originalInvcHead.CardID.ToString()
                       + "`"
                       + modifiedInvcHead.CardID.ToString().Replace("`", "-"));
        }

        // Card Member Reference
        if (modifiedInvcHead.CardmemberReference != originalInvcHead.CardmemberReference)
        {
            changes.Add("InvcHead.CardmemberReference`"
                       + originalInvcHead.CardmemberReference.ToString()
                       + "`"
                       + modifiedInvcHead.CardmemberReference.ToString().Replace("`", "-"));
        }

        // Legal Number
        if (modifiedInvcHead.LegalNumber != originalInvcHead.LegalNumber)
        {
            changes.Add("InvcHead.LegalNumber`"
                       + originalInvcHead.LegalNumber.ToString()
                       + "`"
                       + modifiedInvcHead.LegalNumber.ToString().Replace("`", "-"));
        }

        // External ID
        if (modifiedInvcHead.ExternalID != originalInvcHead.ExternalID)
        {
            changes.Add("InvcHead.ExternalID`"
                       + originalInvcHead.ExternalID.ToString()
                       + "`"
                       + modifiedInvcHead.ExternalID.ToString().Replace("`", "-"));
        }

        // Cross Reference Invoice Number
        if (modifiedInvcHead.XRefInvoiceNum != originalInvcHead.XRefInvoiceNum)
        {
            changes.Add("InvcHead.XRefInvoiceNum`"
                       + originalInvcHead.XRefInvoiceNum.ToString()
                       + "`"
                       + modifiedInvcHead.XRefInvoiceNum.ToString().Replace("`", "-"));
        }

        // Deposit Gain Loss
        if (modifiedInvcHead.DepGainLoss != originalInvcHead.DepGainLoss)
        {
            changes.Add("InvcHead.DepGainLoss`"
                       + originalInvcHead.DepGainLoss.ToString()
                       + "`"
                       + modifiedInvcHead.DepGainLoss.ToString().Replace("`", "-"));
        }

        // DN Comments
        if (modifiedInvcHead.DNComments != originalInvcHead.DNComments)
        {
            changes.Add("InvcHead.DNComments`"
                       + originalInvcHead.DNComments.ToString()
                       + "`"
                       + modifiedInvcHead.DNComments.ToString().Replace("`", "-"));
        }

        // DN Customer Number
        if (modifiedInvcHead.DNCustNbr != originalInvcHead.DNCustNbr)
        {
            changes.Add("InvcHead.DNCustNbr`"
                       + originalInvcHead.DNCustNbr.ToString()
                       + "`"
                       + modifiedInvcHead.DNCustNbr.ToString().Replace("`", "-"));
        }

        // Debit Note
        if (modifiedInvcHead.DebitNote != originalInvcHead.DebitNote)
        {
            changes.Add("InvcHead.DebitNote`"
                       + originalInvcHead.DebitNote.ToString()
                       + "`"
                       + modifiedInvcHead.DebitNote.ToString().Replace("`", "-"));
        }

        // Sold To Customer Number
        if (modifiedInvcHead.SoldToCustNum != originalInvcHead.SoldToCustNum)
        {
            changes.Add("InvcHead.SoldToCustNum`"
                       + originalInvcHead.SoldToCustNum.ToString()
                       + "`"
                       + modifiedInvcHead.SoldToCustNum.ToString().Replace("`", "-"));
        }

        // Consolidated
        if (modifiedInvcHead.Consolidated != originalInvcHead.Consolidated)
        {
            changes.Add("InvcHead.Consolidated`"
                       + originalInvcHead.Consolidated.ToString()
                       + "`"
                       + modifiedInvcHead.Consolidated.ToString().Replace("`", "-"));
        }

        // Bill To Invoice Address
        if (modifiedInvcHead.BillToInvoiceAddress != originalInvcHead.BillToInvoiceAddress)
        {
            changes.Add("InvcHead.BillToInvoiceAddress`"
                       + originalInvcHead.BillToInvoiceAddress.ToString()
                       + "`"
                       + modifiedInvcHead.BillToInvoiceAddress.ToString().Replace("`", "-"));
        }

        // Sold To Invoice Address
        if (modifiedInvcHead.SoldToInvoiceAddress != originalInvcHead.SoldToInvoiceAddress)
        {
            changes.Add("InvcHead.SoldToInvoiceAddress`"
                       + originalInvcHead.SoldToInvoiceAddress.ToString()
                       + "`"
                       + modifiedInvcHead.SoldToInvoiceAddress.ToString().Replace("`", "-"));
        }

        // Process Card
        if (modifiedInvcHead.ProcessCard != originalInvcHead.ProcessCard)
        {
            changes.Add("InvcHead.ProcessCard`"
                       + originalInvcHead.ProcessCard.ToString()
                       + "`"
                       + modifiedInvcHead.ProcessCard.ToString().Replace("`", "-"));
        }

        // Rep Commission 1
        if (modifiedInvcHead.RepComm1 != originalInvcHead.RepComm1)
        {
            changes.Add("InvcHead.RepComm1`"
                       + originalInvcHead.RepComm1.ToString()
                       + "`"
                       + modifiedInvcHead.RepComm1.ToString().Replace("`", "-"));
        }

        // Rep Commission 2
        if (modifiedInvcHead.RepComm2 != originalInvcHead.RepComm2)
        {
            changes.Add("InvcHead.RepComm2`"
                       + originalInvcHead.RepComm2.ToString()
                       + "`"
                       + modifiedInvcHead.RepComm2.ToString().Replace("`", "-"));
        }

        // Rep Commission 3
        if (modifiedInvcHead.RepComm3 != originalInvcHead.RepComm3)
        {
            changes.Add("InvcHead.RepComm3`"
                       + originalInvcHead.RepComm3.ToString()
                       + "`"
                       + modifiedInvcHead.RepComm3.ToString().Replace("`", "-"));
        }

        // Rep Commission 4
        if (modifiedInvcHead.RepComm4 != originalInvcHead.RepComm4)
        {
            changes.Add("InvcHead.RepComm4`"
                       + originalInvcHead.RepComm4.ToString()
                       + "`"
                       + modifiedInvcHead.RepComm4.ToString().Replace("`", "-"));
        }

        // Rep Commission 5
        if (modifiedInvcHead.RepComm5 != originalInvcHead.RepComm5)
        {
            changes.Add("InvcHead.RepComm5`"
                       + originalInvcHead.RepComm5.ToString()
                       + "`"
                       + modifiedInvcHead.RepComm5.ToString().Replace("`", "-"));
        }

        // Rep Rate 1
        if (modifiedInvcHead.RepRate1 != originalInvcHead.RepRate1)
        {
            changes.Add("InvcHead.RepRate1`"
                       + originalInvcHead.RepRate1.ToString()
                       + "`"
                       + modifiedInvcHead.RepRate1.ToString().Replace("`", "-"));
        }

        // Rep Rate 2
        if (modifiedInvcHead.RepRate2 != originalInvcHead.RepRate2)
        {
            changes.Add("InvcHead.RepRate2`"
                       + originalInvcHead.RepRate2.ToString()
                       + "`"
                       + modifiedInvcHead.RepRate2.ToString().Replace("`", "-"));
        }

        // Rep Rate 3
        if (modifiedInvcHead.RepRate3 != originalInvcHead.RepRate3)
        {
            changes.Add("InvcHead.RepRate3`"
                       + originalInvcHead.RepRate3.ToString()
                       + "`"
                       + modifiedInvcHead.RepRate3.ToString().Replace("`", "-"));
        }

        // Rep Rate 4
        if (modifiedInvcHead.RepRate4 != originalInvcHead.RepRate4)
        {
            changes.Add("InvcHead.RepRate4`"
                       + originalInvcHead.RepRate4.ToString()
                       + "`"
                       + modifiedInvcHead.RepRate4.ToString().Replace("`", "-"));
        }

        // Rep Rate 5
        if (modifiedInvcHead.RepRate5 != originalInvcHead.RepRate5)
        {
            changes.Add("InvcHead.RepRate5`"
                       + originalInvcHead.RepRate5.ToString()
                       + "`"
                       + modifiedInvcHead.RepRate5.ToString().Replace("`", "-"));
        }

        // Rep Sales 1
        if (modifiedInvcHead.RepSales1 != originalInvcHead.RepSales1)
        {
            changes.Add("InvcHead.RepSales1`"
                       + originalInvcHead.RepSales1.ToString()
                       + "`"
                       + modifiedInvcHead.RepSales1.ToString().Replace("`", "-"));
        }

        // Rep Sales 2
        if (modifiedInvcHead.RepSales2 != originalInvcHead.RepSales2)
        {
            changes.Add("InvcHead.RepSales2`"
                       + originalInvcHead.RepSales2.ToString()
                       + "`"
                       + modifiedInvcHead.RepSales2.ToString().Replace("`", "-"));
        }

        // Rep Sales 3
        if (modifiedInvcHead.RepSales3 != originalInvcHead.RepSales3)
        {
            changes.Add("InvcHead.RepSales3`"
                       + originalInvcHead.RepSales3.ToString()
                       + "`"
                       + modifiedInvcHead.RepSales3.ToString().Replace("`", "-"));
        }

        // Rep Sales 4
        if (modifiedInvcHead.RepSales4 != originalInvcHead.RepSales4)
        {
            changes.Add("InvcHead.RepSales4`"
                       + originalInvcHead.RepSales4.ToString()
                       + "`"
                       + modifiedInvcHead.RepSales4.ToString().Replace("`", "-"));
        }

        // Rep Sales 5
        if (modifiedInvcHead.RepSales5 != originalInvcHead.RepSales5)
        {
            changes.Add("InvcHead.RepSales5`"
                       + originalInvcHead.RepSales5.ToString()
                       + "`"
                       + modifiedInvcHead.RepSales5.ToString().Replace("`", "-"));
        }

        // Rep Split 1
        if (modifiedInvcHead.RepSplit1 != originalInvcHead.RepSplit1)
        {
            changes.Add("InvcHead.RepSplit1`"
                       + originalInvcHead.RepSplit1.ToString()
                       + "`"
                       + modifiedInvcHead.RepSplit1.ToString().Replace("`", "-"));
        }

        // Rep Split 2
        if (modifiedInvcHead.RepSplit2 != originalInvcHead.RepSplit2)
        {
            changes.Add("InvcHead.RepSplit2`"
                       + originalInvcHead.RepSplit2.ToString()
                       + "`"
                       + modifiedInvcHead.RepSplit2.ToString().Replace("`", "-"));
        }

        // Rep Split 3
        if (modifiedInvcHead.RepSplit3 != originalInvcHead.RepSplit3)
        {
            changes.Add("InvcHead.RepSplit3`"
                       + originalInvcHead.RepSplit3.ToString()
                       + "`"
                       + modifiedInvcHead.RepSplit3.ToString().Replace("`", "-"));
        }

        // Rep Split 4
        if (modifiedInvcHead.RepSplit4 != originalInvcHead.RepSplit4)
        {
            changes.Add("InvcHead.RepSplit4`"
                       + originalInvcHead.RepSplit4.ToString()
                       + "`"
                       + modifiedInvcHead.RepSplit4.ToString().Replace("`", "-"));
        }

        // Rep Split 5
        if (modifiedInvcHead.RepSplit5 != originalInvcHead.RepSplit5)
        {
            changes.Add("InvcHead.RepSplit5`"
                       + originalInvcHead.RepSplit5.ToString()
                       + "`"
                       + modifiedInvcHead.RepSplit5.ToString().Replace("`", "-"));
        }

        // CM Type
        if (modifiedInvcHead.CMType != originalInvcHead.CMType)
        {
            changes.Add("InvcHead.CMType`"
                       + originalInvcHead.CMType.ToString()
                       + "`"
                       + modifiedInvcHead.CMType.ToString().Replace("`", "-"));
        }

        // CC Street Address
        if (modifiedInvcHead.CCStreetAddr != originalInvcHead.CCStreetAddr)
        {
            changes.Add("InvcHead.CCStreetAddr`"
                       + originalInvcHead.CCStreetAddr.ToString()
                       + "`"
                       + modifiedInvcHead.CCStreetAddr.ToString().Replace("`", "-"));
        }

        // CC Zip
        if (modifiedInvcHead.CCZip != originalInvcHead.CCZip)
        {
            changes.Add("InvcHead.CCZip`"
                       + originalInvcHead.CCZip.ToString()
                       + "`"
                       + modifiedInvcHead.CCZip.ToString().Replace("`", "-"));
        }

        // Changed By
        if (modifiedInvcHead.ChangedBy != originalInvcHead.ChangedBy)
        {
            changes.Add("InvcHead.ChangedBy`"
                       + originalInvcHead.ChangedBy.ToString()
                       + "`"
                       + modifiedInvcHead.ChangedBy.ToString().Replace("`", "-"));
        }

        // Change Date
        if (modifiedInvcHead.ChangeDate != originalInvcHead.ChangeDate)
        {
            changes.Add("InvcHead.ChangeDate`"
                       + originalInvcHead.ChangeDate.ToString()
                       + "`"
                       + modifiedInvcHead.ChangeDate.ToString().Replace("`", "-"));
        }

        // Change Time
        if (modifiedInvcHead.ChangeTime != originalInvcHead.ChangeTime)
        {
            changes.Add("InvcHead.ChangeTime`"
                       + originalInvcHead.ChangeTime.ToString()
                       + "`"
                       + modifiedInvcHead.ChangeTime.ToString().Replace("`", "-"));
        }

        // Ready To Calc
        if (modifiedInvcHead.ReadyToCalc != originalInvcHead.ReadyToCalc)
        {
            changes.Add("InvcHead.ReadyToCalc`"
                       + originalInvcHead.ReadyToCalc.ToString()
                       + "`"
                       + modifiedInvcHead.ReadyToCalc.ToString().Replace("`", "-"));
        }

        // Auto Print Ready
        if (modifiedInvcHead.AutoPrintReady != originalInvcHead.AutoPrintReady)
        {
            changes.Add("InvcHead.AutoPrintReady`"
                       + originalInvcHead.AutoPrintReady.ToString()
                       + "`"
                       + modifiedInvcHead.AutoPrintReady.ToString().Replace("`", "-"));
        }

        // EDI Ready
        if (modifiedInvcHead.EDIReady != originalInvcHead.EDIReady)
        {
            changes.Add("InvcHead.EDIReady`"
                       + originalInvcHead.EDIReady.ToString()
                       + "`"
                       + modifiedInvcHead.EDIReady.ToString().Replace("`", "-"));
        }

        // Rounding
        if (modifiedInvcHead.Rounding != originalInvcHead.Rounding)
        {
            changes.Add("InvcHead.Rounding`"
                       + originalInvcHead.Rounding.ToString()
                       + "`"
                       + modifiedInvcHead.Rounding.ToString().Replace("`", "-"));
        }

        // Recalc Before Post
        if (modifiedInvcHead.RecalcBeforePost != originalInvcHead.RecalcBeforePost)
        {
            changes.Add("InvcHead.RecalcBeforePost`"
                       + originalInvcHead.RecalcBeforePost.ToString()
                       + "`"
                       + modifiedInvcHead.RecalcBeforePost.ToString().Replace("`", "-"));
        }

        // Doc Rounding
        if (modifiedInvcHead.DocRounding != originalInvcHead.DocRounding)
        {
            changes.Add("InvcHead.DocRounding`"
                       + originalInvcHead.DocRounding.ToString()
                       + "`"
                       + modifiedInvcHead.DocRounding.ToString().Replace("`", "-"));
        }

        // Rpt1 Deposit Credit
        if (modifiedInvcHead.Rpt1DepositCredit != originalInvcHead.Rpt1DepositCredit)
        {
            changes.Add("InvcHead.Rpt1DepositCredit`"
                       + originalInvcHead.Rpt1DepositCredit.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1DepositCredit.ToString().Replace("`", "-"));
        }

        // Rpt2 Deposit Credit
        if (modifiedInvcHead.Rpt2DepositCredit != originalInvcHead.Rpt2DepositCredit)
        {
            changes.Add("InvcHead.Rpt2DepositCredit`"
                       + originalInvcHead.Rpt2DepositCredit.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2DepositCredit.ToString().Replace("`", "-"));
        }

        // Rpt3 Deposit Credit
        if (modifiedInvcHead.Rpt3DepositCredit != originalInvcHead.Rpt3DepositCredit)
        {
            changes.Add("InvcHead.Rpt3DepositCredit`"
                       + originalInvcHead.Rpt3DepositCredit.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3DepositCredit.ToString().Replace("`", "-"));
        }

        // Rpt1 Invoice Amount
        if (modifiedInvcHead.Rpt1InvoiceAmt != originalInvcHead.Rpt1InvoiceAmt)
        {
            changes.Add("InvcHead.Rpt1InvoiceAmt`"
                       + originalInvcHead.Rpt1InvoiceAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Invoice Amount
        if (modifiedInvcHead.Rpt2InvoiceAmt != originalInvcHead.Rpt2InvoiceAmt)
        {
            changes.Add("InvcHead.Rpt2InvoiceAmt`"
                       + originalInvcHead.Rpt2InvoiceAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Invoice Amount
        if (modifiedInvcHead.Rpt3InvoiceAmt != originalInvcHead.Rpt3InvoiceAmt)
        {
            changes.Add("InvcHead.Rpt3InvoiceAmt`"
                       + originalInvcHead.Rpt3InvoiceAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Invoice Balance
        if (modifiedInvcHead.Rpt1InvoiceBal != originalInvcHead.Rpt1InvoiceBal)
        {
            changes.Add("InvcHead.Rpt1InvoiceBal`"
                       + originalInvcHead.Rpt1InvoiceBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1InvoiceBal.ToString().Replace("`", "-"));
        }

        // Rpt2 Invoice Balance
        if (modifiedInvcHead.Rpt2InvoiceBal != originalInvcHead.Rpt2InvoiceBal)
        {
            changes.Add("InvcHead.Rpt2InvoiceBal`"
                       + originalInvcHead.Rpt2InvoiceBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2InvoiceBal.ToString().Replace("`", "-"));
        }

        // Rpt3 Invoice Balance
        if (modifiedInvcHead.Rpt3InvoiceBal != originalInvcHead.Rpt3InvoiceBal)
        {
            changes.Add("InvcHead.Rpt3InvoiceBal`"
                       + originalInvcHead.Rpt3InvoiceBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3InvoiceBal.ToString().Replace("`", "-"));
        }

        // Rpt1 Pay Amounts
        if (modifiedInvcHead.Rpt1PayAmounts != originalInvcHead.Rpt1PayAmounts)
        {
            changes.Add("InvcHead.Rpt1PayAmounts`"
                       + originalInvcHead.Rpt1PayAmounts.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1PayAmounts.ToString().Replace("`", "-"));
        }

        // Rpt2 Pay Amounts
        if (modifiedInvcHead.Rpt2PayAmounts != originalInvcHead.Rpt2PayAmounts)
        {
            changes.Add("InvcHead.Rpt2PayAmounts`"
                       + originalInvcHead.Rpt2PayAmounts.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2PayAmounts.ToString().Replace("`", "-"));
        }

        // Rpt3 Pay Amounts
        if (modifiedInvcHead.Rpt3PayAmounts != originalInvcHead.Rpt3PayAmounts)
        {
            changes.Add("InvcHead.Rpt3PayAmounts`"
                       + originalInvcHead.Rpt3PayAmounts.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3PayAmounts.ToString().Replace("`", "-"));
        }

        // Rpt1 Pay Discount Amount
        if (modifiedInvcHead.Rpt1PayDiscAmt != originalInvcHead.Rpt1PayDiscAmt)
        {
            changes.Add("InvcHead.Rpt1PayDiscAmt`"
                       + originalInvcHead.Rpt1PayDiscAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1PayDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Pay Discount Amount
        if (modifiedInvcHead.Rpt2PayDiscAmt != originalInvcHead.Rpt2PayDiscAmt)
        {
            changes.Add("InvcHead.Rpt2PayDiscAmt`"
                       + originalInvcHead.Rpt2PayDiscAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2PayDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Pay Discount Amount
        if (modifiedInvcHead.Rpt3PayDiscAmt != originalInvcHead.Rpt3PayDiscAmt)
        {
            changes.Add("InvcHead.Rpt3PayDiscAmt`"
                       + originalInvcHead.Rpt3PayDiscAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3PayDiscAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Rounding
        if (modifiedInvcHead.Rpt1Rounding != originalInvcHead.Rpt1Rounding)
        {
            changes.Add("InvcHead.Rpt1Rounding`"
                       + originalInvcHead.Rpt1Rounding.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1Rounding.ToString().Replace("`", "-"));
        }

        // Rpt2 Rounding
        if (modifiedInvcHead.Rpt2Rounding != originalInvcHead.Rpt2Rounding)
        {
            changes.Add("InvcHead.Rpt2Rounding`"
                       + originalInvcHead.Rpt2Rounding.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2Rounding.ToString().Replace("`", "-"));
        }

        // Rpt3 Rounding
        if (modifiedInvcHead.Rpt3Rounding != originalInvcHead.Rpt3Rounding)
        {
            changes.Add("InvcHead.Rpt3Rounding`"
                       + originalInvcHead.Rpt3Rounding.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3Rounding.ToString().Replace("`", "-"));
        }

        // Rpt1 Unposted Balance
        if (modifiedInvcHead.Rpt1UnpostedBal != originalInvcHead.Rpt1UnpostedBal)
        {
            changes.Add("InvcHead.Rpt1UnpostedBal`"
                       + originalInvcHead.Rpt1UnpostedBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1UnpostedBal.ToString().Replace("`", "-"));
        }

        // Rpt2 Unposted Balance
        if (modifiedInvcHead.Rpt2UnpostedBal != originalInvcHead.Rpt2UnpostedBal)
        {
            changes.Add("InvcHead.Rpt2UnpostedBal`"
                       + originalInvcHead.Rpt2UnpostedBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2UnpostedBal.ToString().Replace("`", "-"));
        }

        // Rpt3 Unposted Balance
        if (modifiedInvcHead.Rpt3UnpostedBal != originalInvcHead.Rpt3UnpostedBal)
        {
            changes.Add("InvcHead.Rpt3UnpostedBal`"
                       + originalInvcHead.Rpt3UnpostedBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3UnpostedBal.ToString().Replace("`", "-"));
        }

        // Rate Group Code
        if (modifiedInvcHead.RateGrpCode != originalInvcHead.RateGrpCode)
        {
            changes.Add("InvcHead.RateGrpCode`"
                       + originalInvcHead.RateGrpCode.ToString()
                       + "`"
                       + modifiedInvcHead.RateGrpCode.ToString().Replace("`", "-"));
        }

        // Doc Deposit Applied
        if (modifiedInvcHead.DocDepApplied != originalInvcHead.DocDepApplied)
        {
            changes.Add("InvcHead.DocDepApplied`"
                       + originalInvcHead.DocDepApplied.ToString()
                       + "`"
                       + modifiedInvcHead.DocDepApplied.ToString().Replace("`", "-"));
        }

        // Rpt1 Deposit Gain Loss
        if (modifiedInvcHead.Rpt1DepGainLoss != originalInvcHead.Rpt1DepGainLoss)
        {
            changes.Add("InvcHead.Rpt1DepGainLoss`"
                       + originalInvcHead.Rpt1DepGainLoss.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1DepGainLoss.ToString().Replace("`", "-"));
        }

        // Rpt2 Deposit Gain Loss
        if (modifiedInvcHead.Rpt2DepGainLoss != originalInvcHead.Rpt2DepGainLoss)
        {
            changes.Add("InvcHead.Rpt2DepGainLoss`"
                       + originalInvcHead.Rpt2DepGainLoss.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2DepGainLoss.ToString().Replace("`", "-"));
        }

        // Rpt3 Deposit Gain Loss
        if (modifiedInvcHead.Rpt3DepGainLoss != originalInvcHead.Rpt3DepGainLoss)
        {
            changes.Add("InvcHead.Rpt3DepGainLoss`"
                       + originalInvcHead.Rpt3DepGainLoss.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3DepGainLoss.ToString().Replace("`", "-"));
        }

        // Apply Date
        if (modifiedInvcHead.ApplyDate != originalInvcHead.ApplyDate)
        {
            changes.Add("InvcHead.ApplyDate`"
                       + originalInvcHead.ApplyDate.ToString()
                       + "`"
                       + modifiedInvcHead.ApplyDate.ToString().Replace("`", "-"));
        }

        // Fiscal Year Suffix
        if (modifiedInvcHead.FiscalYearSuffix != originalInvcHead.FiscalYearSuffix)
        {
            changes.Add("InvcHead.FiscalYearSuffix`"
                       + originalInvcHead.FiscalYearSuffix.ToString()
                       + "`"
                       + modifiedInvcHead.FiscalYearSuffix.ToString().Replace("`", "-"));
        }

        // Fiscal Calendar ID
        if (modifiedInvcHead.FiscalCalendarID != originalInvcHead.FiscalCalendarID)
        {
            changes.Add("InvcHead.FiscalCalendarID`"
                       + originalInvcHead.FiscalCalendarID.ToString()
                       + "`"
                       + modifiedInvcHead.FiscalCalendarID.ToString().Replace("`", "-"));
        }

        // Tax Point
        if (modifiedInvcHead.TaxPoint != originalInvcHead.TaxPoint)
        {
            changes.Add("InvcHead.TaxPoint`"
                       + originalInvcHead.TaxPoint.ToString()
                       + "`"
                       + modifiedInvcHead.TaxPoint.ToString().Replace("`", "-"));
        }

        // Tax Rate Date
        if (modifiedInvcHead.TaxRateDate != originalInvcHead.TaxRateDate)
        {
            changes.Add("InvcHead.TaxRateDate`"
                       + originalInvcHead.TaxRateDate.ToString()
                       + "`"
                       + modifiedInvcHead.TaxRateDate.ToString().Replace("`", "-"));
        }

        // Tax Region Code
        if (modifiedInvcHead.TaxRegionCode != originalInvcHead.TaxRegionCode)
        {
            changes.Add("InvcHead.TaxRegionCode`"
                       + originalInvcHead.TaxRegionCode.ToString()
                       + "`"
                       + modifiedInvcHead.TaxRegionCode.ToString().Replace("`", "-"));
        }

        // Last Charge Calculation Date
        if (modifiedInvcHead.LastChrgCalcDate != originalInvcHead.LastChrgCalcDate)
        {
            changes.Add("InvcHead.LastChrgCalcDate`"
                       + originalInvcHead.LastChrgCalcDate.ToString()
                       + "`"
                       + modifiedInvcHead.LastChrgCalcDate.ToString().Replace("`", "-"));
        }

        // Transaction Document Type ID
        if (modifiedInvcHead.TranDocTypeID != originalInvcHead.TranDocTypeID)
        {
            changes.Add("InvcHead.TranDocTypeID`"
                       + originalInvcHead.TranDocTypeID.ToString()
                       + "`"
                       + modifiedInvcHead.TranDocTypeID.ToString().Replace("`", "-"));
        }

        // Total Finance Charge
        if (modifiedInvcHead.TotFinChrg != originalInvcHead.TotFinChrg)
        {
            changes.Add("InvcHead.TotFinChrg`"
                       + originalInvcHead.TotFinChrg.ToString()
                       + "`"
                       + modifiedInvcHead.TotFinChrg.ToString().Replace("`", "-"));
        }

        // Document Printed
        if (modifiedInvcHead.DocumentPrinted != originalInvcHead.DocumentPrinted)
        {
            changes.Add("InvcHead.DocumentPrinted`"
                       + originalInvcHead.DocumentPrinted.ToString()
                       + "`"
                       + modifiedInvcHead.DocumentPrinted.ToString().Replace("`", "-"));
        }

        // Pay Discount Days
        if (modifiedInvcHead.PayDiscDays != originalInvcHead.PayDiscDays)
        {
            changes.Add("InvcHead.PayDiscDays`"
                       + originalInvcHead.PayDiscDays.ToString()
                       + "`"
                       + modifiedInvcHead.PayDiscDays.ToString().Replace("`", "-"));
        }

        // Pay Discount Percent
        if (modifiedInvcHead.PayDiscPer != originalInvcHead.PayDiscPer)
        {
            changes.Add("InvcHead.PayDiscPer`"
                       + originalInvcHead.PayDiscPer.ToString()
                       + "`"
                       + modifiedInvcHead.PayDiscPer.ToString().Replace("`", "-"));
        }

        // Blocked Finance Charge
        if (modifiedInvcHead.BlockedFinChrg != originalInvcHead.BlockedFinChrg)
        {
            changes.Add("InvcHead.BlockedFinChrg`"
                       + originalInvcHead.BlockedFinChrg.ToString()
                       + "`"
                       + modifiedInvcHead.BlockedFinChrg.ToString().Replace("`", "-"));
        }

        // Blocked Finance Charge Reason
        if (modifiedInvcHead.BlockedFinChrgReason != originalInvcHead.BlockedFinChrgReason)
        {
            changes.Add("InvcHead.BlockedFinChrgReason`"
                       + originalInvcHead.BlockedFinChrgReason.ToString()
                       + "`"
                       + modifiedInvcHead.BlockedFinChrgReason.ToString().Replace("`", "-"));
        }

        // Withhold Amount
        if (modifiedInvcHead.WithholdAmt != originalInvcHead.WithholdAmt)
        {
            changes.Add("InvcHead.WithholdAmt`"
                       + originalInvcHead.WithholdAmt.ToString()
                       + "`"
                       + modifiedInvcHead.WithholdAmt.ToString().Replace("`", "-"));
        }

        // Doc Withhold Amount
        if (modifiedInvcHead.DocWithholdAmt != originalInvcHead.DocWithholdAmt)
        {
            changes.Add("InvcHead.DocWithholdAmt`"
                       + originalInvcHead.DocWithholdAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocWithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Withhold Amount
        if (modifiedInvcHead.Rpt1WithholdAmt != originalInvcHead.Rpt1WithholdAmt)
        {
            changes.Add("InvcHead.Rpt1WithholdAmt`"
                       + originalInvcHead.Rpt1WithholdAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1WithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Withhold Amount
        if (modifiedInvcHead.Rpt2WithholdAmt != originalInvcHead.Rpt2WithholdAmt)
        {
            changes.Add("InvcHead.Rpt2WithholdAmt`"
                       + originalInvcHead.Rpt2WithholdAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2WithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Withhold Amount
        if (modifiedInvcHead.Rpt3WithholdAmt != originalInvcHead.Rpt3WithholdAmt)
        {
            changes.Add("InvcHead.Rpt3WithholdAmt`"
                       + originalInvcHead.Rpt3WithholdAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3WithholdAmt.ToString().Replace("`", "-"));
        }

        // Blocked Reminder Letters
        if (modifiedInvcHead.BlockedRemLetters != originalInvcHead.BlockedRemLetters)
        {
            changes.Add("InvcHead.BlockedRemLetters`"
                       + originalInvcHead.BlockedRemLetters.ToString()
                       + "`"
                       + modifiedInvcHead.BlockedRemLetters.ToString().Replace("`", "-"));
        }

        // Pay Discount Partial Pay
        if (modifiedInvcHead.PayDiscPartPay != originalInvcHead.PayDiscPartPay)
        {
            changes.Add("InvcHead.PayDiscPartPay`"
                       + originalInvcHead.PayDiscPartPay.ToString()
                       + "`"
                       + modifiedInvcHead.PayDiscPartPay.ToString().Replace("`", "-"));
        }

        // Blocked Reminder Letters Reason
        if (modifiedInvcHead.BlockedRemLettersReason != originalInvcHead.BlockedRemLettersReason)
        {
            changes.Add("InvcHead.BlockedRemLettersReason`"
                       + originalInvcHead.BlockedRemLettersReason.ToString()
                       + "`"
                       + modifiedInvcHead.BlockedRemLettersReason.ToString().Replace("`", "-"));
        }

        // Ship Date
        if (modifiedInvcHead.ShipDate != originalInvcHead.ShipDate)
        {
            changes.Add("InvcHead.ShipDate`"
                       + originalInvcHead.ShipDate.ToString()
                       + "`"
                       + modifiedInvcHead.ShipDate.ToString().Replace("`", "-"));
        }

        // Currency Rate Date
        if (modifiedInvcHead.CurrRateDate != originalInvcHead.CurrRateDate)
        {
            changes.Add("InvcHead.CurrRateDate`"
                       + originalInvcHead.CurrRateDate.ToString()
                       + "`"
                       + modifiedInvcHead.CurrRateDate.ToString().Replace("`", "-"));
        }

        // PI Payment
        if (modifiedInvcHead.PIPayment != originalInvcHead.PIPayment)
        {
            changes.Add("InvcHead.PIPayment`"
                       + originalInvcHead.PIPayment.ToString()
                       + "`"
                       + modifiedInvcHead.PIPayment.ToString().Replace("`", "-"));
        }

        // PM UID
        if (modifiedInvcHead.PMUID != originalInvcHead.PMUID)
        {
            changes.Add("InvcHead.PMUID`"
                       + originalInvcHead.PMUID.ToString()
                       + "`"
                       + modifiedInvcHead.PMUID.ToString().Replace("`", "-"));
        }

        // Use Alt Bill To
        if (modifiedInvcHead.UseAltBillTo != originalInvcHead.UseAltBillTo)
        {
            changes.Add("InvcHead.UseAltBillTo`"
                       + originalInvcHead.UseAltBillTo.ToString()
                       + "`"
                       + modifiedInvcHead.UseAltBillTo.ToString().Replace("`", "-"));
        }

        // Correction Invoice
        if (modifiedInvcHead.CorrectionInv != originalInvcHead.CorrectionInv)
        {
            changes.Add("InvcHead.CorrectionInv`"
                       + originalInvcHead.CorrectionInv.ToString()
                       + "`"
                       + modifiedInvcHead.CorrectionInv.ToString().Replace("`", "-"));
        }

        // In Price
        if (modifiedInvcHead.InPrice != originalInvcHead.InPrice)
        {
            changes.Add("InvcHead.InPrice`"
                       + originalInvcHead.InPrice.ToString()
                       + "`"
                       + modifiedInvcHead.InPrice.ToString().Replace("`", "-"));
        }

        // Tax Rate Group Code
        if (modifiedInvcHead.TaxRateGrpCode != originalInvcHead.TaxRateGrpCode)
        {
            changes.Add("InvcHead.TaxRateGrpCode`"
                       + originalInvcHead.TaxRateGrpCode.ToString()
                       + "`"
                       + modifiedInvcHead.TaxRateGrpCode.ToString().Replace("`", "-"));
        }

        // Lock Tax Rate
        if (modifiedInvcHead.LockTaxRate != originalInvcHead.LockTaxRate)
        {
            changes.Add("InvcHead.LockTaxRate`"
                       + originalInvcHead.LockTaxRate.ToString()
                       + "`"
                       + modifiedInvcHead.LockTaxRate.ToString().Replace("`", "-"));
        }

        // SE Bank Reference
        if (modifiedInvcHead.SEBankRef != originalInvcHead.SEBankRef)
        {
            changes.Add("InvcHead.SEBankRef`"
                       + originalInvcHead.SEBankRef.ToString()
                       + "`"
                       + modifiedInvcHead.SEBankRef.ToString().Replace("`", "-"));
        }

        // GUI Tax Type Code
        if (modifiedInvcHead.GUITaxTypeCode != originalInvcHead.GUITaxTypeCode)
        {
            changes.Add("InvcHead.GUITaxTypeCode`"
                       + originalInvcHead.GUITaxTypeCode.ToString()
                       + "`"
                       + modifiedInvcHead.GUITaxTypeCode.ToString().Replace("`", "-"));
        }

        // GUI Format Code
        if (modifiedInvcHead.GUIFormatCode != originalInvcHead.GUIFormatCode)
        {
            changes.Add("InvcHead.GUIFormatCode`"
                       + originalInvcHead.GUIFormatCode.ToString()
                       + "`"
                       + modifiedInvcHead.GUIFormatCode.ToString().Replace("`", "-"));
        }

        // GUI Deduct Code
        if (modifiedInvcHead.GUIDeductCode != originalInvcHead.GUIDeductCode)
        {
            changes.Add("InvcHead.GUIDeductCode`"
                       + originalInvcHead.GUIDeductCode.ToString()
                       + "`"
                       + modifiedInvcHead.GUIDeductCode.ToString().Replace("`", "-"));
        }

        // Reversal Document Amount
        if (modifiedInvcHead.ReversalDocAmount != originalInvcHead.ReversalDocAmount)
        {
            changes.Add("InvcHead.ReversalDocAmount`"
                       + originalInvcHead.ReversalDocAmount.ToString()
                       + "`"
                       + modifiedInvcHead.ReversalDocAmount.ToString().Replace("`", "-"));
        }

        // Original Due Date
        if (modifiedInvcHead.OrigDueDate != originalInvcHead.OrigDueDate)
        {
            changes.Add("InvcHead.OrigDueDate`"
                       + originalInvcHead.OrigDueDate.ToString()
                       + "`"
                       + modifiedInvcHead.OrigDueDate.ToString().Replace("`", "-"));
        }

        // Head Number
        if (modifiedInvcHead.HeadNum != originalInvcHead.HeadNum)
        {
            changes.Add("InvcHead.HeadNum`"
                       + originalInvcHead.HeadNum.ToString()
                       + "`"
                       + modifiedInvcHead.HeadNum.ToString().Replace("`", "-"));
        }

        // AR Location ID
        if (modifiedInvcHead.ARLOCID != originalInvcHead.ARLOCID)
        {
            changes.Add("InvcHead.ARLOCID`"
                       + originalInvcHead.ARLOCID.ToString()
                       + "`"
                       + modifiedInvcHead.ARLOCID.ToString().Replace("`", "-"));
        }

        // Contract Reference
        if (modifiedInvcHead.ContractRef != originalInvcHead.ContractRef)
        {
            changes.Add("InvcHead.ContractRef`"
                       + originalInvcHead.ContractRef.ToString()
                       + "`"
                       + modifiedInvcHead.ContractRef.ToString().Replace("`", "-"));
        }

        // Our Bank
        if (modifiedInvcHead.OurBank != originalInvcHead.OurBank)
        {
            changes.Add("InvcHead.OurBank`"
                       + originalInvcHead.OurBank.ToString()
                       + "`"
                       + modifiedInvcHead.OurBank.ToString().Replace("`", "-"));
        }

        // Contract Date
        if (modifiedInvcHead.ContractDate != originalInvcHead.ContractDate)
        {
            changes.Add("InvcHead.ContractDate`"
                       + originalInvcHead.ContractDate.ToString()
                       + "`"
                       + modifiedInvcHead.ContractDate.ToString().Replace("`", "-"));
        }

        // PB Project ID
        if (modifiedInvcHead.PBProjectID != originalInvcHead.PBProjectID)
        {
            changes.Add("InvcHead.PBProjectID`"
                       + originalInvcHead.PBProjectID.ToString()
                       + "`"
                       + modifiedInvcHead.PBProjectID.ToString().Replace("`", "-"));
        }

        // Deposit Amount
        if (modifiedInvcHead.DepositAmt != originalInvcHead.DepositAmt)
        {
            changes.Add("InvcHead.DepositAmt`"
                       + originalInvcHead.DepositAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DepositAmt.ToString().Replace("`", "-"));
        }

        // GUI Export Bill Number
        if (modifiedInvcHead.GUIExportBillNumber != originalInvcHead.GUIExportBillNumber)
        {
            changes.Add("InvcHead.GUIExportBillNumber`"
                       + originalInvcHead.GUIExportBillNumber.ToString()
                       + "`"
                       + modifiedInvcHead.GUIExportBillNumber.ToString().Replace("`", "-"));
        }

        // Doc Deposit Amount
        if (modifiedInvcHead.DocDepositAmt != originalInvcHead.DocDepositAmt)
        {
            changes.Add("InvcHead.DocDepositAmt`"
                       + originalInvcHead.DocDepositAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocDepositAmt.ToString().Replace("`", "-"));
        }

        // GUI Date Of Export
        if (modifiedInvcHead.GUIDateOfExport != originalInvcHead.GUIDateOfExport)
        {
            changes.Add("InvcHead.GUIDateOfExport`"
                       + originalInvcHead.GUIDateOfExport.ToString()
                       + "`"
                       + modifiedInvcHead.GUIDateOfExport.ToString().Replace("`", "-"));
        }

        // Rpt1 Deposit Amount
        if (modifiedInvcHead.Rpt1DepositAmt != originalInvcHead.Rpt1DepositAmt)
        {
            changes.Add("InvcHead.Rpt1DepositAmt`"
                       + originalInvcHead.Rpt1DepositAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1DepositAmt.ToString().Replace("`", "-"));
        }

        // GUI Export Type
        if (modifiedInvcHead.GUIExportType != originalInvcHead.GUIExportType)
        {
            changes.Add("InvcHead.GUIExportType`"
                       + originalInvcHead.GUIExportType.ToString()
                       + "`"
                       + modifiedInvcHead.GUIExportType.ToString().Replace("`", "-"));
        }

        // Rpt2 Deposit Amount
        if (modifiedInvcHead.Rpt2DepositAmt != originalInvcHead.Rpt2DepositAmt)
        {
            changes.Add("InvcHead.Rpt2DepositAmt`"
                       + originalInvcHead.Rpt2DepositAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2DepositAmt.ToString().Replace("`", "-"));
        }

        // GUI Export Mark
        if (modifiedInvcHead.GUIExportMark != originalInvcHead.GUIExportMark)
        {
            changes.Add("InvcHead.GUIExportMark`"
                       + originalInvcHead.GUIExportMark.ToString()
                       + "`"
                       + modifiedInvcHead.GUIExportMark.ToString().Replace("`", "-"));
        }

        // Rpt3 Deposit Amount
        if (modifiedInvcHead.Rpt3DepositAmt != originalInvcHead.Rpt3DepositAmt)
        {
            changes.Add("InvcHead.Rpt3DepositAmt`"
                       + originalInvcHead.Rpt3DepositAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3DepositAmt.ToString().Replace("`", "-"));
        }

        // GUI Export Bill Type
        if (modifiedInvcHead.GUIExportBillType != originalInvcHead.GUIExportBillType)
        {
            changes.Add("InvcHead.GUIExportBillType`"
                       + originalInvcHead.GUIExportBillType.ToString()
                       + "`"
                       + modifiedInvcHead.GUIExportBillType.ToString().Replace("`", "-"));
        }

        // Deposit Unallocated Amount
        if (modifiedInvcHead.DepUnallocatedAmt != originalInvcHead.DepUnallocatedAmt)
        {
            changes.Add("InvcHead.DepUnallocatedAmt`"
                       + originalInvcHead.DepUnallocatedAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DepUnallocatedAmt.ToString().Replace("`", "-"));
        }

        // Summarization Date
        if (modifiedInvcHead.SummarizationDate != originalInvcHead.SummarizationDate)
        {
            changes.Add("InvcHead.SummarizationDate`"
                       + originalInvcHead.SummarizationDate.ToString()
                       + "`"
                       + modifiedInvcHead.SummarizationDate.ToString().Replace("`", "-"));
        }

        // Doc Deposit Unallocated Amount
        if (modifiedInvcHead.DocDepUnallocatedAmt != originalInvcHead.DocDepUnallocatedAmt)
        {
            changes.Add("InvcHead.DocDepUnallocatedAmt`"
                       + originalInvcHead.DocDepUnallocatedAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocDepUnallocatedAmt.ToString().Replace("`", "-"));
        }

        // Billing Date
        if (modifiedInvcHead.BillingDate != originalInvcHead.BillingDate)
        {
            changes.Add("InvcHead.BillingDate`"
                       + originalInvcHead.BillingDate.ToString()
                       + "`"
                       + modifiedInvcHead.BillingDate.ToString().Replace("`", "-"));
        }

        // Rpt1 Deposit Unallocated Amount
        if (modifiedInvcHead.Rpt1DepUnallocatedAmt != originalInvcHead.Rpt1DepUnallocatedAmt)
        {
            changes.Add("InvcHead.Rpt1DepUnallocatedAmt`"
                       + originalInvcHead.Rpt1DepUnallocatedAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1DepUnallocatedAmt.ToString().Replace("`", "-"));
        }

        // Billing Number
        if (modifiedInvcHead.BillingNumber != originalInvcHead.BillingNumber)
        {
            changes.Add("InvcHead.BillingNumber`"
                       + originalInvcHead.BillingNumber.ToString()
                       + "`"
                       + modifiedInvcHead.BillingNumber.ToString().Replace("`", "-"));
        }

        // Rpt2 Deposit Unallocated Amount
        if (modifiedInvcHead.Rpt2DepUnallocatedAmt != originalInvcHead.Rpt2DepUnallocatedAmt)
        {
            changes.Add("InvcHead.Rpt2DepUnallocatedAmt`"
                       + originalInvcHead.Rpt2DepUnallocatedAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2DepUnallocatedAmt.ToString().Replace("`", "-"));
        }

        // Ready To Bill
        if (modifiedInvcHead.ReadyToBill != originalInvcHead.ReadyToBill)
        {
            changes.Add("InvcHead.ReadyToBill`"
                       + originalInvcHead.ReadyToBill.ToString()
                       + "`"
                       + modifiedInvcHead.ReadyToBill.ToString().Replace("`", "-"));
        }

        // Rpt3 Deposit Unallocated Amount
        if (modifiedInvcHead.Rpt3DepUnallocatedAmt != originalInvcHead.Rpt3DepUnallocatedAmt)
        {
            changes.Add("InvcHead.Rpt3DepUnallocatedAmt`"
                       + originalInvcHead.Rpt3DepUnallocatedAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3DepUnallocatedAmt.ToString().Replace("`", "-"));
        }

        // Override Default Tax Date
        if (modifiedInvcHead.OvrDefTaxDate != originalInvcHead.OvrDefTaxDate)
        {
            changes.Add("InvcHead.OvrDefTaxDate`"
                       + originalInvcHead.OvrDefTaxDate.ToString()
                       + "`"
                       + modifiedInvcHead.OvrDefTaxDate.ToString().Replace("`", "-"));
        }

        // Cross Reference Contract Number
        if (modifiedInvcHead.XRefContractNum != originalInvcHead.XRefContractNum)
        {
            changes.Add("InvcHead.XRefContractNum`"
                       + originalInvcHead.XRefContractNum.ToString()
                       + "`"
                       + modifiedInvcHead.XRefContractNum.ToString().Replace("`", "-"));
        }

        // Cross Reference Contract Date
        if (modifiedInvcHead.XRefContractDate != originalInvcHead.XRefContractDate)
        {
            changes.Add("InvcHead.XRefContractDate`"
                       + originalInvcHead.XRefContractDate.ToString()
                       + "`"
                       + modifiedInvcHead.XRefContractDate.ToString().Replace("`", "-"));
        }

        // Main Site
        if (modifiedInvcHead.MainSite != originalInvcHead.MainSite)
        {
            changes.Add("InvcHead.MainSite`"
                       + originalInvcHead.MainSite.ToString()
                       + "`"
                       + modifiedInvcHead.MainSite.ToString().Replace("`", "-"));
        }

        // Site Code
        if (modifiedInvcHead.SiteCode != originalInvcHead.SiteCode)
        {
            changes.Add("InvcHead.SiteCode`"
                       + originalInvcHead.SiteCode.ToString()
                       + "`"
                       + modifiedInvcHead.SiteCode.ToString().Replace("`", "-"));
        }

        // Branch ID
        if (modifiedInvcHead.BranchID != originalInvcHead.BranchID)
        {
            changes.Add("InvcHead.BranchID`"
                       + originalInvcHead.BranchID.ToString()
                       + "`"
                       + modifiedInvcHead.BranchID.ToString().Replace("`", "-"));
        }

        // Customer Agent Name
        if (modifiedInvcHead.CustAgentName != originalInvcHead.CustAgentName)
        {
            changes.Add("InvcHead.CustAgentName`"
                       + originalInvcHead.CustAgentName.ToString()
                       + "`"
                       + modifiedInvcHead.CustAgentName.ToString().Replace("`", "-"));
        }

        // Customer Agent Tax Registration Number
        if (modifiedInvcHead.CustAgentTaxRegNo != originalInvcHead.CustAgentTaxRegNo)
        {
            changes.Add("InvcHead.CustAgentTaxRegNo`"
                       + originalInvcHead.CustAgentTaxRegNo.ToString()
                       + "`"
                       + modifiedInvcHead.CustAgentTaxRegNo.ToString().Replace("`", "-"));
        }

        // Export Type
        if (modifiedInvcHead.ExportType != originalInvcHead.ExportType)
        {
            changes.Add("InvcHead.ExportType`"
                       + originalInvcHead.ExportType.ToString()
                       + "`"
                       + modifiedInvcHead.ExportType.ToString().Replace("`", "-"));
        }

        // Export Report Number
        if (modifiedInvcHead.ExportReportNo != originalInvcHead.ExportReportNo)
        {
            changes.Add("InvcHead.ExportReportNo`"
                       + originalInvcHead.ExportReportNo.ToString()
                       + "`"
                       + modifiedInvcHead.ExportReportNo.ToString().Replace("`", "-"));
        }

        // Real Estate Number
        if (modifiedInvcHead.RealEstateNo != originalInvcHead.RealEstateNo)
        {
            changes.Add("InvcHead.RealEstateNo`"
                       + originalInvcHead.RealEstateNo.ToString()
                       + "`"
                       + modifiedInvcHead.RealEstateNo.ToString().Replace("`", "-"));
        }

        // Excluded
        if (modifiedInvcHead.Excluded != originalInvcHead.Excluded)
        {
            changes.Add("InvcHead.Excluded`"
                       + originalInvcHead.Excluded.ToString()
                       + "`"
                       + modifiedInvcHead.Excluded.ToString().Replace("`", "-"));
        }

        // Deferred
        if (modifiedInvcHead.Deferred != originalInvcHead.Deferred)
        {
            changes.Add("InvcHead.Deferred`"
                       + originalInvcHead.Deferred.ToString()
                       + "`"
                       + modifiedInvcHead.Deferred.ToString().Replace("`", "-"));
        }

        // Cycle Code
        if (modifiedInvcHead.CycleCode != originalInvcHead.CycleCode)
        {
            changes.Add("InvcHead.CycleCode`"
                       + originalInvcHead.CycleCode.ToString()
                       + "`"
                       + modifiedInvcHead.CycleCode.ToString().Replace("`", "-"));
        }

        // Duration
        if (modifiedInvcHead.Duration != originalInvcHead.Duration)
        {
            changes.Add("InvcHead.Duration`"
                       + originalInvcHead.Duration.ToString()
                       + "`"
                       + modifiedInvcHead.Duration.ToString().Replace("`", "-"));
        }

        // End Date
        if (modifiedInvcHead.EndDate != originalInvcHead.EndDate)
        {
            changes.Add("InvcHead.EndDate`"
                       + originalInvcHead.EndDate.ToString()
                       + "`"
                       + modifiedInvcHead.EndDate.ToString().Replace("`", "-"));
        }

        // Max Value Amount
        if (modifiedInvcHead.MaxValueAmt != originalInvcHead.MaxValueAmt)
        {
            changes.Add("InvcHead.MaxValueAmt`"
                       + originalInvcHead.MaxValueAmt.ToString()
                       + "`"
                       + modifiedInvcHead.MaxValueAmt.ToString().Replace("`", "-"));
        }

        // Doc Max Value Amount
        if (modifiedInvcHead.DocMaxValueAmt != originalInvcHead.DocMaxValueAmt)
        {
            changes.Add("InvcHead.DocMaxValueAmt`"
                       + originalInvcHead.DocMaxValueAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocMaxValueAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Max Value Amount
        if (modifiedInvcHead.Rpt1MaxValueAmt != originalInvcHead.Rpt1MaxValueAmt)
        {
            changes.Add("InvcHead.Rpt1MaxValueAmt`"
                       + originalInvcHead.Rpt1MaxValueAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1MaxValueAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Max Value Amount
        if (modifiedInvcHead.Rpt2MaxValueAmt != originalInvcHead.Rpt2MaxValueAmt)
        {
            changes.Add("InvcHead.Rpt2MaxValueAmt`"
                       + originalInvcHead.Rpt2MaxValueAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2MaxValueAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Max Value Amount
        if (modifiedInvcHead.Rpt3MaxValueAmt != originalInvcHead.Rpt3MaxValueAmt)
        {
            changes.Add("InvcHead.Rpt3MaxValueAmt`"
                       + originalInvcHead.Rpt3MaxValueAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3MaxValueAmt.ToString().Replace("`", "-"));
        }

        // Hold Invoice
        if (modifiedInvcHead.HoldInvoice != originalInvcHead.HoldInvoice)
        {
            changes.Add("InvcHead.HoldInvoice`"
                       + originalInvcHead.HoldInvoice.ToString()
                       + "`"
                       + modifiedInvcHead.HoldInvoice.ToString().Replace("`", "-"));
        }

        // Override End Date
        if (modifiedInvcHead.OverrideEndDate != originalInvcHead.OverrideEndDate)
        {
            changes.Add("InvcHead.OverrideEndDate`"
                       + originalInvcHead.OverrideEndDate.ToString()
                       + "`"
                       + modifiedInvcHead.OverrideEndDate.ToString().Replace("`", "-"));
        }

        // Cycle Inactive
        if (modifiedInvcHead.CycleInactive != originalInvcHead.CycleInactive)
        {
            changes.Add("InvcHead.CycleInactive`"
                       + originalInvcHead.CycleInactive.ToString()
                       + "`"
                       + modifiedInvcHead.CycleInactive.ToString().Replace("`", "-"));
        }

        // Recur Source
        if (modifiedInvcHead.RecurSource != originalInvcHead.RecurSource)
        {
            changes.Add("InvcHead.RecurSource`"
                       + originalInvcHead.RecurSource.ToString()
                       + "`"
                       + modifiedInvcHead.RecurSource.ToString().Replace("`", "-"));
        }

        // Instance Number
        if (modifiedInvcHead.InstanceNum != originalInvcHead.InstanceNum)
        {
            changes.Add("InvcHead.InstanceNum`"
                       + originalInvcHead.InstanceNum.ToString()
                       + "`"
                       + modifiedInvcHead.InstanceNum.ToString().Replace("`", "-"));
        }

        // Recur Balance
        if (modifiedInvcHead.RecurBalance != originalInvcHead.RecurBalance)
        {
            changes.Add("InvcHead.RecurBalance`"
                       + originalInvcHead.RecurBalance.ToString()
                       + "`"
                       + modifiedInvcHead.RecurBalance.ToString().Replace("`", "-"));
        }

        // Doc Recur Balance
        if (modifiedInvcHead.DocRecurBalance != originalInvcHead.DocRecurBalance)
        {
            changes.Add("InvcHead.DocRecurBalance`"
                       + originalInvcHead.DocRecurBalance.ToString()
                       + "`"
                       + modifiedInvcHead.DocRecurBalance.ToString().Replace("`", "-"));
        }

        // Rpt1 Recur Balance
        if (modifiedInvcHead.Rpt1RecurBalance != originalInvcHead.Rpt1RecurBalance)
        {
            changes.Add("InvcHead.Rpt1RecurBalance`"
                       + originalInvcHead.Rpt1RecurBalance.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1RecurBalance.ToString().Replace("`", "-"));
        }

        // Rpt2 Recur Balance
        if (modifiedInvcHead.Rpt2RecurBalance != originalInvcHead.Rpt2RecurBalance)
        {
            changes.Add("InvcHead.Rpt2RecurBalance`"
                       + originalInvcHead.Rpt2RecurBalance.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2RecurBalance.ToString().Replace("`", "-"));
        }

        // Rpt3 Recur Balance
        if (modifiedInvcHead.Rpt3RecurBalance != originalInvcHead.Rpt3RecurBalance)
        {
            changes.Add("InvcHead.Rpt3RecurBalance`"
                       + originalInvcHead.Rpt3RecurBalance.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3RecurBalance.ToString().Replace("`", "-"));
        }

        // Last Date
        if (modifiedInvcHead.LastDate != originalInvcHead.LastDate)
        {
            changes.Add("InvcHead.LastDate`"
                       + originalInvcHead.LastDate.ToString()
                       + "`"
                       + modifiedInvcHead.LastDate.ToString().Replace("`", "-"));
        }

        // Invoice Number List
        if (modifiedInvcHead.InvoiceNumList != originalInvcHead.InvoiceNumList)
        {
            changes.Add("InvcHead.InvoiceNumList`"
                       + originalInvcHead.InvoiceNumList.ToString()
                       + "`"
                       + modifiedInvcHead.InvoiceNumList.ToString().Replace("`", "-"));
        }

        // Is Added To GTI
        if (modifiedInvcHead.IsAddedToGTI != originalInvcHead.IsAddedToGTI)
        {
            changes.Add("InvcHead.IsAddedToGTI`"
                       + originalInvcHead.IsAddedToGTI.ToString()
                       + "`"
                       + modifiedInvcHead.IsAddedToGTI.ToString().Replace("`", "-"));
        }

        // CH ISR Code Line
        if (modifiedInvcHead.CHISRCodeLine != originalInvcHead.CHISRCodeLine)
        {
            changes.Add("InvcHead.CHISRCodeLine`"
                       + originalInvcHead.CHISRCodeLine.ToString()
                       + "`"
                       + modifiedInvcHead.CHISRCodeLine.ToString().Replace("`", "-"));
        }

        // CM Reason
        if (modifiedInvcHead.CMReason != originalInvcHead.CMReason)
        {
            changes.Add("InvcHead.CMReason`"
                       + originalInvcHead.CMReason.ToString()
                       + "`"
                       + modifiedInvcHead.CMReason.ToString().Replace("`", "-"));
        }

        // TH Is Immaterial Adjustment
        if (modifiedInvcHead.THIsImmatAdjustment != originalInvcHead.THIsImmatAdjustment)
        {
            changes.Add("InvcHead.THIsImmatAdjustment`"
                       + originalInvcHead.THIsImmatAdjustment.ToString()
                       + "`"
                       + modifiedInvcHead.THIsImmatAdjustment.ToString().Replace("`", "-"));
        }

        // AG Authorization Code
        if (modifiedInvcHead.AGAuthorizationCode != originalInvcHead.AGAuthorizationCode)
        {
            changes.Add("InvcHead.AGAuthorizationCode`"
                       + originalInvcHead.AGAuthorizationCode.ToString()
                       + "`"
                       + modifiedInvcHead.AGAuthorizationCode.ToString().Replace("`", "-"));
        }

        // AG Authorization Date
        if (modifiedInvcHead.AGAuthorizationDate != originalInvcHead.AGAuthorizationDate)
        {
            changes.Add("InvcHead.AGAuthorizationDate`"
                       + originalInvcHead.AGAuthorizationDate.ToString()
                       + "`"
                       + modifiedInvcHead.AGAuthorizationDate.ToString().Replace("`", "-"));
        }

        // AG Use Good Default Mark
        if (modifiedInvcHead.AGUseGoodDefaultMark != originalInvcHead.AGUseGoodDefaultMark)
        {
            changes.Add("InvcHead.AGUseGoodDefaultMark`"
                       + originalInvcHead.AGUseGoodDefaultMark.ToString()
                       + "`"
                       + modifiedInvcHead.AGUseGoodDefaultMark.ToString().Replace("`", "-"));
        }

        // AG Document Letter
        if (modifiedInvcHead.AGDocumentLetter != originalInvcHead.AGDocumentLetter)
        {
            changes.Add("InvcHead.AGDocumentLetter`"
                       + originalInvcHead.AGDocumentLetter.ToString()
                       + "`"
                       + modifiedInvcHead.AGDocumentLetter.ToString().Replace("`", "-"));
        }

        // AG Invoicing Point
        if (modifiedInvcHead.AGInvoicingPoint != originalInvcHead.AGInvoicingPoint)
        {
            changes.Add("InvcHead.AGInvoicingPoint`"
                       + originalInvcHead.AGInvoicingPoint.ToString()
                       + "`"
                       + modifiedInvcHead.AGInvoicingPoint.ToString().Replace("`", "-"));
        }

        // AG Legal Number
        if (modifiedInvcHead.AGLegalNumber != originalInvcHead.AGLegalNumber)
        {
            changes.Add("InvcHead.AGLegalNumber`"
                       + originalInvcHead.AGLegalNumber.ToString()
                       + "`"
                       + modifiedInvcHead.AGLegalNumber.ToString().Replace("`", "-"));
        }

        // AG Printing Control Type
        if (modifiedInvcHead.AGPrintingControlType != originalInvcHead.AGPrintingControlType)
        {
            changes.Add("InvcHead.AGPrintingControlType`"
                       + originalInvcHead.AGPrintingControlType.ToString()
                       + "`"
                       + modifiedInvcHead.AGPrintingControlType.ToString().Replace("`", "-"));
        }

        // Revision Date
        if (modifiedInvcHead.RevisionDate != originalInvcHead.RevisionDate)
        {
            changes.Add("InvcHead.RevisionDate`"
                       + originalInvcHead.RevisionDate.ToString()
                       + "`"
                       + modifiedInvcHead.RevisionDate.ToString().Replace("`", "-"));
        }

        // Revision Number
        if (modifiedInvcHead.RevisionNum != originalInvcHead.RevisionNum)
        {
            changes.Add("InvcHead.RevisionNum`"
                       + originalInvcHead.RevisionNum.ToString()
                       + "`"
                       + modifiedInvcHead.RevisionNum.ToString().Replace("`", "-"));
        }

        // TW Declare Year
        if (modifiedInvcHead.TWDeclareYear != originalInvcHead.TWDeclareYear)
        {
            changes.Add("InvcHead.TWDeclareYear`"
                       + originalInvcHead.TWDeclareYear.ToString()
                       + "`"
                       + modifiedInvcHead.TWDeclareYear.ToString().Replace("`", "-"));
        }

        // TW Declare Period
        if (modifiedInvcHead.TWDeclarePeriod != originalInvcHead.TWDeclarePeriod)
        {
            changes.Add("InvcHead.TWDeclarePeriod`"
                       + originalInvcHead.TWDeclarePeriod.ToString()
                       + "`"
                       + modifiedInvcHead.TWDeclarePeriod.ToString().Replace("`", "-"));
        }

        // TW Generation Type
        if (modifiedInvcHead.TWGenerationType != originalInvcHead.TWGenerationType)
        {
            changes.Add("InvcHead.TWGenerationType`"
                       + originalInvcHead.TWGenerationType.ToString()
                       + "`"
                       + modifiedInvcHead.TWGenerationType.ToString().Replace("`", "-"));
        }

        // TW GUI Group
        if (modifiedInvcHead.TWGUIGroup != originalInvcHead.TWGUIGroup)
        {
            changes.Add("InvcHead.TWGUIGroup`"
                       + originalInvcHead.TWGUIGroup.ToString()
                       + "`"
                       + modifiedInvcHead.TWGUIGroup.ToString().Replace("`", "-"));
        }

        // TW Period Prefix
        if (modifiedInvcHead.TWPeriodPrefix != originalInvcHead.TWPeriodPrefix)
        {
            changes.Add("InvcHead.TWPeriodPrefix`"
                       + originalInvcHead.TWPeriodPrefix.ToString()
                       + "`"
                       + modifiedInvcHead.TWPeriodPrefix.ToString().Replace("`", "-"));
        }

        // Invoice In Collections
        if (modifiedInvcHead.InvInCollections != originalInvcHead.InvInCollections)
        {
            changes.Add("InvcHead.InvInCollections`"
                       + originalInvcHead.InvInCollections.ToString()
                       + "`"
                       + modifiedInvcHead.InvInCollections.ToString().Replace("`", "-"));
        }

        // Collections Customer
        if (modifiedInvcHead.CollectionsCust != originalInvcHead.CollectionsCust)
        {
            changes.Add("InvcHead.CollectionsCust`"
                       + originalInvcHead.CollectionsCust.ToString()
                       + "`"
                       + modifiedInvcHead.CollectionsCust.ToString().Replace("`", "-"));
        }

        // Counter AR Form
        if (modifiedInvcHead.CounterARForm != originalInvcHead.CounterARForm)
        {
            changes.Add("InvcHead.CounterARForm`"
                       + originalInvcHead.CounterARForm.ToString()
                       + "`"
                       + modifiedInvcHead.CounterARForm.ToString().Replace("`", "-"));
        }

        // Posted Recognition
        if (modifiedInvcHead.PostedRecog != originalInvcHead.PostedRecog)
        {
            changes.Add("InvcHead.PostedRecog`"
                       + originalInvcHead.PostedRecog.ToString()
                       + "`"
                       + modifiedInvcHead.PostedRecog.ToString().Replace("`", "-"));
        }

        // CN Confirm Date
        if (modifiedInvcHead.CNConfirmDate != originalInvcHead.CNConfirmDate)
        {
            changes.Add("InvcHead.CNConfirmDate`"
                       + originalInvcHead.CNConfirmDate.ToString()
                       + "`"
                       + modifiedInvcHead.CNConfirmDate.ToString().Replace("`", "-"));
        }

        // MX SAT Seal
        if (modifiedInvcHead.MXSATSeal != originalInvcHead.MXSATSeal)
        {
            changes.Add("InvcHead.MXSATSeal`"
                       + originalInvcHead.MXSATSeal.ToString()
                       + "`"
                       + modifiedInvcHead.MXSATSeal.ToString().Replace("`", "-"));
        }

        // MX Serie
        if (modifiedInvcHead.MXSerie != originalInvcHead.MXSerie)
        {
            changes.Add("InvcHead.MXSerie`"
                       + originalInvcHead.MXSerie.ToString()
                       + "`"
                       + modifiedInvcHead.MXSerie.ToString().Replace("`", "-"));
        }

        // MX Tax Receipt Type
        if (modifiedInvcHead.MXTaxRcptType != originalInvcHead.MXTaxRcptType)
        {
            changes.Add("InvcHead.MXTaxRcptType`"
                       + originalInvcHead.MXTaxRcptType.ToString()
                       + "`"
                       + modifiedInvcHead.MXTaxRcptType.ToString().Replace("`", "-"));
        }

        // MX Fiscal Folio
        if (modifiedInvcHead.MXFiscalFolio != originalInvcHead.MXFiscalFolio)
        {
            changes.Add("InvcHead.MXFiscalFolio`"
                       + originalInvcHead.MXFiscalFolio.ToString()
                       + "`"
                       + modifiedInvcHead.MXFiscalFolio.ToString().Replace("`", "-"));
        }

        // MX Total Payments
        if (modifiedInvcHead.MXTotalPayments != originalInvcHead.MXTotalPayments)
        {
            changes.Add("InvcHead.MXTotalPayments`"
                       + originalInvcHead.MXTotalPayments.ToString()
                       + "`"
                       + modifiedInvcHead.MXTotalPayments.ToString().Replace("`", "-"));
        }

        // MX Folio
        if (modifiedInvcHead.MXFolio != originalInvcHead.MXFolio)
        {
            changes.Add("InvcHead.MXFolio`"
                       + originalInvcHead.MXFolio.ToString()
                       + "`"
                       + modifiedInvcHead.MXFolio.ToString().Replace("`", "-"));
        }

        // MX Certified Timestamp
        if (modifiedInvcHead.MXCertifiedTimestamp != originalInvcHead.MXCertifiedTimestamp)
        {
            changes.Add("InvcHead.MXCertifiedTimestamp`"
                       + originalInvcHead.MXCertifiedTimestamp.ToString()
                       + "`"
                       + modifiedInvcHead.MXCertifiedTimestamp.ToString().Replace("`", "-"));
        }

        // MX SAT Certificate Serial Number
        if (modifiedInvcHead.MXSATCertificateSN != originalInvcHead.MXSATCertificateSN)
        {
            changes.Add("InvcHead.MXSATCertificateSN`"
                       + originalInvcHead.MXSATCertificateSN.ToString()
                       + "`"
                       + modifiedInvcHead.MXSATCertificateSN.ToString().Replace("`", "-"));
        }

        // MX Digital Seal
        if (modifiedInvcHead.MXDigitalSeal != originalInvcHead.MXDigitalSeal)
        {
            changes.Add("InvcHead.MXDigitalSeal`"
                       + originalInvcHead.MXDigitalSeal.ToString()
                       + "`"
                       + modifiedInvcHead.MXDigitalSeal.ToString().Replace("`", "-"));
        }

        // MX Posted Timestamp
        if (modifiedInvcHead.MXPostedTimeStamp != originalInvcHead.MXPostedTimeStamp)
        {
            changes.Add("InvcHead.MXPostedTimeStamp`"
                       + originalInvcHead.MXPostedTimeStamp.ToString()
                       + "`"
                       + modifiedInvcHead.MXPostedTimeStamp.ToString().Replace("`", "-"));
        }

        // MX Certificate
        if (modifiedInvcHead.MXCertificate != originalInvcHead.MXCertificate)
        {
            changes.Add("InvcHead.MXCertificate`"
                       + originalInvcHead.MXCertificate.ToString()
                       + "`"
                       + modifiedInvcHead.MXCertificate.ToString().Replace("`", "-"));
        }

        // MX Approval Year
        if (modifiedInvcHead.MXApprovalYear != originalInvcHead.MXApprovalYear)
        {
            changes.Add("InvcHead.MXApprovalYear`"
                       + originalInvcHead.MXApprovalYear.ToString()
                       + "`"
                       + modifiedInvcHead.MXApprovalYear.ToString().Replace("`", "-"));
        }

        // MX CBB
        if (modifiedInvcHead.MXCBB != originalInvcHead.MXCBB)
        {
            changes.Add("InvcHead.MXCBB`"
                       + originalInvcHead.MXCBB.ToString()
                       + "`"
                       + modifiedInvcHead.MXCBB.ToString().Replace("`", "-"));
        }

        // MX Approval Number
        if (modifiedInvcHead.MXApprovalNum != originalInvcHead.MXApprovalNum)
        {
            changes.Add("InvcHead.MXApprovalNum`"
                       + originalInvcHead.MXApprovalNum.ToString()
                       + "`"
                       + modifiedInvcHead.MXApprovalNum.ToString().Replace("`", "-"));
        }

        // MX Original String TFD
        if (modifiedInvcHead.MXOriginalStringTFD != originalInvcHead.MXOriginalStringTFD)
        {
            changes.Add("InvcHead.MXOriginalStringTFD`"
                       + originalInvcHead.MXOriginalStringTFD.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalStringTFD.ToString().Replace("`", "-"));
        }

        // MX Payment Number
        if (modifiedInvcHead.MXPaymentNum != originalInvcHead.MXPaymentNum)
        {
            changes.Add("InvcHead.MXPaymentNum`"
                       + originalInvcHead.MXPaymentNum.ToString()
                       + "`"
                       + modifiedInvcHead.MXPaymentNum.ToString().Replace("`", "-"));
        }

        // MX Paid As
        if (modifiedInvcHead.MXPaidAs != originalInvcHead.MXPaidAs)
        {
            changes.Add("InvcHead.MXPaidAs`"
                       + originalInvcHead.MXPaidAs.ToString()
                       + "`"
                       + modifiedInvcHead.MXPaidAs.ToString().Replace("`", "-"));
        }

        // MX Certificate Serial Number
        if (modifiedInvcHead.MXCertificateSN != originalInvcHead.MXCertificateSN)
        {
            changes.Add("InvcHead.MXCertificateSN`"
                       + originalInvcHead.MXCertificateSN.ToString()
                       + "`"
                       + modifiedInvcHead.MXCertificateSN.ToString().Replace("`", "-"));
        }

        // MX Original Amount
        if (modifiedInvcHead.MXOriginalAmount != originalInvcHead.MXOriginalAmount)
        {
            changes.Add("InvcHead.MXOriginalAmount`"
                       + originalInvcHead.MXOriginalAmount.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalAmount.ToString().Replace("`", "-"));
        }

        // MX Account Number
        if (modifiedInvcHead.MXAccountNumber != originalInvcHead.MXAccountNumber)
        {
            changes.Add("InvcHead.MXAccountNumber`"
                       + originalInvcHead.MXAccountNumber.ToString()
                       + "`"
                       + modifiedInvcHead.MXAccountNumber.ToString().Replace("`", "-"));
        }

        // MX Original Date
        if (modifiedInvcHead.MXOriginalDate != originalInvcHead.MXOriginalDate)
        {
            changes.Add("InvcHead.MXOriginalDate`"
                       + originalInvcHead.MXOriginalDate.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalDate.ToString().Replace("`", "-"));
        }

        // MX Original Series
        if (modifiedInvcHead.MXOriginalSeries != originalInvcHead.MXOriginalSeries)
        {
            changes.Add("InvcHead.MXOriginalSeries`"
                       + originalInvcHead.MXOriginalSeries.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalSeries.ToString().Replace("`", "-"));
        }

        // MX Original Folio
        if (modifiedInvcHead.MXOriginalFolio != originalInvcHead.MXOriginalFolio)
        {
            changes.Add("InvcHead.MXOriginalFolio`"
                       + originalInvcHead.MXOriginalFolio.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalFolio.ToString().Replace("`", "-"));
        }

        // MX Tax Regime
        if (modifiedInvcHead.MXTaxRegime != originalInvcHead.MXTaxRegime)
        {
            changes.Add("InvcHead.MXTaxRegime`"
                       + originalInvcHead.MXTaxRegime.ToString()
                       + "`"
                       + modifiedInvcHead.MXTaxRegime.ToString().Replace("`", "-"));
        }

        // MX Original String
        if (modifiedInvcHead.MXOriginalString != originalInvcHead.MXOriginalString)
        {
            changes.Add("InvcHead.MXOriginalString`"
                       + originalInvcHead.MXOriginalString.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalString.ToString().Replace("`", "-"));
        }

        // MX Payment Name
        if (modifiedInvcHead.MXPaymentName != originalInvcHead.MXPaymentName)
        {
            changes.Add("InvcHead.MXPaymentName`"
                       + originalInvcHead.MXPaymentName.ToString()
                       + "`"
                       + modifiedInvcHead.MXPaymentName.ToString().Replace("`", "-"));
        }

        // E Invoice
        if (modifiedInvcHead.EInvoice != originalInvcHead.EInvoice)
        {
            changes.Add("InvcHead.EInvoice`"
                       + originalInvcHead.EInvoice.ToString()
                       + "`"
                       + modifiedInvcHead.EInvoice.ToString().Replace("`", "-"));
        }

        // E Invoice Status
        if (modifiedInvcHead.EInvStatus != originalInvcHead.EInvStatus)
        {
            changes.Add("InvcHead.EInvStatus`"
                       + originalInvcHead.EInvStatus.ToString()
                       + "`"
                       + modifiedInvcHead.EInvStatus.ToString().Replace("`", "-"));
        }

        // E Invoice Timestamp
        if (modifiedInvcHead.EInvTimestamp != originalInvcHead.EInvTimestamp)
        {
            changes.Add("InvcHead.EInvTimestamp`"
                       + originalInvcHead.EInvTimestamp.ToString()
                       + "`"
                       + modifiedInvcHead.EInvTimestamp.ToString().Replace("`", "-"));
        }

        // E Invoice Updated By
        if (modifiedInvcHead.EInvUpdatedBy != originalInvcHead.EInvUpdatedBy)
        {
            changes.Add("InvcHead.EInvUpdatedBy`"
                       + originalInvcHead.EInvUpdatedBy.ToString()
                       + "`"
                       + modifiedInvcHead.EInvUpdatedBy.ToString().Replace("`", "-"));
        }

        // E Invoice Exception
        if (modifiedInvcHead.EInvException != originalInvcHead.EInvException)
        {
            changes.Add("InvcHead.EInvException`"
                       + originalInvcHead.EInvException.ToString()
                       + "`"
                       + modifiedInvcHead.EInvException.ToString().Replace("`", "-"));
        }

        // With Tax Confirm
        if (modifiedInvcHead.WithTaxConfirm != originalInvcHead.WithTaxConfirm)
        {
            changes.Add("InvcHead.WithTaxConfirm`"
                       + originalInvcHead.WithTaxConfirm.ToString()
                       + "`"
                       + modifiedInvcHead.WithTaxConfirm.ToString().Replace("`", "-"));
        }

        // Use Alt Bill To ID
        if (modifiedInvcHead.UseAltBillToID != originalInvcHead.UseAltBillToID)
        {
            changes.Add("InvcHead.UseAltBillToID`"
                       + originalInvcHead.UseAltBillToID.ToString()
                       + "`"
                       + modifiedInvcHead.UseAltBillToID.ToString().Replace("`", "-"));
        }

        // MX Cancelled Date
        if (modifiedInvcHead.MXCancelledDate != originalInvcHead.MXCancelledDate)
        {
            changes.Add("InvcHead.MXCancelledDate`"
                       + originalInvcHead.MXCancelledDate.ToString()
                       + "`"
                       + modifiedInvcHead.MXCancelledDate.ToString().Replace("`", "-"));
        }

        // Overpaid
        if (modifiedInvcHead.Overpaid != originalInvcHead.Overpaid)
        {
            changes.Add("InvcHead.Overpaid`"
                       + originalInvcHead.Overpaid.ToString()
                       + "`"
                       + modifiedInvcHead.Overpaid.ToString().Replace("`", "-"));
        }

        // Order Exchange Rate
        if (modifiedInvcHead.OrdExchangeRate != originalInvcHead.OrdExchangeRate)
        {
            changes.Add("InvcHead.OrdExchangeRate`"
                       + originalInvcHead.OrdExchangeRate.ToString()
                       + "`"
                       + modifiedInvcHead.OrdExchangeRate.ToString().Replace("`", "-"));
        }

        // PE AP Payment Number
        if (modifiedInvcHead.PEAPPayNum != originalInvcHead.PEAPPayNum)
        {
            changes.Add("InvcHead.PEAPPayNum`"
                       + originalInvcHead.PEAPPayNum.ToString()
                       + "`"
                       + modifiedInvcHead.PEAPPayNum.ToString().Replace("`", "-"));
        }

        // PE Bank Number
        if (modifiedInvcHead.PEBankNumber != originalInvcHead.PEBankNumber)
        {
            changes.Add("InvcHead.PEBankNumber`"
                       + originalInvcHead.PEBankNumber.ToString()
                       + "`"
                       + modifiedInvcHead.PEBankNumber.ToString().Replace("`", "-"));
        }

        // PE Charges
        if (modifiedInvcHead.PECharges != originalInvcHead.PECharges)
        {
            changes.Add("InvcHead.PECharges`"
                       + originalInvcHead.PECharges.ToString()
                       + "`"
                       + modifiedInvcHead.PECharges.ToString().Replace("`", "-"));
        }

        // PE Commissions
        if (modifiedInvcHead.PECommissions != originalInvcHead.PECommissions)
        {
            changes.Add("InvcHead.PECommissions`"
                       + originalInvcHead.PECommissions.ToString()
                       + "`"
                       + modifiedInvcHead.PECommissions.ToString().Replace("`", "-"));
        }

        // PE Detraction Tax Amount
        if (modifiedInvcHead.PEDetTaxAmt != originalInvcHead.PEDetTaxAmt)
        {
            changes.Add("InvcHead.PEDetTaxAmt`"
                       + originalInvcHead.PEDetTaxAmt.ToString()
                       + "`"
                       + modifiedInvcHead.PEDetTaxAmt.ToString().Replace("`", "-"));
        }

        // PE Detraction Tax Currency Code
        if (modifiedInvcHead.PEDetTaxCurrencyCode != originalInvcHead.PEDetTaxCurrencyCode)
        {
            changes.Add("InvcHead.PEDetTaxCurrencyCode`"
                       + originalInvcHead.PEDetTaxCurrencyCode.ToString()
                       + "`"
                       + modifiedInvcHead.PEDetTaxCurrencyCode.ToString().Replace("`", "-"));
        }

        // PE Discharge Amount
        if (modifiedInvcHead.PEDischargeAmt != originalInvcHead.PEDischargeAmt)
        {
            changes.Add("InvcHead.PEDischargeAmt`"
                       + originalInvcHead.PEDischargeAmt.ToString()
                       + "`"
                       + modifiedInvcHead.PEDischargeAmt.ToString().Replace("`", "-"));
        }

        // PE Discharge Date
        if (modifiedInvcHead.PEDischargeDate != originalInvcHead.PEDischargeDate)
        {
            changes.Add("InvcHead.PEDischargeDate`"
                       + originalInvcHead.PEDischargeDate.ToString()
                       + "`"
                       + modifiedInvcHead.PEDischargeDate.ToString().Replace("`", "-"));
        }

        // PE Interest
        if (modifiedInvcHead.PEInterest != originalInvcHead.PEInterest)
        {
            changes.Add("InvcHead.PEInterest`"
                       + originalInvcHead.PEInterest.ToString()
                       + "`"
                       + modifiedInvcHead.PEInterest.ToString().Replace("`", "-"));
        }

        // PE No Pay Penalty
        if (modifiedInvcHead.PENoPayPenalty != originalInvcHead.PENoPayPenalty)
        {
            changes.Add("InvcHead.PENoPayPenalty`"
                       + originalInvcHead.PENoPayPenalty.ToString()
                       + "`"
                       + modifiedInvcHead.PENoPayPenalty.ToString().Replace("`", "-"));
        }

        // PE SUNAT Deposit Amount
        if (modifiedInvcHead.PESUNATDepAmt != originalInvcHead.PESUNATDepAmt)
        {
            changes.Add("InvcHead.PESUNATDepAmt`"
                       + originalInvcHead.PESUNATDepAmt.ToString()
                       + "`"
                       + modifiedInvcHead.PESUNATDepAmt.ToString().Replace("`", "-"));
        }

        // PE SUNAT Deposit Date
        if (modifiedInvcHead.PESUNATDepDate != originalInvcHead.PESUNATDepDate)
        {
            changes.Add("InvcHead.PESUNATDepDate`"
                       + originalInvcHead.PESUNATDepDate.ToString()
                       + "`"
                       + modifiedInvcHead.PESUNATDepDate.ToString().Replace("`", "-"));
        }

        // PE SUNAT Deposit Number
        if (modifiedInvcHead.PESUNATDepNum != originalInvcHead.PESUNATDepNum)
        {
            changes.Add("InvcHead.PESUNATDepNum`"
                       + originalInvcHead.PESUNATDepNum.ToString()
                       + "`"
                       + modifiedInvcHead.PESUNATDepNum.ToString().Replace("`", "-"));
        }

        // Doc PE Interest
        if (modifiedInvcHead.DocPEInterest != originalInvcHead.DocPEInterest)
        {
            changes.Add("InvcHead.DocPEInterest`"
                       + originalInvcHead.DocPEInterest.ToString()
                       + "`"
                       + modifiedInvcHead.DocPEInterest.ToString().Replace("`", "-"));
        }

        // Doc PE Commissions
        if (modifiedInvcHead.DocPECommissions != originalInvcHead.DocPECommissions)
        {
            changes.Add("InvcHead.DocPECommissions`"
                       + originalInvcHead.DocPECommissions.ToString()
                       + "`"
                       + modifiedInvcHead.DocPECommissions.ToString().Replace("`", "-"));
        }

        // Doc PE Charges
        if (modifiedInvcHead.DocPECharges != originalInvcHead.DocPECharges)
        {
            changes.Add("InvcHead.DocPECharges`"
                       + originalInvcHead.DocPECharges.ToString()
                       + "`"
                       + modifiedInvcHead.DocPECharges.ToString().Replace("`", "-"));
        }

        // Doc PE No Pay Penalty
        if (modifiedInvcHead.DocPENoPayPenalty != originalInvcHead.DocPENoPayPenalty)
        {
            changes.Add("InvcHead.DocPENoPayPenalty`"
                       + originalInvcHead.DocPENoPayPenalty.ToString()
                       + "`"
                       + modifiedInvcHead.DocPENoPayPenalty.ToString().Replace("`", "-"));
        }

        // Doc PE Discharge Amount
        if (modifiedInvcHead.DocPEDischargeAmt != originalInvcHead.DocPEDischargeAmt)
        {
            changes.Add("InvcHead.DocPEDischargeAmt`"
                       + originalInvcHead.DocPEDischargeAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocPEDischargeAmt.ToString().Replace("`", "-"));
        }

        // Doc PE Detraction Tax Amount
        if (modifiedInvcHead.DocPEDetTaxAmt != originalInvcHead.DocPEDetTaxAmt)
        {
            changes.Add("InvcHead.DocPEDetTaxAmt`"
                       + originalInvcHead.DocPEDetTaxAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocPEDetTaxAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 PE Interest
        if (modifiedInvcHead.Rpt1PEInterest != originalInvcHead.Rpt1PEInterest)
        {
            changes.Add("InvcHead.Rpt1PEInterest`"
                       + originalInvcHead.Rpt1PEInterest.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1PEInterest.ToString().Replace("`", "-"));
        }

        // Rpt1 PE Commissions
        if (modifiedInvcHead.Rpt1PECommissions != originalInvcHead.Rpt1PECommissions)
        {
            changes.Add("InvcHead.Rpt1PECommissions`"
                       + originalInvcHead.Rpt1PECommissions.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1PECommissions.ToString().Replace("`", "-"));
        }

        // Rpt1 PE Charges
        if (modifiedInvcHead.Rpt1PECharges != originalInvcHead.Rpt1PECharges)
        {
            changes.Add("InvcHead.Rpt1PECharges`"
                       + originalInvcHead.Rpt1PECharges.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1PECharges.ToString().Replace("`", "-"));
        }

        // Rpt1 PE No Pay Penalty
        if (modifiedInvcHead.Rpt1PENoPayPenalty != originalInvcHead.Rpt1PENoPayPenalty)
        {
            changes.Add("InvcHead.Rpt1PENoPayPenalty`"
                       + originalInvcHead.Rpt1PENoPayPenalty.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1PENoPayPenalty.ToString().Replace("`", "-"));
        }

        // Rpt1 PE Discharge Amount
        if (modifiedInvcHead.Rpt1PEDischargeAmt != originalInvcHead.Rpt1PEDischargeAmt)
        {
            changes.Add("InvcHead.Rpt1PEDischargeAmt`"
                       + originalInvcHead.Rpt1PEDischargeAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1PEDischargeAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 PE Interest
        if (modifiedInvcHead.Rpt2PEInterest != originalInvcHead.Rpt2PEInterest)
        {
            changes.Add("InvcHead.Rpt2PEInterest`"
                       + originalInvcHead.Rpt2PEInterest.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2PEInterest.ToString().Replace("`", "-"));
        }

        // Rpt2 PE Commissions
        if (modifiedInvcHead.Rpt2PECommissions != originalInvcHead.Rpt2PECommissions)
        {
            changes.Add("InvcHead.Rpt2PECommissions`"
                       + originalInvcHead.Rpt2PECommissions.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2PECommissions.ToString().Replace("`", "-"));
        }

        // Rpt2 PE Charges
        if (modifiedInvcHead.Rpt2PECharges != originalInvcHead.Rpt2PECharges)
        {
            changes.Add("InvcHead.Rpt2PECharges`"
                       + originalInvcHead.Rpt2PECharges.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2PECharges.ToString().Replace("`", "-"));
        }

        // Rpt2 PE No Pay Penalty
        if (modifiedInvcHead.Rpt2PENoPayPenalty != originalInvcHead.Rpt2PENoPayPenalty)
        {
            changes.Add("InvcHead.Rpt2PENoPayPenalty`"
                       + originalInvcHead.Rpt2PENoPayPenalty.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2PENoPayPenalty.ToString().Replace("`", "-"));
        }

        // Rpt2 PE Discharge Amount
        if (modifiedInvcHead.Rpt2PEDischargeAmt != originalInvcHead.Rpt2PEDischargeAmt)
        {
            changes.Add("InvcHead.Rpt2PEDischargeAmt`"
                       + originalInvcHead.Rpt2PEDischargeAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2PEDischargeAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 PE Interest
        if (modifiedInvcHead.Rpt3PEInterest != originalInvcHead.Rpt3PEInterest)
        {
            changes.Add("InvcHead.Rpt3PEInterest`"
                       + originalInvcHead.Rpt3PEInterest.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3PEInterest.ToString().Replace("`", "-"));
        }

        // Rpt3 PE Commissions
        if (modifiedInvcHead.Rpt3PECommissions != originalInvcHead.Rpt3PECommissions)
        {
            changes.Add("InvcHead.Rpt3PECommissions`"
                       + originalInvcHead.Rpt3PECommissions.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3PECommissions.ToString().Replace("`", "-"));
        }

        // Rpt3 PE Charges
        if (modifiedInvcHead.Rpt3PECharges != originalInvcHead.Rpt3PECharges)
        {
            changes.Add("InvcHead.Rpt3PECharges`"
                       + originalInvcHead.Rpt3PECharges.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3PECharges.ToString().Replace("`", "-"));
        }

        // Rpt3 PE No Pay Penalty
        if (modifiedInvcHead.Rpt3PENoPayPenalty != originalInvcHead.Rpt3PENoPayPenalty)
        {
            changes.Add("InvcHead.Rpt3PENoPayPenalty`"
                       + originalInvcHead.Rpt3PENoPayPenalty.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3PENoPayPenalty.ToString().Replace("`", "-"));
        }

        // Rpt3 PE Discharge Amount
        if (modifiedInvcHead.Rpt3PEDischargeAmt != originalInvcHead.Rpt3PEDischargeAmt)
        {
            changes.Add("InvcHead.Rpt3PEDischargeAmt`"
                       + originalInvcHead.Rpt3PEDischargeAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3PEDischargeAmt.ToString().Replace("`", "-"));
        }

        // Our Supplier Code
        if (modifiedInvcHead.OurSupplierCode != originalInvcHead.OurSupplierCode)
        {
            changes.Add("InvcHead.OurSupplierCode`"
                       + originalInvcHead.OurSupplierCode.ToString()
                       + "`"
                       + modifiedInvcHead.OurSupplierCode.ToString().Replace("`", "-"));
        }

        // PE Guarantee Name
        if (modifiedInvcHead.PEGuaranteeName != originalInvcHead.PEGuaranteeName)
        {
            changes.Add("InvcHead.PEGuaranteeName`"
                       + originalInvcHead.PEGuaranteeName.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeName.ToString().Replace("`", "-"));
        }

        // PE Guarantee Address 1
        if (modifiedInvcHead.PEGuaranteeAddress1 != originalInvcHead.PEGuaranteeAddress1)
        {
            changes.Add("InvcHead.PEGuaranteeAddress1`"
                       + originalInvcHead.PEGuaranteeAddress1.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeAddress1.ToString().Replace("`", "-"));
        }

        // PE Guarantee Address 2
        if (modifiedInvcHead.PEGuaranteeAddress2 != originalInvcHead.PEGuaranteeAddress2)
        {
            changes.Add("InvcHead.PEGuaranteeAddress2`"
                       + originalInvcHead.PEGuaranteeAddress2.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeAddress2.ToString().Replace("`", "-"));
        }

        // PE Guarantee Address 3
        if (modifiedInvcHead.PEGuaranteeAddress3 != originalInvcHead.PEGuaranteeAddress3)
        {
            changes.Add("InvcHead.PEGuaranteeAddress3`"
                       + originalInvcHead.PEGuaranteeAddress3.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeAddress3.ToString().Replace("`", "-"));
        }

        // PE Guarantee City
        if (modifiedInvcHead.PEGuaranteeCity != originalInvcHead.PEGuaranteeCity)
        {
            changes.Add("InvcHead.PEGuaranteeCity`"
                       + originalInvcHead.PEGuaranteeCity.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeCity.ToString().Replace("`", "-"));
        }

        // PE Guarantee State
        if (modifiedInvcHead.PEGuaranteeState != originalInvcHead.PEGuaranteeState)
        {
            changes.Add("InvcHead.PEGuaranteeState`"
                       + originalInvcHead.PEGuaranteeState.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeState.ToString().Replace("`", "-"));
        }

        // PE Guarantee Zip
        if (modifiedInvcHead.PEGuaranteeZip != originalInvcHead.PEGuaranteeZip)
        {
            changes.Add("InvcHead.PEGuaranteeZip`"
                       + originalInvcHead.PEGuaranteeZip.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeZip.ToString().Replace("`", "-"));
        }

        // PE Guarantee Country
        if (modifiedInvcHead.PEGuaranteeCountry != originalInvcHead.PEGuaranteeCountry)
        {
            changes.Add("InvcHead.PEGuaranteeCountry`"
                       + originalInvcHead.PEGuaranteeCountry.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeCountry.ToString().Replace("`", "-"));
        }

        // PE Guarantee Tax ID
        if (modifiedInvcHead.PEGuaranteeTaxID != originalInvcHead.PEGuaranteeTaxID)
        {
            changes.Add("InvcHead.PEGuaranteeTaxID`"
                       + originalInvcHead.PEGuaranteeTaxID.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteeTaxID.ToString().Replace("`", "-"));
        }

        // PE Guarantee Phone Number
        if (modifiedInvcHead.PEGuaranteePhoneNum != originalInvcHead.PEGuaranteePhoneNum)
        {
            changes.Add("InvcHead.PEGuaranteePhoneNum`"
                       + originalInvcHead.PEGuaranteePhoneNum.ToString()
                       + "`"
                       + modifiedInvcHead.PEGuaranteePhoneNum.ToString().Replace("`", "-"));
        }

        // PE BOE Status
        if (modifiedInvcHead.PEBOEStatus != originalInvcHead.PEBOEStatus)
        {
            changes.Add("InvcHead.PEBOEStatus`"
                       + originalInvcHead.PEBOEStatus.ToString()
                       + "`"
                       + modifiedInvcHead.PEBOEStatus.ToString().Replace("`", "-"));
        }

        // PE BOE Is Multi Generation
        if (modifiedInvcHead.PEBOEIsMultiGen != originalInvcHead.PEBOEIsMultiGen)
        {
            changes.Add("InvcHead.PEBOEIsMultiGen`"
                       + originalInvcHead.PEBOEIsMultiGen.ToString()
                       + "`"
                       + modifiedInvcHead.PEBOEIsMultiGen.ToString().Replace("`", "-"));
        }

        // PE Reference Document ID
        if (modifiedInvcHead.PERefDocID != originalInvcHead.PERefDocID)
        {
            changes.Add("InvcHead.PERefDocID`"
                       + originalInvcHead.PERefDocID.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocID.ToString().Replace("`", "-"));
        }

        // PE Reason Code
        if (modifiedInvcHead.PEReasonCode != originalInvcHead.PEReasonCode)
        {
            changes.Add("InvcHead.PEReasonCode`"
                       + originalInvcHead.PEReasonCode.ToString()
                       + "`"
                       + modifiedInvcHead.PEReasonCode.ToString().Replace("`", "-"));
        }

        // PE Reason Description
        if (modifiedInvcHead.PEReasonDesc != originalInvcHead.PEReasonDesc)
        {
            changes.Add("InvcHead.PEReasonDesc`"
                       + originalInvcHead.PEReasonDesc.ToString()
                       + "`"
                       + modifiedInvcHead.PEReasonDesc.ToString().Replace("`", "-"));
        }

        // TW GUI Registration Number Seller
        if (modifiedInvcHead.TWGUIRegNumSeller != originalInvcHead.TWGUIRegNumSeller)
        {
            changes.Add("InvcHead.TWGUIRegNumSeller`"
                       + originalInvcHead.TWGUIRegNumSeller.ToString()
                       + "`"
                       + modifiedInvcHead.TWGUIRegNumSeller.ToString().Replace("`", "-"));
        }

        // TW GUI Registration Number Buyer
        if (modifiedInvcHead.TWGUIRegNumBuyer != originalInvcHead.TWGUIRegNumBuyer)
        {
            changes.Add("InvcHead.TWGUIRegNumBuyer`"
                       + originalInvcHead.TWGUIRegNumBuyer.ToString()
                       + "`"
                       + modifiedInvcHead.TWGUIRegNumBuyer.ToString().Replace("`", "-"));
        }

        // TW GUI Export Document Name
        if (modifiedInvcHead.TWGUIExportDocumentName != originalInvcHead.TWGUIExportDocumentName)
        {
            changes.Add("InvcHead.TWGUIExportDocumentName`"
                       + originalInvcHead.TWGUIExportDocumentName.ToString()
                       + "`"
                       + modifiedInvcHead.TWGUIExportDocumentName.ToString().Replace("`", "-"));
        }

        // TW GUI Export Remarks
        if (modifiedInvcHead.TWGUIExportRemarks != originalInvcHead.TWGUIExportRemarks)
        {
            changes.Add("InvcHead.TWGUIExportRemarks`"
                       + originalInvcHead.TWGUIExportRemarks.ToString()
                       + "`"
                       + modifiedInvcHead.TWGUIExportRemarks.ToString().Replace("`", "-"));
        }

        // TW GUI Export Verification
        if (modifiedInvcHead.TWGUIExportVerification != originalInvcHead.TWGUIExportVerification)
        {
            changes.Add("InvcHead.TWGUIExportVerification`"
                       + originalInvcHead.TWGUIExportVerification.ToString()
                       + "`"
                       + modifiedInvcHead.TWGUIExportVerification.ToString().Replace("`", "-"));
        }

        // PE Debit Note Reason Code
        if (modifiedInvcHead.PEDebitNoteReasonCode != originalInvcHead.PEDebitNoteReasonCode)
        {
            changes.Add("InvcHead.PEDebitNoteReasonCode`"
                       + originalInvcHead.PEDebitNoteReasonCode.ToString()
                       + "`"
                       + modifiedInvcHead.PEDebitNoteReasonCode.ToString().Replace("`", "-"));
        }

        // PE Debit Note
        if (modifiedInvcHead.PEDebitNote != originalInvcHead.PEDebitNote)
        {
            changes.Add("InvcHead.PEDebitNote`"
                       + originalInvcHead.PEDebitNote.ToString()
                       + "`"
                       + modifiedInvcHead.PEDebitNote.ToString().Replace("`", "-"));
        }

        // MX Partial Payment
        if (modifiedInvcHead.MXPartPmt != originalInvcHead.MXPartPmt)
        {
            changes.Add("InvcHead.MXPartPmt`"
                       + originalInvcHead.MXPartPmt.ToString()
                       + "`"
                       + modifiedInvcHead.MXPartPmt.ToString().Replace("`", "-"));
        }

        // CN Tax Invoice Type
        if (modifiedInvcHead.CNTaxInvoiceType != originalInvcHead.CNTaxInvoiceType)
        {
            changes.Add("InvcHead.CNTaxInvoiceType`"
                       + originalInvcHead.CNTaxInvoiceType.ToString()
                       + "`"
                       + modifiedInvcHead.CNTaxInvoiceType.ToString().Replace("`", "-"));
        }

        // MX Export Operation Type
        if (modifiedInvcHead.MXExportOperationType != originalInvcHead.MXExportOperationType)
        {
            changes.Add("InvcHead.MXExportOperationType`"
                       + originalInvcHead.MXExportOperationType.ToString()
                       + "`"
                       + modifiedInvcHead.MXExportOperationType.ToString().Replace("`", "-"));
        }

        // MX Export Customer Document Code
        if (modifiedInvcHead.MXExportCustDocCode != originalInvcHead.MXExportCustDocCode)
        {
            changes.Add("InvcHead.MXExportCustDocCode`"
                       + originalInvcHead.MXExportCustDocCode.ToString()
                       + "`"
                       + modifiedInvcHead.MXExportCustDocCode.ToString().Replace("`", "-"));
        }

        // MX Export Certificate Origin Number
        if (modifiedInvcHead.MXExportCertOriginNum != originalInvcHead.MXExportCertOriginNum)
        {
            changes.Add("InvcHead.MXExportCertOriginNum`"
                       + originalInvcHead.MXExportCertOriginNum.ToString()
                       + "`"
                       + modifiedInvcHead.MXExportCertOriginNum.ToString().Replace("`", "-"));
        }

        // MX Export Confirmation Number
        if (modifiedInvcHead.MXExportConfNum != originalInvcHead.MXExportConfNum)
        {
            changes.Add("InvcHead.MXExportConfNum`"
                       + originalInvcHead.MXExportConfNum.ToString()
                       + "`"
                       + modifiedInvcHead.MXExportConfNum.ToString().Replace("`", "-"));
        }

        // MX Export Certificate Origin
        if (modifiedInvcHead.MXExportCertOrigin != originalInvcHead.MXExportCertOrigin)
        {
            changes.Add("InvcHead.MXExportCertOrigin`"
                       + originalInvcHead.MXExportCertOrigin.ToString()
                       + "`"
                       + modifiedInvcHead.MXExportCertOrigin.ToString().Replace("`", "-"));
        }

        // MX Incoterm
        if (modifiedInvcHead.MXIncoterm != originalInvcHead.MXIncoterm)
        {
            changes.Add("InvcHead.MXIncoterm`"
                       + originalInvcHead.MXIncoterm.ToString()
                       + "`"
                       + modifiedInvcHead.MXIncoterm.ToString().Replace("`", "-"));
        }

        // AG Document Concept
        if (modifiedInvcHead.AGDocConcept != originalInvcHead.AGDocConcept)
        {
            changes.Add("InvcHead.AGDocConcept`"
                       + originalInvcHead.AGDocConcept.ToString()
                       + "`"
                       + modifiedInvcHead.AGDocConcept.ToString().Replace("`", "-"));
        }

        // E Invoice Reference Number
        if (modifiedInvcHead.EInvRefNum != originalInvcHead.EInvRefNum)
        {
            changes.Add("InvcHead.EInvRefNum`"
                       + originalInvcHead.EInvRefNum.ToString()
                       + "`"
                       + modifiedInvcHead.EInvRefNum.ToString().Replace("`", "-"));
        }

        // Export Document Reference Number
        if (modifiedInvcHead.ExportDocRefNum != originalInvcHead.ExportDocRefNum)
        {
            changes.Add("InvcHead.ExportDocRefNum`"
                       + originalInvcHead.ExportDocRefNum.ToString()
                       + "`"
                       + modifiedInvcHead.ExportDocRefNum.ToString().Replace("`", "-"));
        }

        // Export Document Date
        if (modifiedInvcHead.ExportDocDate != originalInvcHead.ExportDocDate)
        {
            changes.Add("InvcHead.ExportDocDate`"
                       + originalInvcHead.ExportDocDate.ToString()
                       + "`"
                       + modifiedInvcHead.ExportDocDate.ToString().Replace("`", "-"));
        }

        // IN Tax Transaction ID
        if (modifiedInvcHead.INTaxTransactionID != originalInvcHead.INTaxTransactionID)
        {
            changes.Add("InvcHead.INTaxTransactionID`"
                       + originalInvcHead.INTaxTransactionID.ToString()
                       + "`"
                       + modifiedInvcHead.INTaxTransactionID.ToString().Replace("`", "-"));
        }

        // MX Moving Reason Flag
        if (modifiedInvcHead.MXMovingReasonFlag != originalInvcHead.MXMovingReasonFlag)
        {
            changes.Add("InvcHead.MXMovingReasonFlag`"
                       + originalInvcHead.MXMovingReasonFlag.ToString()
                       + "`"
                       + modifiedInvcHead.MXMovingReasonFlag.ToString().Replace("`", "-"));
        }

        // MX Moving Reason
        if (modifiedInvcHead.MXMovingReason != originalInvcHead.MXMovingReason)
        {
            changes.Add("InvcHead.MXMovingReason`"
                       + originalInvcHead.MXMovingReason.ToString()
                       + "`"
                       + modifiedInvcHead.MXMovingReason.ToString().Replace("`", "-"));
        }

        // MX Number Registration ID Tributary
        if (modifiedInvcHead.MXNumRegIdTrib != originalInvcHead.MXNumRegIdTrib)
        {
            changes.Add("InvcHead.MXNumRegIdTrib`"
                       + originalInvcHead.MXNumRegIdTrib.ToString()
                       + "`"
                       + modifiedInvcHead.MXNumRegIdTrib.ToString().Replace("`", "-"));
        }

        // MX Residence Country Number
        if (modifiedInvcHead.MXResidenCountryNum != originalInvcHead.MXResidenCountryNum)
        {
            changes.Add("InvcHead.MXResidenCountryNum`"
                       + originalInvcHead.MXResidenCountryNum.ToString()
                       + "`"
                       + modifiedInvcHead.MXResidenCountryNum.ToString().Replace("`", "-"));
        }

        // MX Purchase Type
        if (modifiedInvcHead.MXPurchaseType != originalInvcHead.MXPurchaseType)
        {
            changes.Add("InvcHead.MXPurchaseType`"
                       + originalInvcHead.MXPurchaseType.ToString()
                       + "`"
                       + modifiedInvcHead.MXPurchaseType.ToString().Replace("`", "-"));
        }

        // MX Confirmation Code
        if (modifiedInvcHead.MXConfirmationCode != originalInvcHead.MXConfirmationCode)
        {
            changes.Add("InvcHead.MXConfirmationCode`"
                       + originalInvcHead.MXConfirmationCode.ToString()
                       + "`"
                       + modifiedInvcHead.MXConfirmationCode.ToString().Replace("`", "-"));
        }

        // MX External Code
        if (modifiedInvcHead.MXExternalCode != originalInvcHead.MXExternalCode)
        {
            changes.Add("InvcHead.MXExternalCode`"
                       + originalInvcHead.MXExternalCode.ToString()
                       + "`"
                       + modifiedInvcHead.MXExternalCode.ToString().Replace("`", "-"));
        }

        // Service Invoice
        if (modifiedInvcHead.ServiceInvoice != originalInvcHead.ServiceInvoice)
        {
            changes.Add("InvcHead.ServiceInvoice`"
                       + originalInvcHead.ServiceInvoice.ToString()
                       + "`"
                       + modifiedInvcHead.ServiceInvoice.ToString().Replace("`", "-"));
        }

        // MX Domestic Transfer
        if (modifiedInvcHead.MXDomesticTransfer != originalInvcHead.MXDomesticTransfer)
        {
            changes.Add("InvcHead.MXDomesticTransfer`"
                       + originalInvcHead.MXDomesticTransfer.ToString()
                       + "`"
                       + modifiedInvcHead.MXDomesticTransfer.ToString().Replace("`", "-"));
        }

        // MX Cancellation Mode
        if (modifiedInvcHead.MXCancellationMode != originalInvcHead.MXCancellationMode)
        {
            changes.Add("InvcHead.MXCancellationMode`"
                       + originalInvcHead.MXCancellationMode.ToString()
                       + "`"
                       + modifiedInvcHead.MXCancellationMode.ToString().Replace("`", "-"));
        }

        // IN Shipping Port Code
        if (modifiedInvcHead.INShippingPortCode != originalInvcHead.INShippingPortCode)
        {
            changes.Add("InvcHead.INShippingPortCode`"
                       + originalInvcHead.INShippingPortCode.ToString()
                       + "`"
                       + modifiedInvcHead.INShippingPortCode.ToString().Replace("`", "-"));
        }

        // IN Export Procedure
        if (modifiedInvcHead.INExportProcedure != originalInvcHead.INExportProcedure)
        {
            changes.Add("InvcHead.INExportProcedure`"
                       + originalInvcHead.INExportProcedure.ToString()
                       + "`"
                       + modifiedInvcHead.INExportProcedure.ToString().Replace("`", "-"));
        }

        // Created On
        if (modifiedInvcHead.CreatedOn != originalInvcHead.CreatedOn)
        {
            changes.Add("InvcHead.CreatedOn`"
                       + originalInvcHead.CreatedOn.ToString()
                       + "`"
                       + modifiedInvcHead.CreatedOn.ToString().Replace("`", "-"));
        }

        // Digital Signature
        if (modifiedInvcHead.DigitalSignature != originalInvcHead.DigitalSignature)
        {
            changes.Add("InvcHead.DigitalSignature`"
                       + originalInvcHead.DigitalSignature.ToString()
                       + "`"
                       + modifiedInvcHead.DigitalSignature.ToString().Replace("`", "-"));
        }

        // Signed On
        if (modifiedInvcHead.SignedOn != originalInvcHead.SignedOn)
        {
            changes.Add("InvcHead.SignedOn`"
                       + originalInvcHead.SignedOn.ToString()
                       + "`"
                       + modifiedInvcHead.SignedOn.ToString().Replace("`", "-"));
        }

        // Signed By
        if (modifiedInvcHead.SignedBy != originalInvcHead.SignedBy)
        {
            changes.Add("InvcHead.SignedBy`"
                       + originalInvcHead.SignedBy.ToString()
                       + "`"
                       + modifiedInvcHead.SignedBy.ToString().Replace("`", "-"));
        }

        // First Print Date
        if (modifiedInvcHead.FirstPrintDate != originalInvcHead.FirstPrintDate)
        {
            changes.Add("InvcHead.FirstPrintDate`"
                       + originalInvcHead.FirstPrintDate.ToString()
                       + "`"
                       + modifiedInvcHead.FirstPrintDate.ToString().Replace("`", "-"));
        }

        // Document Copy Number
        if (modifiedInvcHead.DocCopyNum != originalInvcHead.DocCopyNum)
        {
            changes.Add("InvcHead.DocCopyNum`"
                       + originalInvcHead.DocCopyNum.ToString()
                       + "`"
                       + modifiedInvcHead.DocCopyNum.ToString().Replace("`", "-"));
        }

        // Deposit Balance
        if (modifiedInvcHead.DepositBalance != originalInvcHead.DepositBalance)
        {
            changes.Add("InvcHead.DepositBalance`"
                       + originalInvcHead.DepositBalance.ToString()
                       + "`"
                       + modifiedInvcHead.DepositBalance.ToString().Replace("`", "-"));
        }

        // Doc Deposit Balance
        if (modifiedInvcHead.DocDepositBalance != originalInvcHead.DocDepositBalance)
        {
            changes.Add("InvcHead.DocDepositBalance`"
                       + originalInvcHead.DocDepositBalance.ToString()
                       + "`"
                       + modifiedInvcHead.DocDepositBalance.ToString().Replace("`", "-"));
        }

        // Rpt1 Deposit Balance
        if (modifiedInvcHead.Rpt1DepositBalance != originalInvcHead.Rpt1DepositBalance)
        {
            changes.Add("InvcHead.Rpt1DepositBalance`"
                       + originalInvcHead.Rpt1DepositBalance.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1DepositBalance.ToString().Replace("`", "-"));
        }

        // Rpt2 Deposit Balance
        if (modifiedInvcHead.Rpt2DepositBalance != originalInvcHead.Rpt2DepositBalance)
        {
            changes.Add("InvcHead.Rpt2DepositBalance`"
                       + originalInvcHead.Rpt2DepositBalance.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2DepositBalance.ToString().Replace("`", "-"));
        }

        // Rpt3 Deposit Balance
        if (modifiedInvcHead.Rpt3DepositBalance != originalInvcHead.Rpt3DepositBalance)
        {
            changes.Add("InvcHead.Rpt3DepositBalance`"
                       + originalInvcHead.Rpt3DepositBalance.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3DepositBalance.ToString().Replace("`", "-"));
        }

        // Quote Number
        if (modifiedInvcHead.QuoteNum != originalInvcHead.QuoteNum)
        {
            changes.Add("InvcHead.QuoteNum`"
                       + originalInvcHead.QuoteNum.ToString()
                       + "`"
                       + modifiedInvcHead.QuoteNum.ToString().Replace("`", "-"));
        }

        // HD Case Number
        if (modifiedInvcHead.HDCaseNum != originalInvcHead.HDCaseNum)
        {
            changes.Add("InvcHead.HDCaseNum`"
                       + originalInvcHead.HDCaseNum.ToString()
                       + "`"
                       + modifiedInvcHead.HDCaseNum.ToString().Replace("`", "-"));
        }

        // Credit Override
        if (modifiedInvcHead.CreditOverride != originalInvcHead.CreditOverride)
        {
            changes.Add("InvcHead.CreditOverride`"
                       + originalInvcHead.CreditOverride.ToString()
                       + "`"
                       + modifiedInvcHead.CreditOverride.ToString().Replace("`", "-"));
        }

        // Credit Override Date
        if (modifiedInvcHead.CreditOverrideDate != originalInvcHead.CreditOverrideDate)
        {
            changes.Add("InvcHead.CreditOverrideDate`"
                       + originalInvcHead.CreditOverrideDate.ToString()
                       + "`"
                       + modifiedInvcHead.CreditOverrideDate.ToString().Replace("`", "-"));
        }

        // Credit Override User ID
        if (modifiedInvcHead.CreditOverrideUserID != originalInvcHead.CreditOverrideUserID)
        {
            changes.Add("InvcHead.CreditOverrideUserID`"
                       + originalInvcHead.CreditOverrideUserID.ToString()
                       + "`"
                       + modifiedInvcHead.CreditOverrideUserID.ToString().Replace("`", "-"));
        }

        // Credit Hold
        if (modifiedInvcHead.CreditHold != originalInvcHead.CreditHold)
        {
            changes.Add("InvcHead.CreditHold`"
                       + originalInvcHead.CreditHold.ToString()
                       + "`"
                       + modifiedInvcHead.CreditHold.ToString().Replace("`", "-"));
        }

        // PE XML Type
        if (modifiedInvcHead.PEXMLType != originalInvcHead.PEXMLType)
        {
            changes.Add("InvcHead.PEXMLType`"
                       + originalInvcHead.PEXMLType.ToString()
                       + "`"
                       + modifiedInvcHead.PEXMLType.ToString().Replace("`", "-"));
        }

        // CO Credit Memo Reason Code
        if (modifiedInvcHead.COCreditMemoReasonCode != originalInvcHead.COCreditMemoReasonCode)
        {
            changes.Add("InvcHead.COCreditMemoReasonCode`"
                       + originalInvcHead.COCreditMemoReasonCode.ToString()
                       + "`"
                       + modifiedInvcHead.COCreditMemoReasonCode.ToString().Replace("`", "-"));
        }

        // CO Debit Memo Reason Code
        if (modifiedInvcHead.CODebitMemoReasonCode != originalInvcHead.CODebitMemoReasonCode)
        {
            changes.Add("InvcHead.CODebitMemoReasonCode`"
                       + originalInvcHead.CODebitMemoReasonCode.ToString()
                       + "`"
                       + modifiedInvcHead.CODebitMemoReasonCode.ToString().Replace("`", "-"));
        }

        // CO Reason Description
        if (modifiedInvcHead.COReasonDesc != originalInvcHead.COReasonDesc)
        {
            changes.Add("InvcHead.COReasonDesc`"
                       + originalInvcHead.COReasonDesc.ToString()
                       + "`"
                       + modifiedInvcHead.COReasonDesc.ToString().Replace("`", "-"));
        }

        // CO Debit Note
        if (modifiedInvcHead.CODebitNote != originalInvcHead.CODebitNote)
        {
            changes.Add("InvcHead.CODebitNote`"
                       + originalInvcHead.CODebitNote.ToString()
                       + "`"
                       + modifiedInvcHead.CODebitNote.ToString().Replace("`", "-"));
        }

        // PE Detraction Transaction Number
        if (modifiedInvcHead.PEDetractionTranNum != originalInvcHead.PEDetractionTranNum)
        {
            changes.Add("InvcHead.PEDetractionTranNum`"
                       + originalInvcHead.PEDetractionTranNum.ToString()
                       + "`"
                       + modifiedInvcHead.PEDetractionTranNum.ToString().Replace("`", "-"));
        }

        // PE Product Code
        if (modifiedInvcHead.PEProductCode != originalInvcHead.PEProductCode)
        {
            changes.Add("InvcHead.PEProductCode`"
                       + originalInvcHead.PEProductCode.ToString()
                       + "`"
                       + modifiedInvcHead.PEProductCode.ToString().Replace("`", "-"));
        }

        // PE Collection Group ID
        if (modifiedInvcHead.PECollectionGroupID != originalInvcHead.PECollectionGroupID)
        {
            changes.Add("InvcHead.PECollectionGroupID`"
                       + originalInvcHead.PECollectionGroupID.ToString()
                       + "`"
                       + modifiedInvcHead.PECollectionGroupID.ToString().Replace("`", "-"));
        }

        // PE Caption Code
        if (modifiedInvcHead.PECaptionCode != originalInvcHead.PECaptionCode)
        {
            changes.Add("InvcHead.PECaptionCode`"
                       + originalInvcHead.PECaptionCode.ToString()
                       + "`"
                       + modifiedInvcHead.PECaptionCode.ToString().Replace("`", "-"));
        }

        // PE Caption
        if (modifiedInvcHead.PECaption != originalInvcHead.PECaption)
        {
            changes.Add("InvcHead.PECaption`"
                       + originalInvcHead.PECaption.ToString()
                       + "`"
                       + modifiedInvcHead.PECaption.ToString().Replace("`", "-"));
        }

        // PE Reference Document Type
        if (modifiedInvcHead.PERefDocumentType != originalInvcHead.PERefDocumentType)
        {
            changes.Add("InvcHead.PERefDocumentType`"
                       + originalInvcHead.PERefDocumentType.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentType.ToString().Replace("`", "-"));
        }

        // PE Reference Document Number
        if (modifiedInvcHead.PERefDocumentNumber != originalInvcHead.PERefDocumentNumber)
        {
            changes.Add("InvcHead.PERefDocumentNumber`"
                       + originalInvcHead.PERefDocumentNumber.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentNumber.ToString().Replace("`", "-"));
        }

        // PE Detraction Good Service Code
        if (modifiedInvcHead.PEDetrGoodServiceCode != originalInvcHead.PEDetrGoodServiceCode)
        {
            changes.Add("InvcHead.PEDetrGoodServiceCode`"
                       + originalInvcHead.PEDetrGoodServiceCode.ToString()
                       + "`"
                       + modifiedInvcHead.PEDetrGoodServiceCode.ToString().Replace("`", "-"));
        }

        // PE Reference Document Type 2
        if (modifiedInvcHead.PERefDocumentType2 != originalInvcHead.PERefDocumentType2)
        {
            changes.Add("InvcHead.PERefDocumentType2`"
                       + originalInvcHead.PERefDocumentType2.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentType2.ToString().Replace("`", "-"));
        }

        // PE Reference Document Type 3
        if (modifiedInvcHead.PERefDocumentType3 != originalInvcHead.PERefDocumentType3)
        {
            changes.Add("InvcHead.PERefDocumentType3`"
                       + originalInvcHead.PERefDocumentType3.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentType3.ToString().Replace("`", "-"));
        }

        // PE Reference Document Type 4
        if (modifiedInvcHead.PERefDocumentType4 != originalInvcHead.PERefDocumentType4)
        {
            changes.Add("InvcHead.PERefDocumentType4`"
                       + originalInvcHead.PERefDocumentType4.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentType4.ToString().Replace("`", "-"));
        }

        // PE Reference Document Type 5
        if (modifiedInvcHead.PERefDocumentType5 != originalInvcHead.PERefDocumentType5)
        {
            changes.Add("InvcHead.PERefDocumentType5`"
                       + originalInvcHead.PERefDocumentType5.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentType5.ToString().Replace("`", "-"));
        }

        // PE Reference Document Number 2
        if (modifiedInvcHead.PERefDocumentNumber2 != originalInvcHead.PERefDocumentNumber2)
        {
            changes.Add("InvcHead.PERefDocumentNumber2`"
                       + originalInvcHead.PERefDocumentNumber2.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentNumber2.ToString().Replace("`", "-"));
        }

        // PE Reference Document Number 3
        if (modifiedInvcHead.PERefDocumentNumber3 != originalInvcHead.PERefDocumentNumber3)
        {
            changes.Add("InvcHead.PERefDocumentNumber3`"
                       + originalInvcHead.PERefDocumentNumber3.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentNumber3.ToString().Replace("`", "-"));
        }

        // PE Reference Document Number 4
        if (modifiedInvcHead.PERefDocumentNumber4 != originalInvcHead.PERefDocumentNumber4)
        {
            changes.Add("InvcHead.PERefDocumentNumber4`"
                       + originalInvcHead.PERefDocumentNumber4.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentNumber4.ToString().Replace("`", "-"));
        }

        // PE Reference Document Number 5
        if (modifiedInvcHead.PERefDocumentNumber5 != originalInvcHead.PERefDocumentNumber5)
        {
            changes.Add("InvcHead.PERefDocumentNumber5`"
                       + originalInvcHead.PERefDocumentNumber5.ToString()
                       + "`"
                       + modifiedInvcHead.PERefDocumentNumber5.ToString().Replace("`", "-"));
        }

        // ELI E Invoice
        if (modifiedInvcHead.ELIEInvoice != originalInvcHead.ELIEInvoice)
        {
            changes.Add("InvcHead.ELIEInvoice`"
                       + originalInvcHead.ELIEInvoice.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvoice.ToString().Replace("`", "-"));
        }

        // ELI E Invoice Status
        if (modifiedInvcHead.ELIEInvStatus != originalInvcHead.ELIEInvStatus)
        {
            changes.Add("InvcHead.ELIEInvStatus`"
                       + originalInvcHead.ELIEInvStatus.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvStatus.ToString().Replace("`", "-"));
        }

        // ELI E Invoice Updated By
        if (modifiedInvcHead.ELIEInvUpdatedBy != originalInvcHead.ELIEInvUpdatedBy)
        {
            changes.Add("InvcHead.ELIEInvUpdatedBy`"
                       + originalInvcHead.ELIEInvUpdatedBy.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvUpdatedBy.ToString().Replace("`", "-"));
        }

        // ELI E Invoice Exception
        if (modifiedInvcHead.ELIEInvException != originalInvcHead.ELIEInvException)
        {
            changes.Add("InvcHead.ELIEInvException`"
                       + originalInvcHead.ELIEInvException.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvException.ToString().Replace("`", "-"));
        }

        // ELI E Invoice Updated On
        if (modifiedInvcHead.ELIEInvUpdatedOn != originalInvcHead.ELIEInvUpdatedOn)
        {
            changes.Add("InvcHead.ELIEInvUpdatedOn`"
                       + originalInvcHead.ELIEInvUpdatedOn.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvUpdatedOn.ToString().Replace("`", "-"));
        }

        // CO Operation Type
        if (modifiedInvcHead.COOperType != originalInvcHead.COOperType)
        {
            changes.Add("InvcHead.COOperType`"
                       + originalInvcHead.COOperType.ToString()
                       + "`"
                       + modifiedInvcHead.COOperType.ToString().Replace("`", "-"));
        }

        // Central Collection
        if (modifiedInvcHead.CentralCollection != originalInvcHead.CentralCollection)
        {
            changes.Add("InvcHead.CentralCollection`"
                       + originalInvcHead.CentralCollection.ToString()
                       + "`"
                       + modifiedInvcHead.CentralCollection.ToString().Replace("`", "-"));
        }

        // Central Collection Child Company
        if (modifiedInvcHead.CColChildCompany != originalInvcHead.CColChildCompany)
        {
            changes.Add("InvcHead.CColChildCompany`"
                       + originalInvcHead.CColChildCompany.ToString()
                       + "`"
                       + modifiedInvcHead.CColChildCompany.ToString().Replace("`", "-"));
        }

        // Central Collection Parent Company
        if (modifiedInvcHead.CColParentCompany != originalInvcHead.CColParentCompany)
        {
            changes.Add("InvcHead.CColParentCompany`"
                       + originalInvcHead.CColParentCompany.ToString()
                       + "`"
                       + modifiedInvcHead.CColParentCompany.ToString().Replace("`", "-"));
        }

        // Central Collection Order Number
        if (modifiedInvcHead.CColOrderNum != originalInvcHead.CColOrderNum)
        {
            changes.Add("InvcHead.CColOrderNum`"
                       + originalInvcHead.CColOrderNum.ToString()
                       + "`"
                       + modifiedInvcHead.CColOrderNum.ToString().Replace("`", "-"));
        }

        // Central Collection Child Invoice Number
        if (modifiedInvcHead.CColChildInvoiceNum != originalInvcHead.CColChildInvoiceNum)
        {
            changes.Add("InvcHead.CColChildInvoiceNum`"
                       + originalInvcHead.CColChildInvoiceNum.ToString()
                       + "`"
                       + modifiedInvcHead.CColChildInvoiceNum.ToString().Replace("`", "-"));
        }

        // Central Collection Invoice Number
        if (modifiedInvcHead.CColInvoiceNum != originalInvcHead.CColInvoiceNum)
        {
            changes.Add("InvcHead.CColInvoiceNum`"
                       + originalInvcHead.CColInvoiceNum.ToString()
                       + "`"
                       + modifiedInvcHead.CColInvoiceNum.ToString().Replace("`", "-"));
        }

        // Central Collection Child Legal Number
        if (modifiedInvcHead.CColChildLegalNumber != originalInvcHead.CColChildLegalNumber)
        {
            changes.Add("InvcHead.CColChildLegalNumber`"
                       + originalInvcHead.CColChildLegalNumber.ToString()
                       + "`"
                       + modifiedInvcHead.CColChildLegalNumber.ToString().Replace("`", "-"));
        }

        // Central Collection Legal Number
        if (modifiedInvcHead.CColLegalNumber != originalInvcHead.CColLegalNumber)
        {
            changes.Add("InvcHead.CColLegalNumber`"
                       + originalInvcHead.CColLegalNumber.ToString()
                       + "`"
                       + modifiedInvcHead.CColLegalNumber.ToString().Replace("`", "-"));
        }

        // Central Collection Invoice Reference
        if (modifiedInvcHead.CColInvoiceRef != originalInvcHead.CColInvoiceRef)
        {
            changes.Add("InvcHead.CColInvoiceRef`"
                       + originalInvcHead.CColInvoiceRef.ToString()
                       + "`"
                       + modifiedInvcHead.CColInvoiceRef.ToString().Replace("`", "-"));
        }

        // Central Collection Invoice Balance
        if (modifiedInvcHead.CColInvBal != originalInvcHead.CColInvBal)
        {
            changes.Add("InvcHead.CColInvBal`"
                       + originalInvcHead.CColInvBal.ToString()
                       + "`"
                       + modifiedInvcHead.CColInvBal.ToString().Replace("`", "-"));
        }

        // Doc Central Collection Invoice Balance
        if (modifiedInvcHead.DocCColInvBal != originalInvcHead.DocCColInvBal)
        {
            changes.Add("InvcHead.DocCColInvBal`"
                       + originalInvcHead.DocCColInvBal.ToString()
                       + "`"
                       + modifiedInvcHead.DocCColInvBal.ToString().Replace("`", "-"));
        }

        // Central Collection Invoice Amount
        if (modifiedInvcHead.CColInvAmt != originalInvcHead.CColInvAmt)
        {
            changes.Add("InvcHead.CColInvAmt`"
                       + originalInvcHead.CColInvAmt.ToString()
                       + "`"
                       + modifiedInvcHead.CColInvAmt.ToString().Replace("`", "-"));
        }

        // Doc Central Collection Invoice Amount
        if (modifiedInvcHead.DocCColInvAmt != originalInvcHead.DocCColInvAmt)
        {
            changes.Add("InvcHead.DocCColInvAmt`"
                       + originalInvcHead.DocCColInvAmt.ToString()
                       + "`"
                       + modifiedInvcHead.DocCColInvAmt.ToString().Replace("`", "-"));
        }

        // Rpt1 Central Collection Invoice Balance
        if (modifiedInvcHead.Rpt1CColInvBal != originalInvcHead.Rpt1CColInvBal)
        {
            changes.Add("InvcHead.Rpt1CColInvBal`"
                       + originalInvcHead.Rpt1CColInvBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1CColInvBal.ToString().Replace("`", "-"));
        }

        // Rpt2 Central Collection Invoice Balance
        if (modifiedInvcHead.Rpt2CColInvBal != originalInvcHead.Rpt2CColInvBal)
        {
            changes.Add("InvcHead.Rpt2CColInvBal`"
                       + originalInvcHead.Rpt2CColInvBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2CColInvBal.ToString().Replace("`", "-"));
        }

        // Rpt3 Central Collection Invoice Balance
        if (modifiedInvcHead.Rpt3CColInvBal != originalInvcHead.Rpt3CColInvBal)
        {
            changes.Add("InvcHead.Rpt3CColInvBal`"
                       + originalInvcHead.Rpt3CColInvBal.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3CColInvBal.ToString().Replace("`", "-"));
        }

        // Rpt1 Central Collection Invoice Amount
        if (modifiedInvcHead.Rpt1CColInvAmt != originalInvcHead.Rpt1CColInvAmt)
        {
            changes.Add("InvcHead.Rpt1CColInvAmt`"
                       + originalInvcHead.Rpt1CColInvAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1CColInvAmt.ToString().Replace("`", "-"));
        }

        // Rpt2 Central Collection Invoice Amount
        if (modifiedInvcHead.Rpt2CColInvAmt != originalInvcHead.Rpt2CColInvAmt)
        {
            changes.Add("InvcHead.Rpt2CColInvAmt`"
                       + originalInvcHead.Rpt2CColInvAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2CColInvAmt.ToString().Replace("`", "-"));
        }

        // Rpt3 Central Collection Invoice Amount
        if (modifiedInvcHead.Rpt3CColInvAmt != originalInvcHead.Rpt3CColInvAmt)
        {
            changes.Add("InvcHead.Rpt3CColInvAmt`"
                       + originalInvcHead.Rpt3CColInvAmt.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3CColInvAmt.ToString().Replace("`", "-"));
        }

        // Netting ID
        if (modifiedInvcHead.NettingID != originalInvcHead.NettingID)
        {
            changes.Add("InvcHead.NettingID`"
                       + originalInvcHead.NettingID.ToString()
                       + "`"
                       + modifiedInvcHead.NettingID.ToString().Replace("`", "-"));
        }

        // ELI E Invoice Terminal Name
        if (modifiedInvcHead.ELIEInvTerminalName != originalInvcHead.ELIEInvTerminalName)
        {
            changes.Add("InvcHead.ELIEInvTerminalName`"
                       + originalInvcHead.ELIEInvTerminalName.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvTerminalName.ToString().Replace("`", "-"));
        }

        // ELI E Invoice Terminal IP
        if (modifiedInvcHead.ELIEInvTerminalIP != originalInvcHead.ELIEInvTerminalIP)
        {
            changes.Add("InvcHead.ELIEInvTerminalIP`"
                       + originalInvcHead.ELIEInvTerminalIP.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvTerminalIP.ToString().Replace("`", "-"));
        }

        // Description
        if (modifiedInvcHead.Description != originalInvcHead.Description)
        {
            changes.Add("InvcHead.Description`"
                       + originalInvcHead.Description.ToString()
                       + "`"
                       + modifiedInvcHead.Description.ToString().Replace("`", "-"));
        }

        // Withhold Account To Interim
        if (modifiedInvcHead.WithholdAcctToInterim != originalInvcHead.WithholdAcctToInterim)
        {
            changes.Add("InvcHead.WithholdAcctToInterim`"
                       + originalInvcHead.WithholdAcctToInterim.ToString()
                       + "`"
                       + modifiedInvcHead.WithholdAcctToInterim.ToString().Replace("`", "-"));
        }

        // Central Collection Open Invoice
        if (modifiedInvcHead.CColOpenInvoice != originalInvcHead.CColOpenInvoice)
        {
            changes.Add("InvcHead.CColOpenInvoice`"
                       + originalInvcHead.CColOpenInvoice.ToString()
                       + "`"
                       + modifiedInvcHead.CColOpenInvoice.ToString().Replace("`", "-"));
        }

        // AG QR Code Data
        if (modifiedInvcHead.AGQRCodeData != originalInvcHead.AGQRCodeData)
        {
            changes.Add("InvcHead.AGQRCodeData`"
                       + originalInvcHead.AGQRCodeData.ToString()
                       + "`"
                       + modifiedInvcHead.AGQRCodeData.ToString().Replace("`", "-"));
        }

        // Exempt Reason Code
        if (modifiedInvcHead.ExemptReasonCode != originalInvcHead.ExemptReasonCode)
        {
            changes.Add("InvcHead.ExemptReasonCode`"
                       + originalInvcHead.ExemptReasonCode.ToString()
                       + "`"
                       + modifiedInvcHead.ExemptReasonCode.ToString().Replace("`", "-"));
        }

        // ELI E Invoice ID
        if (modifiedInvcHead.ELIEInvID != originalInvcHead.ELIEInvID)
        {
            changes.Add("InvcHead.ELIEInvID`"
                       + originalInvcHead.ELIEInvID.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvID.ToString().Replace("`", "-"));
        }

        // Call Number
        if (modifiedInvcHead.CallNum != originalInvcHead.CallNum)
        {
            changes.Add("InvcHead.CallNum`"
                       + originalInvcHead.CallNum.ToString()
                       + "`"
                       + modifiedInvcHead.CallNum.ToString().Replace("`", "-"));
        }

        // Call Line
        if (modifiedInvcHead.CallLine != originalInvcHead.CallLine)
        {
            changes.Add("InvcHead.CallLine`"
                       + originalInvcHead.CallLine.ToString()
                       + "`"
                       + modifiedInvcHead.CallLine.ToString().Replace("`", "-"));
        }

        // Job Number
        if (modifiedInvcHead.JobNum != originalInvcHead.JobNum)
        {
            changes.Add("InvcHead.JobNum`"
                       + originalInvcHead.JobNum.ToString()
                       + "`"
                       + modifiedInvcHead.JobNum.ToString().Replace("`", "-"));
        }

        // MX Cancel Reason Code
        if (modifiedInvcHead.MXCancelReasonCode != originalInvcHead.MXCancelReasonCode)
        {
            changes.Add("InvcHead.MXCancelReasonCode`"
                       + originalInvcHead.MXCancelReasonCode.ToString()
                       + "`"
                       + modifiedInvcHead.MXCancelReasonCode.ToString().Replace("`", "-"));
        }

        // MX Substitute Invoice Number
        if (modifiedInvcHead.MXSubstInvoiceNum != originalInvcHead.MXSubstInvoiceNum)
        {
            changes.Add("InvcHead.MXSubstInvoiceNum`"
                       + originalInvcHead.MXSubstInvoiceNum.ToString()
                       + "`"
                       + modifiedInvcHead.MXSubstInvoiceNum.ToString().Replace("`", "-"));
        }

        // MX Export Type
        if (modifiedInvcHead.MXExportType != originalInvcHead.MXExportType)
        {
            changes.Add("InvcHead.MXExportType`"
                       + originalInvcHead.MXExportType.ToString()
                       + "`"
                       + modifiedInvcHead.MXExportType.ToString().Replace("`", "-"));
        }

        // MX Global Invoice Period
        if (modifiedInvcHead.MXGlobalInvoicePeriod != originalInvcHead.MXGlobalInvoicePeriod)
        {
            changes.Add("InvcHead.MXGlobalInvoicePeriod`"
                       + originalInvcHead.MXGlobalInvoicePeriod.ToString()
                       + "`"
                       + modifiedInvcHead.MXGlobalInvoicePeriod.ToString().Replace("`", "-"));
        }

        // MX Global Invoice Month
        if (modifiedInvcHead.MXGlobalInvoiceMonth != originalInvcHead.MXGlobalInvoiceMonth)
        {
            changes.Add("InvcHead.MXGlobalInvoiceMonth`"
                       + originalInvcHead.MXGlobalInvoiceMonth.ToString()
                       + "`"
                       + modifiedInvcHead.MXGlobalInvoiceMonth.ToString().Replace("`", "-"));
        }

        // ELI E Invoice Service Provider Status
        if (modifiedInvcHead.ELIEInvServiceProviderStatus != originalInvcHead.ELIEInvServiceProviderStatus)
        {
            changes.Add("InvcHead.ELIEInvServiceProviderStatus`"
                       + originalInvcHead.ELIEInvServiceProviderStatus.ToString()
                       + "`"
                       + modifiedInvcHead.ELIEInvServiceProviderStatus.ToString().Replace("`", "-"));
        }

        // Incoterm Code
        if (modifiedInvcHead.IncotermCode != originalInvcHead.IncotermCode)
        {
            changes.Add("InvcHead.IncotermCode`"
                       + originalInvcHead.IncotermCode.ToString()
                       + "`"
                       + modifiedInvcHead.IncotermCode.ToString().Replace("`", "-"));
        }

        // Incoterm Location
        if (modifiedInvcHead.IncotermLocation != originalInvcHead.IncotermLocation)
        {
            changes.Add("InvcHead.IncotermLocation`"
                       + originalInvcHead.IncotermLocation.ToString()
                       + "`"
                       + modifiedInvcHead.IncotermLocation.ToString().Replace("`", "-"));
        }

        // Covenant Discount Percent
        if (modifiedInvcHead.CovenantDiscPercent != originalInvcHead.CovenantDiscPercent)
        {
            changes.Add("InvcHead.CovenantDiscPercent`"
                       + originalInvcHead.CovenantDiscPercent.ToString()
                       + "`"
                       + modifiedInvcHead.CovenantDiscPercent.ToString().Replace("`", "-"));
        }

        // Total Covenant Discount
        if (modifiedInvcHead.TotalCovenantDiscount != originalInvcHead.TotalCovenantDiscount)
        {
            changes.Add("InvcHead.TotalCovenantDiscount`"
                       + originalInvcHead.TotalCovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.TotalCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Doc Covenant Discount
        if (modifiedInvcHead.DocCovenantDiscount != originalInvcHead.DocCovenantDiscount)
        {
            changes.Add("InvcHead.DocCovenantDiscount`"
                       + originalInvcHead.DocCovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.DocCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt1 Covenant Discount
        if (modifiedInvcHead.Rpt1CovenantDiscount != originalInvcHead.Rpt1CovenantDiscount)
        {
            changes.Add("InvcHead.Rpt1CovenantDiscount`"
                       + originalInvcHead.Rpt1CovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1CovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt2 Covenant Discount
        if (modifiedInvcHead.Rpt2CovenantDiscount != originalInvcHead.Rpt2CovenantDiscount)
        {
            changes.Add("InvcHead.Rpt2CovenantDiscount`"
                       + originalInvcHead.Rpt2CovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2CovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt3 Covenant Discount
        if (modifiedInvcHead.Rpt3CovenantDiscount != originalInvcHead.Rpt3CovenantDiscount)
        {
            changes.Add("InvcHead.Rpt3CovenantDiscount`"
                       + originalInvcHead.Rpt3CovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3CovenantDiscount.ToString().Replace("`", "-"));
        }

        // Total In Covenant Discount
        if (modifiedInvcHead.TotalInCovenantDiscount != originalInvcHead.TotalInCovenantDiscount)
        {
            changes.Add("InvcHead.TotalInCovenantDiscount`"
                       + originalInvcHead.TotalInCovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.TotalInCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Doc In Covenant Discount
        if (modifiedInvcHead.DocInCovenantDiscount != originalInvcHead.DocInCovenantDiscount)
        {
            changes.Add("InvcHead.DocInCovenantDiscount`"
                       + originalInvcHead.DocInCovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.DocInCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt1 In Covenant Discount
        if (modifiedInvcHead.Rpt1InCovenantDiscount != originalInvcHead.Rpt1InCovenantDiscount)
        {
            changes.Add("InvcHead.Rpt1InCovenantDiscount`"
                       + originalInvcHead.Rpt1InCovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt1InCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt2 In Covenant Discount
        if (modifiedInvcHead.Rpt2InCovenantDiscount != originalInvcHead.Rpt2InCovenantDiscount)
        {
            changes.Add("InvcHead.Rpt2InCovenantDiscount`"
                       + originalInvcHead.Rpt2InCovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt2InCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt3 In Covenant Discount
        if (modifiedInvcHead.Rpt3InCovenantDiscount != originalInvcHead.Rpt3InCovenantDiscount)
        {
            changes.Add("InvcHead.Rpt3InCovenantDiscount`"
                       + originalInvcHead.Rpt3InCovenantDiscount.ToString()
                       + "`"
                       + modifiedInvcHead.Rpt3InCovenantDiscount.ToString().Replace("`", "-"));
        }

        // MX Observations
        if (modifiedInvcHead.MXObservations != originalInvcHead.MXObservations)
        {
            changes.Add("InvcHead.MXObservations`"
                       + originalInvcHead.MXObservations.ToString()
                       + "`"
                       + modifiedInvcHead.MXObservations.ToString().Replace("`", "-"));
        }

        // MX Original String TFD
        if (modifiedInvcHead.MXOriginalStringTFD != originalInvcHead.MXOriginalStringTFD)
        {
            changes.Add("InvcHead.MXOriginalStringTFD`"
                       + originalInvcHead.MXOriginalStringTFD.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalStringTFD.ToString().Replace("`", "-"));
        }

        // MX Payment Number
        if (modifiedInvcHead.MXPaymentNum != originalInvcHead.MXPaymentNum)
        {
            changes.Add("InvcHead.MXPaymentNum`"
                       + originalInvcHead.MXPaymentNum.ToString()
                       + "`"
                       + modifiedInvcHead.MXPaymentNum.ToString().Replace("`", "-"));
        }

        // MX Paid As
        if (modifiedInvcHead.MXPaidAs != originalInvcHead.MXPaidAs)
        {
            changes.Add("InvcHead.MXPaidAs`"
                       + originalInvcHead.MXPaidAs.ToString()
                       + "`"
                       + modifiedInvcHead.MXPaidAs.ToString().Replace("`", "-"));
        }

        // MX Certificate Serial Number
        if (modifiedInvcHead.MXCertificateSN != originalInvcHead.MXCertificateSN)
        {
            changes.Add("InvcHead.MXCertificateSN`"
                       + originalInvcHead.MXCertificateSN.ToString()
                       + "`"
                       + modifiedInvcHead.MXCertificateSN.ToString().Replace("`", "-"));
        }

        // MX Original Amount
        if (modifiedInvcHead.MXOriginalAmount != originalInvcHead.MXOriginalAmount)
        {
            changes.Add("InvcHead.MXOriginalAmount`"
                       + originalInvcHead.MXOriginalAmount.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalAmount.ToString().Replace("`", "-"));
        }

        // MX Account Number
        if (modifiedInvcHead.MXAccountNumber != originalInvcHead.MXAccountNumber)
        {
            changes.Add("InvcHead.MXAccountNumber`"
                       + originalInvcHead.MXAccountNumber.ToString()
                       + "`"
                       + modifiedInvcHead.MXAccountNumber.ToString().Replace("`", "-"));
        }

        // MX Original Date
        if (modifiedInvcHead.MXOriginalDate != originalInvcHead.MXOriginalDate)
        {
            changes.Add("InvcHead.MXOriginalDate`"
                       + originalInvcHead.MXOriginalDate.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalDate.ToString().Replace("`", "-"));
        }

        // MX Original Series
        if (modifiedInvcHead.MXOriginalSeries != originalInvcHead.MXOriginalSeries)
        {
            changes.Add("InvcHead.MXOriginalSeries`"
                       + originalInvcHead.MXOriginalSeries.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalSeries.ToString().Replace("`", "-"));
        }

        // MX Original Folio
        if (modifiedInvcHead.MXOriginalFolio != originalInvcHead.MXOriginalFolio)
        {
            changes.Add("InvcHead.MXOriginalFolio`"
                       + originalInvcHead.MXOriginalFolio.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalFolio.ToString().Replace("`", "-"));
        }

        // MX Tax Regime
        if (modifiedInvcHead.MXTaxRegime != originalInvcHead.MXTaxRegime)
        {
            changes.Add("InvcHead.MXTaxRegime`"
                       + originalInvcHead.MXTaxRegime.ToString()
                       + "`"
                       + modifiedInvcHead.MXTaxRegime.ToString().Replace("`", "-"));
        }

        // MX Original String
        if (modifiedInvcHead.MXOriginalString != originalInvcHead.MXOriginalString)
        {
            changes.Add("InvcHead.MXOriginalString`"
                       + originalInvcHead.MXOriginalString.ToString()
                       + "`"
                       + modifiedInvcHead.MXOriginalString.ToString().Replace("`", "-"));
        }

        // MX Payment Name
        if (modifiedInvcHead.MXPaymentName != originalInvcHead.MXPaymentName)
        {
            changes.Add("InvcHead.MXPaymentName`"
                       + originalInvcHead.MXPaymentName.ToString()
                       + "`"
                       + modifiedInvcHead.MXPaymentName.ToString().Replace("`", "-"));
        }

        // E Invoice
        if (modifiedInvcHead.EInvoice != originalInvcHead.EInvoice)
        {
            changes.Add("InvcHead.EInvoice`"
                       + originalInvcHead.EInvoice.ToString()
                       + "`"
                       + modifiedInvcHead.EInvoice.ToString().Replace("`", "-"));
        }

        // E Invoice Status
        if (modifiedInvcHead.EInvStatus != originalInvcHead.EInvStatus)
        {
            changes.Add("InvcHead.EInvStatus`"
                       + originalInvcHead.EInvStatus.ToString()
                       + "`"
                       + modifiedInvcHead.EInvStatus.ToString().Replace("`", "-"));
        }

        // E Invoice Timestamp
        if (modifiedInvcHead.EInvTimestamp != originalInvcHead.EInvTimestamp)
        {
            changes.Add("InvcHead.EInvTimestamp`"
                       + originalInvcHead.EInvTimestamp.ToString()
                       + "`"
                       + modifiedInvcHead.EInvTimestamp.ToString().Replace("`", "-"));
        }

        // E Invoice Updated By
        if (modifiedInvcHead.EInvUpdatedBy != originalInvcHead.EInvUpdatedBy)
        {
            changes.Add("InvcHead.EInvUpdatedBy`"
                       + originalInvcHead.EInvUpdatedBy.ToString()
                       + "`"
                       + modifiedInvcHead.EInvUpdatedBy.ToString().Replace("`", "-"));
        }

        // E Invoice Exception
        if (modifiedInvcHead.EInvException != originalInvcHead.EInvException)
        {
            changes.Add("InvcHead.EInvException`"
                       + originalInvcHead.EInvException.ToString()
                       + "`"
                       + modifiedInvcHead.EInvException.ToString().Replace("`", "-"));
        }

        // With Tax Confirm
        if (modifiedInvcHead.WithTaxConfirm != originalInvcHead.WithTaxConfirm)
        {
            changes.Add("InvcHead.WithTaxConfirm`"
                       + originalInvcHead.WithTaxConfirm.ToString()
                       + "`"
                       + modifiedInvcHead.WithTaxConfirm.ToString().Replace("`", "-"));
        }

        // Use Alt Bill To ID
        if (modifiedInvcHead.UseAltBillToID != originalInvcHead.UseAltBillToID)
        {
            changes.Add("InvcHead.UseAltBillToID`"
                       + originalInvcHead.UseAltBillToID.ToString()
                       + "`"
                       + modifiedInvcHead.UseAltBillToID.ToString().Replace("`", "-"));
        }

    }
}

// =================================================================
// Process InvcDtl Changes
// =================================================================

// Check if InvcDtl data exists
if (ds.InvcDtl != null && ds.InvcDtl.Count > 0)
{
    // Process each InvcDtl record
    for (int i = 0; i < ds.InvcDtl.Count; i++)
    {
        var modifiedDtl = ds.InvcDtl[i];
        int dtlInvoiceNum = modifiedDtl.InvoiceNum;
        int invoiceLine = modifiedDtl.InvoiceLine;

        // Check if the detail row has been deleted
        if (modifiedDtl.RowMod == "D")
        {
            changes.Add($"InvcDtl deleted: Line {invoiceLine}");
            callFunc = true;
            continue;
        }

        // Get the original detail record from database
        var originalDtl = (from dbDtl in Db.InvcDtl
                          where dbDtl.Company == companyID
                             && dbDtl.InvoiceNum == dtlInvoiceNum
                             && dbDtl.InvoiceLine == invoiceLine
                          select dbDtl).FirstOrDefault();

        // Handle new detail creation
        if (originalDtl == null)
        {
            changes.Add($"New invoice detail created: Line {invoiceLine}");
            callFunc = true;
            continue;
        }

        // =================================================================
        // InvcDtl Field Comparisons
        // =================================================================

        // Company
        if (modifiedDtl.Company != originalDtl.Company)
        {
            changes.Add($"InvcDtl.Company[Line {invoiceLine}]`"
                       + originalDtl.Company.ToString()
                       + "`"
                       + modifiedDtl.Company.ToString().Replace("`", "-"));
        }

        // InvoiceNum
        if (modifiedDtl.InvoiceNum != originalDtl.InvoiceNum)
        {
            changes.Add($"InvcDtl.InvoiceNum[Line {invoiceLine}]`"
                       + originalDtl.InvoiceNum.ToString()
                       + "`"
                       + modifiedDtl.InvoiceNum.ToString().Replace("`", "-"));
        }

        // InvoiceLine
        if (modifiedDtl.InvoiceLine != originalDtl.InvoiceLine)
        {
            changes.Add($"InvcDtl.InvoiceLine[Line {invoiceLine}]`"
                       + originalDtl.InvoiceLine.ToString()
                       + "`"
                       + modifiedDtl.InvoiceLine.ToString().Replace("`", "-"));
        }

        // LineType
        if (modifiedDtl.LineType != originalDtl.LineType)
        {
            changes.Add($"InvcDtl.LineType[Line {invoiceLine}]`"
                       + originalDtl.LineType.ToString()
                       + "`"
                       + modifiedDtl.LineType.ToString().Replace("`", "-"));
        }

        // ContractNum
        if (modifiedDtl.ContractNum != originalDtl.ContractNum)
        {
            changes.Add($"InvcDtl.ContractNum[Line {invoiceLine}]`"
                       + originalDtl.ContractNum.ToString()
                       + "`"
                       + modifiedDtl.ContractNum.ToString().Replace("`", "-"));
        }

        // XPartNum
        if (modifiedDtl.XPartNum != originalDtl.XPartNum)
        {
            changes.Add($"InvcDtl.XPartNum[Line {invoiceLine}]`"
                       + originalDtl.XPartNum.ToString()
                       + "`"
                       + modifiedDtl.XPartNum.ToString().Replace("`", "-"));
        }

        // XRevisionNum
        if (modifiedDtl.XRevisionNum != originalDtl.XRevisionNum)
        {
            changes.Add($"InvcDtl.XRevisionNum[Line {invoiceLine}]`"
                       + originalDtl.XRevisionNum.ToString()
                       + "`"
                       + modifiedDtl.XRevisionNum.ToString().Replace("`", "-"));
        }

        // PartNum
        if (modifiedDtl.PartNum != originalDtl.PartNum)
        {
            changes.Add($"InvcDtl.PartNum[Line {invoiceLine}]`"
                       + originalDtl.PartNum.ToString()
                       + "`"
                       + modifiedDtl.PartNum.ToString().Replace("`", "-"));
        }

        // LineDesc
        if (modifiedDtl.LineDesc != originalDtl.LineDesc)
        {
            changes.Add($"InvcDtl.LineDesc[Line {invoiceLine}]`"
                       + originalDtl.LineDesc.ToString()
                       + "`"
                       + modifiedDtl.LineDesc.ToString().Replace("`", "-"));
        }

        // IUM
        if (modifiedDtl.IUM != originalDtl.IUM)
        {
            changes.Add($"InvcDtl.IUM[Line {invoiceLine}]`"
                       + originalDtl.IUM.ToString()
                       + "`"
                       + modifiedDtl.IUM.ToString().Replace("`", "-"));
        }

        // RevisionNum
        if (modifiedDtl.RevisionNum != originalDtl.RevisionNum)
        {
            changes.Add($"InvcDtl.RevisionNum[Line {invoiceLine}]`"
                       + originalDtl.RevisionNum.ToString()
                       + "`"
                       + modifiedDtl.RevisionNum.ToString().Replace("`", "-"));
        }

        // POLine
        if (modifiedDtl.POLine != originalDtl.POLine)
        {
            changes.Add($"InvcDtl.POLine[Line {invoiceLine}]`"
                       + originalDtl.POLine.ToString()
                       + "`"
                       + modifiedDtl.POLine.ToString().Replace("`", "-"));
        }

        // TaxExempt
        if (modifiedDtl.TaxExempt != originalDtl.TaxExempt)
        {
            changes.Add($"InvcDtl.TaxExempt[Line {invoiceLine}]`"
                       + originalDtl.TaxExempt.ToString()
                       + "`"
                       + modifiedDtl.TaxExempt.ToString().Replace("`", "-"));
        }

        // TaxCatID
        if (modifiedDtl.TaxCatID != originalDtl.TaxCatID)
        {
            changes.Add($"InvcDtl.TaxCatID[Line {invoiceLine}]`"
                       + originalDtl.TaxCatID.ToString()
                       + "`"
                       + modifiedDtl.TaxCatID.ToString().Replace("`", "-"));
        }

        // Commissionable
        if (modifiedDtl.Commissionable != originalDtl.Commissionable)
        {
            changes.Add($"InvcDtl.Commissionable[Line {invoiceLine}]`"
                       + originalDtl.Commissionable.ToString()
                       + "`"
                       + modifiedDtl.Commissionable.ToString().Replace("`", "-"));
        }

        // DiscountPercent
        if (modifiedDtl.DiscountPercent != originalDtl.DiscountPercent)
        {
            changes.Add($"InvcDtl.DiscountPercent[Line {invoiceLine}]`"
                       + originalDtl.DiscountPercent.ToString()
                       + "`"
                       + modifiedDtl.DiscountPercent.ToString().Replace("`", "-"));
        }

        // UnitPrice
        if (modifiedDtl.UnitPrice != originalDtl.UnitPrice)
        {
            changes.Add($"InvcDtl.UnitPrice[Line {invoiceLine}]`"
                       + originalDtl.UnitPrice.ToString()
                       + "`"
                       + modifiedDtl.UnitPrice.ToString().Replace("`", "-"));
        }

        // DocUnitPrice
        if (modifiedDtl.DocUnitPrice != originalDtl.DocUnitPrice)
        {
            changes.Add($"InvcDtl.DocUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.DocUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.DocUnitPrice.ToString().Replace("`", "-"));
        }

        // PricePerCode
        if (modifiedDtl.PricePerCode != originalDtl.PricePerCode)
        {
            changes.Add($"InvcDtl.PricePerCode[Line {invoiceLine}]`"
                       + originalDtl.PricePerCode.ToString()
                       + "`"
                       + modifiedDtl.PricePerCode.ToString().Replace("`", "-"));
        }

        // OurOrderQty
        if (modifiedDtl.OurOrderQty != originalDtl.OurOrderQty)
        {
            changes.Add($"InvcDtl.OurOrderQty[Line {invoiceLine}]`"
                       + originalDtl.OurOrderQty.ToString()
                       + "`"
                       + modifiedDtl.OurOrderQty.ToString().Replace("`", "-"));
        }

        // ExtPrice
        if (modifiedDtl.ExtPrice != originalDtl.ExtPrice)
        {
            changes.Add($"InvcDtl.ExtPrice[Line {invoiceLine}]`"
                       + originalDtl.ExtPrice.ToString()
                       + "`"
                       + modifiedDtl.ExtPrice.ToString().Replace("`", "-"));
        }

        // DocExtPrice
        if (modifiedDtl.DocExtPrice != originalDtl.DocExtPrice)
        {
            changes.Add($"InvcDtl.DocExtPrice[Line {invoiceLine}]`"
                       + originalDtl.DocExtPrice.ToString()
                       + "`"
                       + modifiedDtl.DocExtPrice.ToString().Replace("`", "-"));
        }

        // Discount
        if (modifiedDtl.Discount != originalDtl.Discount)
        {
            changes.Add($"InvcDtl.Discount[Line {invoiceLine}]`"
                       + originalDtl.Discount.ToString()
                       + "`"
                       + modifiedDtl.Discount.ToString().Replace("`", "-"));
        }

        // DocDiscount
        if (modifiedDtl.DocDiscount != originalDtl.DocDiscount)
        {
            changes.Add($"InvcDtl.DocDiscount[Line {invoiceLine}]`"
                       + originalDtl.DocDiscount.ToString()
                       + "`"
                       + modifiedDtl.DocDiscount.ToString().Replace("`", "-"));
        }

        // TotalMiscChrg
        if (modifiedDtl.TotalMiscChrg != originalDtl.TotalMiscChrg)
        {
            changes.Add($"InvcDtl.TotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.TotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.TotalMiscChrg.ToString().Replace("`", "-"));
        }

        // DocTotalMiscChrg
        if (modifiedDtl.DocTotalMiscChrg != originalDtl.DocTotalMiscChrg)
        {
            changes.Add($"InvcDtl.DocTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.DocTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.DocTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // ProdCode
        if (modifiedDtl.ProdCode != originalDtl.ProdCode)
        {
            changes.Add($"InvcDtl.ProdCode[Line {invoiceLine}]`"
                       + originalDtl.ProdCode.ToString()
                       + "`"
                       + modifiedDtl.ProdCode.ToString().Replace("`", "-"));
        }

        // OurShipQty
        if (modifiedDtl.OurShipQty != originalDtl.OurShipQty)
        {
            changes.Add($"InvcDtl.OurShipQty[Line {invoiceLine}]`"
                       + originalDtl.OurShipQty.ToString()
                       + "`"
                       + modifiedDtl.OurShipQty.ToString().Replace("`", "-"));
        }

        // PackNum
        if (modifiedDtl.PackNum != originalDtl.PackNum)
        {
            changes.Add($"InvcDtl.PackNum[Line {invoiceLine}]`"
                       + originalDtl.PackNum.ToString()
                       + "`"
                       + modifiedDtl.PackNum.ToString().Replace("`", "-"));
        }

        // PackLine
        if (modifiedDtl.PackLine != originalDtl.PackLine)
        {
            changes.Add($"InvcDtl.PackLine[Line {invoiceLine}]`"
                       + originalDtl.PackLine.ToString()
                       + "`"
                       + modifiedDtl.PackLine.ToString().Replace("`", "-"));
        }

        // OrderNum
        if (modifiedDtl.OrderNum != originalDtl.OrderNum)
        {
            changes.Add($"InvcDtl.OrderNum[Line {invoiceLine}]`"
                       + originalDtl.OrderNum.ToString()
                       + "`"
                       + modifiedDtl.OrderNum.ToString().Replace("`", "-"));
        }

        // OrderLine
        if (modifiedDtl.OrderLine != originalDtl.OrderLine)
        {
            changes.Add($"InvcDtl.OrderLine[Line {invoiceLine}]`"
                       + originalDtl.OrderLine.ToString()
                       + "`"
                       + modifiedDtl.OrderLine.ToString().Replace("`", "-"));
        }

        // OrderRelNum
        if (modifiedDtl.OrderRelNum != originalDtl.OrderRelNum)
        {
            changes.Add($"InvcDtl.OrderRelNum[Line {invoiceLine}]`"
                       + originalDtl.OrderRelNum.ToString()
                       + "`"
                       + modifiedDtl.OrderRelNum.ToString().Replace("`", "-"));
        }

        // ShipToCustNum
        if (modifiedDtl.ShipToCustNum != originalDtl.ShipToCustNum)
        {
            changes.Add($"InvcDtl.ShipToCustNum[Line {invoiceLine}]`"
                       + originalDtl.ShipToCustNum.ToString()
                       + "`"
                       + modifiedDtl.ShipToCustNum.ToString().Replace("`", "-"));
        }

        // ShipToNum
        if (modifiedDtl.ShipToNum != originalDtl.ShipToNum)
        {
            changes.Add($"InvcDtl.ShipToNum[Line {invoiceLine}]`"
                       + originalDtl.ShipToNum.ToString()
                       + "`"
                       + modifiedDtl.ShipToNum.ToString().Replace("`", "-"));
        }

        // ShipDate
        if (modifiedDtl.ShipDate != originalDtl.ShipDate)
        {
            changes.Add($"InvcDtl.ShipDate[Line {invoiceLine}]`"
                       + originalDtl.ShipDate.ToString()
                       + "`"
                       + modifiedDtl.ShipDate.ToString().Replace("`", "-"));
        }

        // ShipViaCode
        if (modifiedDtl.ShipViaCode != originalDtl.ShipViaCode)
        {
            changes.Add($"InvcDtl.ShipViaCode[Line {invoiceLine}]`"
                       + originalDtl.ShipViaCode.ToString()
                       + "`"
                       + modifiedDtl.ShipViaCode.ToString().Replace("`", "-"));
        }

        // AdvanceBillCredit
        if (modifiedDtl.AdvanceBillCredit != originalDtl.AdvanceBillCredit)
        {
            changes.Add($"InvcDtl.AdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.AdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.AdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // DocAdvanceBillCredit
        if (modifiedDtl.DocAdvanceBillCredit != originalDtl.DocAdvanceBillCredit)
        {
            changes.Add($"InvcDtl.DocAdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.DocAdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.DocAdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // CustNum
        if (modifiedDtl.CustNum != originalDtl.CustNum)
        {
            changes.Add($"InvcDtl.CustNum[Line {invoiceLine}]`"
                       + originalDtl.CustNum.ToString()
                       + "`"
                       + modifiedDtl.CustNum.ToString().Replace("`", "-"));
        }

        // InvoiceComment
        if (modifiedDtl.InvoiceComment != originalDtl.InvoiceComment)
        {
            changes.Add($"InvcDtl.InvoiceComment[Line {invoiceLine}]`"
                       + originalDtl.InvoiceComment.ToString()
                       + "`"
                       + modifiedDtl.InvoiceComment.ToString().Replace("`", "-"));
        }

        // ShpConNum
        if (modifiedDtl.ShpConNum != originalDtl.ShpConNum)
        {
            changes.Add($"InvcDtl.ShpConNum[Line {invoiceLine}]`"
                       + originalDtl.ShpConNum.ToString()
                       + "`"
                       + modifiedDtl.ShpConNum.ToString().Replace("`", "-"));
        }

        // MtlUnitCost
        if (modifiedDtl.MtlUnitCost != originalDtl.MtlUnitCost)
        {
            changes.Add($"InvcDtl.MtlUnitCost[Line {invoiceLine}]`"
                       + originalDtl.MtlUnitCost.ToString()
                       + "`"
                       + modifiedDtl.MtlUnitCost.ToString().Replace("`", "-"));
        }

        // LbrUnitCost
        if (modifiedDtl.LbrUnitCost != originalDtl.LbrUnitCost)
        {
            changes.Add($"InvcDtl.LbrUnitCost[Line {invoiceLine}]`"
                       + originalDtl.LbrUnitCost.ToString()
                       + "`"
                       + modifiedDtl.LbrUnitCost.ToString().Replace("`", "-"));
        }

        // BurUnitCost
        if (modifiedDtl.BurUnitCost != originalDtl.BurUnitCost)
        {
            changes.Add($"InvcDtl.BurUnitCost[Line {invoiceLine}]`"
                       + originalDtl.BurUnitCost.ToString()
                       + "`"
                       + modifiedDtl.BurUnitCost.ToString().Replace("`", "-"));
        }

        // SubUnitCost
        if (modifiedDtl.SubUnitCost != originalDtl.SubUnitCost)
        {
            changes.Add($"InvcDtl.SubUnitCost[Line {invoiceLine}]`"
                       + originalDtl.SubUnitCost.ToString()
                       + "`"
                       + modifiedDtl.SubUnitCost.ToString().Replace("`", "-"));
        }

        // MtlBurUnitCost
        if (modifiedDtl.MtlBurUnitCost != originalDtl.MtlBurUnitCost)
        {
            changes.Add($"InvcDtl.MtlBurUnitCost[Line {invoiceLine}]`"
                       + originalDtl.MtlBurUnitCost.ToString()
                       + "`"
                       + modifiedDtl.MtlBurUnitCost.ToString().Replace("`", "-"));
        }

        // COSPostingReqd
        if (modifiedDtl.COSPostingReqd != originalDtl.COSPostingReqd)
        {
            changes.Add($"InvcDtl.COSPostingReqd[Line {invoiceLine}]`"
                       + originalDtl.COSPostingReqd.ToString()
                       + "`"
                       + modifiedDtl.COSPostingReqd.ToString().Replace("`", "-"));
        }

        // COSPosted
        if (modifiedDtl.COSPosted != originalDtl.COSPosted)
        {
            changes.Add($"InvcDtl.COSPosted[Line {invoiceLine}]`"
                       + originalDtl.COSPosted.ToString()
                       + "`"
                       + modifiedDtl.COSPosted.ToString().Replace("`", "-"));
        }

        // ContractCode
        if (modifiedDtl.ContractCode != originalDtl.ContractCode)
        {
            changes.Add($"InvcDtl.ContractCode[Line {invoiceLine}]`"
                       + originalDtl.ContractCode.ToString()
                       + "`"
                       + modifiedDtl.ContractCode.ToString().Replace("`", "-"));
        }

        // CallNum
        if (modifiedDtl.CallNum != originalDtl.CallNum)
        {
            changes.Add($"InvcDtl.CallNum[Line {invoiceLine}]`"
                       + originalDtl.CallNum.ToString()
                       + "`"
                       + modifiedDtl.CallNum.ToString().Replace("`", "-"));
        }

        // CallCode
        if (modifiedDtl.CallCode != originalDtl.CallCode)
        {
            changes.Add($"InvcDtl.CallCode[Line {invoiceLine}]`"
                       + originalDtl.CallCode.ToString()
                       + "`"
                       + modifiedDtl.CallCode.ToString().Replace("`", "-"));
        }

        // RMANum
        if (modifiedDtl.RMANum != originalDtl.RMANum)
        {
            changes.Add($"InvcDtl.RMANum[Line {invoiceLine}]`"
                       + originalDtl.RMANum.ToString()
                       + "`"
                       + modifiedDtl.RMANum.ToString().Replace("`", "-"));
        }

        // RMALine
        if (modifiedDtl.RMALine != originalDtl.RMALine)
        {
            changes.Add($"InvcDtl.RMALine[Line {invoiceLine}]`"
                       + originalDtl.RMALine.ToString()
                       + "`"
                       + modifiedDtl.RMALine.ToString().Replace("`", "-"));
        }

        // SalesCatID
        if (modifiedDtl.SalesCatID != originalDtl.SalesCatID)
        {
            changes.Add($"InvcDtl.SalesCatID[Line {invoiceLine}]`"
                       + originalDtl.SalesCatID.ToString()
                       + "`"
                       + modifiedDtl.SalesCatID.ToString().Replace("`", "-"));
        }

        // FiscalYear
        if (modifiedDtl.FiscalYear != originalDtl.FiscalYear)
        {
            changes.Add($"InvcDtl.FiscalYear[Line {invoiceLine}]`"
                       + originalDtl.FiscalYear.ToString()
                       + "`"
                       + modifiedDtl.FiscalYear.ToString().Replace("`", "-"));
        }

        // FiscalPeriod
        if (modifiedDtl.FiscalPeriod != originalDtl.FiscalPeriod)
        {
            changes.Add($"InvcDtl.FiscalPeriod[Line {invoiceLine}]`"
                       + originalDtl.FiscalPeriod.ToString()
                       + "`"
                       + modifiedDtl.FiscalPeriod.ToString().Replace("`", "-"));
        }

        // JournalCode
        if (modifiedDtl.JournalCode != originalDtl.JournalCode)
        {
            changes.Add($"InvcDtl.JournalCode[Line {invoiceLine}]`"
                       + originalDtl.JournalCode.ToString()
                       + "`"
                       + modifiedDtl.JournalCode.ToString().Replace("`", "-"));
        }

        // JournalNum
        if (modifiedDtl.JournalNum != originalDtl.JournalNum)
        {
            changes.Add($"InvcDtl.JournalNum[Line {invoiceLine}]`"
                       + originalDtl.JournalNum.ToString()
                       + "`"
                       + modifiedDtl.JournalNum.ToString().Replace("`", "-"));
        }

        // SellingOrderQty
        if (modifiedDtl.SellingOrderQty != originalDtl.SellingOrderQty)
        {
            changes.Add($"InvcDtl.SellingOrderQty[Line {invoiceLine}]`"
                       + originalDtl.SellingOrderQty.ToString()
                       + "`"
                       + modifiedDtl.SellingOrderQty.ToString().Replace("`", "-"));
        }

        // SellingShipQty
        if (modifiedDtl.SellingShipQty != originalDtl.SellingShipQty)
        {
            changes.Add($"InvcDtl.SellingShipQty[Line {invoiceLine}]`"
                       + originalDtl.SellingShipQty.ToString()
                       + "`"
                       + modifiedDtl.SellingShipQty.ToString().Replace("`", "-"));
        }

        // SalesUM
        if (modifiedDtl.SalesUM != originalDtl.SalesUM)
        {
            changes.Add($"InvcDtl.SalesUM[Line {invoiceLine}]`"
                       + originalDtl.SalesUM.ToString()
                       + "`"
                       + modifiedDtl.SalesUM.ToString().Replace("`", "-"));
        }

        // SellingFactor
        if (modifiedDtl.SellingFactor != originalDtl.SellingFactor)
        {
            changes.Add($"InvcDtl.SellingFactor[Line {invoiceLine}]`"
                       + originalDtl.SellingFactor.ToString()
                       + "`"
                       + modifiedDtl.SellingFactor.ToString().Replace("`", "-"));
        }

        // ProjectID
        if (modifiedDtl.ProjectID != originalDtl.ProjectID)
        {
            changes.Add($"InvcDtl.ProjectID[Line {invoiceLine}]`"
                       + originalDtl.ProjectID.ToString()
                       + "`"
                       + modifiedDtl.ProjectID.ToString().Replace("`", "-"));
        }

        // MilestoneID
        if (modifiedDtl.MilestoneID != originalDtl.MilestoneID)
        {
            changes.Add($"InvcDtl.MilestoneID[Line {invoiceLine}]`"
                       + originalDtl.MilestoneID.ToString()
                       + "`"
                       + modifiedDtl.MilestoneID.ToString().Replace("`", "-"));
        }

        // ListPrice
        if (modifiedDtl.ListPrice != originalDtl.ListPrice)
        {
            changes.Add($"InvcDtl.ListPrice[Line {invoiceLine}]`"
                       + originalDtl.ListPrice.ToString()
                       + "`"
                       + modifiedDtl.ListPrice.ToString().Replace("`", "-"));
        }

        // DocListPrice
        if (modifiedDtl.DocListPrice != originalDtl.DocListPrice)
        {
            changes.Add($"InvcDtl.DocListPrice[Line {invoiceLine}]`"
                       + originalDtl.DocListPrice.ToString()
                       + "`"
                       + modifiedDtl.DocListPrice.ToString().Replace("`", "-"));
        }

        // OrdBasedPrice
        if (modifiedDtl.OrdBasedPrice != originalDtl.OrdBasedPrice)
        {
            changes.Add($"InvcDtl.OrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.OrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.OrdBasedPrice.ToString().Replace("`", "-"));
        }

        // DocOrdBasedPrice
        if (modifiedDtl.DocOrdBasedPrice != originalDtl.DocOrdBasedPrice)
        {
            changes.Add($"InvcDtl.DocOrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.DocOrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.DocOrdBasedPrice.ToString().Replace("`", "-"));
        }

        // AdvGainLoss
        if (modifiedDtl.AdvGainLoss != originalDtl.AdvGainLoss)
        {
            changes.Add($"InvcDtl.AdvGainLoss[Line {invoiceLine}]`"
                       + originalDtl.AdvGainLoss.ToString()
                       + "`"
                       + modifiedDtl.AdvGainLoss.ToString().Replace("`", "-"));
        }

        // SellingFactorDirection
        if (modifiedDtl.SellingFactorDirection != originalDtl.SellingFactorDirection)
        {
            changes.Add($"InvcDtl.SellingFactorDirection[Line {invoiceLine}]`"
                       + originalDtl.SellingFactorDirection.ToString()
                       + "`"
                       + modifiedDtl.SellingFactorDirection.ToString().Replace("`", "-"));
        }

        // RepRate1
        if (modifiedDtl.RepRate1 != originalDtl.RepRate1)
        {
            changes.Add($"InvcDtl.RepRate1[Line {invoiceLine}]`"
                       + originalDtl.RepRate1.ToString()
                       + "`"
                       + modifiedDtl.RepRate1.ToString().Replace("`", "-"));
        }

        // RepRate2
        if (modifiedDtl.RepRate2 != originalDtl.RepRate2)
        {
            changes.Add($"InvcDtl.RepRate2[Line {invoiceLine}]`"
                       + originalDtl.RepRate2.ToString()
                       + "`"
                       + modifiedDtl.RepRate2.ToString().Replace("`", "-"));
        }

        // RepRate3
        if (modifiedDtl.RepRate3 != originalDtl.RepRate3)
        {
            changes.Add($"InvcDtl.RepRate3[Line {invoiceLine}]`"
                       + originalDtl.RepRate3.ToString()
                       + "`"
                       + modifiedDtl.RepRate3.ToString().Replace("`", "-"));
        }

        // RepRate4
        if (modifiedDtl.RepRate4 != originalDtl.RepRate4)
        {
            changes.Add($"InvcDtl.RepRate4[Line {invoiceLine}]`"
                       + originalDtl.RepRate4.ToString()
                       + "`"
                       + modifiedDtl.RepRate4.ToString().Replace("`", "-"));
        }

        // RepRate5
        if (modifiedDtl.RepRate5 != originalDtl.RepRate5)
        {
            changes.Add($"InvcDtl.RepRate5[Line {invoiceLine}]`"
                       + originalDtl.RepRate5.ToString()
                       + "`"
                       + modifiedDtl.RepRate5.ToString().Replace("`", "-"));
        }

        // RepSplit1
        if (modifiedDtl.RepSplit1 != originalDtl.RepSplit1)
        {
            changes.Add($"InvcDtl.RepSplit1[Line {invoiceLine}]`"
                       + originalDtl.RepSplit1.ToString()
                       + "`"
                       + modifiedDtl.RepSplit1.ToString().Replace("`", "-"));
        }

        // RepSplit2
        if (modifiedDtl.RepSplit2 != originalDtl.RepSplit2)
        {
            changes.Add($"InvcDtl.RepSplit2[Line {invoiceLine}]`"
                       + originalDtl.RepSplit2.ToString()
                       + "`"
                       + modifiedDtl.RepSplit2.ToString().Replace("`", "-"));
        }

        // RepSplit3
        if (modifiedDtl.RepSplit3 != originalDtl.RepSplit3)
        {
            changes.Add($"InvcDtl.RepSplit3[Line {invoiceLine}]`"
                       + originalDtl.RepSplit3.ToString()
                       + "`"
                       + modifiedDtl.RepSplit3.ToString().Replace("`", "-"));
        }

        // RepSplit4
        if (modifiedDtl.RepSplit4 != originalDtl.RepSplit4)
        {
            changes.Add($"InvcDtl.RepSplit4[Line {invoiceLine}]`"
                       + originalDtl.RepSplit4.ToString()
                       + "`"
                       + modifiedDtl.RepSplit4.ToString().Replace("`", "-"));
        }

        // RepSplit5
        if (modifiedDtl.RepSplit5 != originalDtl.RepSplit5)
        {
            changes.Add($"InvcDtl.RepSplit5[Line {invoiceLine}]`"
                       + originalDtl.RepSplit5.ToString()
                       + "`"
                       + modifiedDtl.RepSplit5.ToString().Replace("`", "-"));
        }

        // BTCustNum
        if (modifiedDtl.BTCustNum != originalDtl.BTCustNum)
        {
            changes.Add($"InvcDtl.BTCustNum[Line {invoiceLine}]`"
                       + originalDtl.BTCustNum.ToString()
                       + "`"
                       + modifiedDtl.BTCustNum.ToString().Replace("`", "-"));
        }

        // JCMtlUnitCost
        if (modifiedDtl.JCMtlUnitCost != originalDtl.JCMtlUnitCost)
        {
            changes.Add($"InvcDtl.JCMtlUnitCost[Line {invoiceLine}]`"
                       + originalDtl.JCMtlUnitCost.ToString()
                       + "`"
                       + modifiedDtl.JCMtlUnitCost.ToString().Replace("`", "-"));
        }

        // JCLbrUnitCost
        if (modifiedDtl.JCLbrUnitCost != originalDtl.JCLbrUnitCost)
        {
            changes.Add($"InvcDtl.JCLbrUnitCost[Line {invoiceLine}]`"
                       + originalDtl.JCLbrUnitCost.ToString()
                       + "`"
                       + modifiedDtl.JCLbrUnitCost.ToString().Replace("`", "-"));
        }

        // JCBurUnitCost
        if (modifiedDtl.JCBurUnitCost != originalDtl.JCBurUnitCost)
        {
            changes.Add($"InvcDtl.JCBurUnitCost[Line {invoiceLine}]`"
                       + originalDtl.JCBurUnitCost.ToString()
                       + "`"
                       + modifiedDtl.JCBurUnitCost.ToString().Replace("`", "-"));
        }

        // JCSubUnitCost
        if (modifiedDtl.JCSubUnitCost != originalDtl.JCSubUnitCost)
        {
            changes.Add($"InvcDtl.JCSubUnitCost[Line {invoiceLine}]`"
                       + originalDtl.JCSubUnitCost.ToString()
                       + "`"
                       + modifiedDtl.JCSubUnitCost.ToString().Replace("`", "-"));
        }

        // JCMtlBurUnitCost
        if (modifiedDtl.JCMtlBurUnitCost != originalDtl.JCMtlBurUnitCost)
        {
            changes.Add($"InvcDtl.JCMtlBurUnitCost[Line {invoiceLine}]`"
                       + originalDtl.JCMtlBurUnitCost.ToString()
                       + "`"
                       + modifiedDtl.JCMtlBurUnitCost.ToString().Replace("`", "-"));
        }

        // ChangedBy
        if (modifiedDtl.ChangedBy != originalDtl.ChangedBy)
        {
            changes.Add($"InvcDtl.ChangedBy[Line {invoiceLine}]`"
                       + originalDtl.ChangedBy.ToString()
                       + "`"
                       + modifiedDtl.ChangedBy.ToString().Replace("`", "-"));
        }

        // ChangeDate
        if (modifiedDtl.ChangeDate != originalDtl.ChangeDate)
        {
            changes.Add($"InvcDtl.ChangeDate[Line {invoiceLine}]`"
                       + originalDtl.ChangeDate.ToString()
                       + "`"
                       + modifiedDtl.ChangeDate.ToString().Replace("`", "-"));
        }

        // ChangeTime
        if (modifiedDtl.ChangeTime != originalDtl.ChangeTime)
        {
            changes.Add($"InvcDtl.ChangeTime[Line {invoiceLine}]`"
                       + originalDtl.ChangeTime.ToString()
                       + "`"
                       + modifiedDtl.ChangeTime.ToString().Replace("`", "-"));
        }

        // RevChargeMethod
        if (modifiedDtl.RevChargeMethod != originalDtl.RevChargeMethod)
        {
            changes.Add($"InvcDtl.RevChargeMethod[Line {invoiceLine}]`"
                       + originalDtl.RevChargeMethod.ToString()
                       + "`"
                       + modifiedDtl.RevChargeMethod.ToString().Replace("`", "-"));
        }

        // OverrideReverseCharge
        if (modifiedDtl.OverrideReverseCharge != originalDtl.OverrideReverseCharge)
        {
            changes.Add($"InvcDtl.OverrideReverseCharge[Line {invoiceLine}]`"
                       + originalDtl.OverrideReverseCharge.ToString()
                       + "`"
                       + modifiedDtl.OverrideReverseCharge.ToString().Replace("`", "-"));
        }

        // RevChargeApplied
        if (modifiedDtl.RevChargeApplied != originalDtl.RevChargeApplied)
        {
            changes.Add($"InvcDtl.RevChargeApplied[Line {invoiceLine}]`"
                       + originalDtl.RevChargeApplied.ToString()
                       + "`"
                       + modifiedDtl.RevChargeApplied.ToString().Replace("`", "-"));
        }

        // TaxConnectCalc
        if (modifiedDtl.TaxConnectCalc != originalDtl.TaxConnectCalc)
        {
            changes.Add($"InvcDtl.TaxConnectCalc[Line {invoiceLine}]`"
                       + originalDtl.TaxConnectCalc.ToString()
                       + "`"
                       + modifiedDtl.TaxConnectCalc.ToString().Replace("`", "-"));
        }

        // GetDfltTaxIds
        if (modifiedDtl.GetDfltTaxIds != originalDtl.GetDfltTaxIds)
        {
            changes.Add($"InvcDtl.GetDfltTaxIds[Line {invoiceLine}]`"
                       + originalDtl.GetDfltTaxIds.ToString()
                       + "`"
                       + modifiedDtl.GetDfltTaxIds.ToString().Replace("`", "-"));
        }

        // Rpt1AdvanceBillCredit
        if (modifiedDtl.Rpt1AdvanceBillCredit != originalDtl.Rpt1AdvanceBillCredit)
        {
            changes.Add($"InvcDtl.Rpt1AdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.Rpt1AdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.Rpt1AdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // Rpt2AdvanceBillCredit
        if (modifiedDtl.Rpt2AdvanceBillCredit != originalDtl.Rpt2AdvanceBillCredit)
        {
            changes.Add($"InvcDtl.Rpt2AdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.Rpt2AdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.Rpt2AdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // Rpt3AdvanceBillCredit
        if (modifiedDtl.Rpt3AdvanceBillCredit != originalDtl.Rpt3AdvanceBillCredit)
        {
            changes.Add($"InvcDtl.Rpt3AdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.Rpt3AdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.Rpt3AdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // Rpt1Discount
        if (modifiedDtl.Rpt1Discount != originalDtl.Rpt1Discount)
        {
            changes.Add($"InvcDtl.Rpt1Discount[Line {invoiceLine}]`"
                       + originalDtl.Rpt1Discount.ToString()
                       + "`"
                       + modifiedDtl.Rpt1Discount.ToString().Replace("`", "-"));
        }

        // Rpt2Discount
        if (modifiedDtl.Rpt2Discount != originalDtl.Rpt2Discount)
        {
            changes.Add($"InvcDtl.Rpt2Discount[Line {invoiceLine}]`"
                       + originalDtl.Rpt2Discount.ToString()
                       + "`"
                       + modifiedDtl.Rpt2Discount.ToString().Replace("`", "-"));
        }

        // Rpt3Discount
        if (modifiedDtl.Rpt3Discount != originalDtl.Rpt3Discount)
        {
            changes.Add($"InvcDtl.Rpt3Discount[Line {invoiceLine}]`"
                       + originalDtl.Rpt3Discount.ToString()
                       + "`"
                       + modifiedDtl.Rpt3Discount.ToString().Replace("`", "-"));
        }

        // Rpt1ExtPrice
        if (modifiedDtl.Rpt1ExtPrice != originalDtl.Rpt1ExtPrice)
        {
            changes.Add($"InvcDtl.Rpt1ExtPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1ExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1ExtPrice.ToString().Replace("`", "-"));
        }

        // Rpt2ExtPrice
        if (modifiedDtl.Rpt2ExtPrice != originalDtl.Rpt2ExtPrice)
        {
            changes.Add($"InvcDtl.Rpt2ExtPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2ExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2ExtPrice.ToString().Replace("`", "-"));
        }

        // Rpt3ExtPrice
        if (modifiedDtl.Rpt3ExtPrice != originalDtl.Rpt3ExtPrice)
        {
            changes.Add($"InvcDtl.Rpt3ExtPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3ExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3ExtPrice.ToString().Replace("`", "-"));
        }

        // Rpt1ListPrice
        if (modifiedDtl.Rpt1ListPrice != originalDtl.Rpt1ListPrice)
        {
            changes.Add($"InvcDtl.Rpt1ListPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1ListPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1ListPrice.ToString().Replace("`", "-"));
        }

        // Rpt2ListPrice
        if (modifiedDtl.Rpt2ListPrice != originalDtl.Rpt2ListPrice)
        {
            changes.Add($"InvcDtl.Rpt2ListPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2ListPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2ListPrice.ToString().Replace("`", "-"));
        }

        // Rpt3ListPrice
        if (modifiedDtl.Rpt3ListPrice != originalDtl.Rpt3ListPrice)
        {
            changes.Add($"InvcDtl.Rpt3ListPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3ListPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3ListPrice.ToString().Replace("`", "-"));
        }

        // Rpt1OrdBasedPrice
        if (modifiedDtl.Rpt1OrdBasedPrice != originalDtl.Rpt1OrdBasedPrice)
        {
            changes.Add($"InvcDtl.Rpt1OrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1OrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1OrdBasedPrice.ToString().Replace("`", "-"));
        }

        // Rpt2OrdBasedPrice
        if (modifiedDtl.Rpt2OrdBasedPrice != originalDtl.Rpt2OrdBasedPrice)
        {
            changes.Add($"InvcDtl.Rpt2OrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2OrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2OrdBasedPrice.ToString().Replace("`", "-"));
        }

        // Rpt3OrdBasedPrice
        if (modifiedDtl.Rpt3OrdBasedPrice != originalDtl.Rpt3OrdBasedPrice)
        {
            changes.Add($"InvcDtl.Rpt3OrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3OrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3OrdBasedPrice.ToString().Replace("`", "-"));
        }

        // Rpt1TotalMiscChrg
        if (modifiedDtl.Rpt1TotalMiscChrg != originalDtl.Rpt1TotalMiscChrg)
        {
            changes.Add($"InvcDtl.Rpt1TotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt1TotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt1TotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt2TotalMiscChrg
        if (modifiedDtl.Rpt2TotalMiscChrg != originalDtl.Rpt2TotalMiscChrg)
        {
            changes.Add($"InvcDtl.Rpt2TotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt2TotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt2TotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt3TotalMiscChrg
        if (modifiedDtl.Rpt3TotalMiscChrg != originalDtl.Rpt3TotalMiscChrg)
        {
            changes.Add($"InvcDtl.Rpt3TotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt3TotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt3TotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt1UnitPrice
        if (modifiedDtl.Rpt1UnitPrice != originalDtl.Rpt1UnitPrice)
        {
            changes.Add($"InvcDtl.Rpt1UnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1UnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1UnitPrice.ToString().Replace("`", "-"));
        }

        // Rpt2UnitPrice
        if (modifiedDtl.Rpt2UnitPrice != originalDtl.Rpt2UnitPrice)
        {
            changes.Add($"InvcDtl.Rpt2UnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2UnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2UnitPrice.ToString().Replace("`", "-"));
        }

        // Rpt3UnitPrice
        if (modifiedDtl.Rpt3UnitPrice != originalDtl.Rpt3UnitPrice)
        {
            changes.Add($"InvcDtl.Rpt3UnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3UnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3UnitPrice.ToString().Replace("`", "-"));
        }

        // Rpt1AdvGainLoss
        if (modifiedDtl.Rpt1AdvGainLoss != originalDtl.Rpt1AdvGainLoss)
        {
            changes.Add($"InvcDtl.Rpt1AdvGainLoss[Line {invoiceLine}]`"
                       + originalDtl.Rpt1AdvGainLoss.ToString()
                       + "`"
                       + modifiedDtl.Rpt1AdvGainLoss.ToString().Replace("`", "-"));
        }

        // Rpt2AdvGainLoss
        if (modifiedDtl.Rpt2AdvGainLoss != originalDtl.Rpt2AdvGainLoss)
        {
            changes.Add($"InvcDtl.Rpt2AdvGainLoss[Line {invoiceLine}]`"
                       + originalDtl.Rpt2AdvGainLoss.ToString()
                       + "`"
                       + modifiedDtl.Rpt2AdvGainLoss.ToString().Replace("`", "-"));
        }

        // Rpt3AdvGainLoss
        if (modifiedDtl.Rpt3AdvGainLoss != originalDtl.Rpt3AdvGainLoss)
        {
            changes.Add($"InvcDtl.Rpt3AdvGainLoss[Line {invoiceLine}]`"
                       + originalDtl.Rpt3AdvGainLoss.ToString()
                       + "`"
                       + modifiedDtl.Rpt3AdvGainLoss.ToString().Replace("`", "-"));
        }

        // FiscalYearSuffix
        if (modifiedDtl.FiscalYearSuffix != originalDtl.FiscalYearSuffix)
        {
            changes.Add($"InvcDtl.FiscalYearSuffix[Line {invoiceLine}]`"
                       + originalDtl.FiscalYearSuffix.ToString()
                       + "`"
                       + modifiedDtl.FiscalYearSuffix.ToString().Replace("`", "-"));
        }

        // FiscalCalendarID
        if (modifiedDtl.FiscalCalendarID != originalDtl.FiscalCalendarID)
        {
            changes.Add($"InvcDtl.FiscalCalendarID[Line {invoiceLine}]`"
                       + originalDtl.FiscalCalendarID.ToString()
                       + "`"
                       + modifiedDtl.FiscalCalendarID.ToString().Replace("`", "-"));
        }

        // TaxRegionCode
        if (modifiedDtl.TaxRegionCode != originalDtl.TaxRegionCode)
        {
            changes.Add($"InvcDtl.TaxRegionCode[Line {invoiceLine}]`"
                       + originalDtl.TaxRegionCode.ToString()
                       + "`"
                       + modifiedDtl.TaxRegionCode.ToString().Replace("`", "-"));
        }

        // UseOTS
        if (modifiedDtl.UseOTS != originalDtl.UseOTS)
        {
            changes.Add($"InvcDtl.UseOTS[Line {invoiceLine}]`"
                       + originalDtl.UseOTS.ToString()
                       + "`"
                       + modifiedDtl.UseOTS.ToString().Replace("`", "-"));
        }

        // OTSName
        if (modifiedDtl.OTSName != originalDtl.OTSName)
        {
            changes.Add($"InvcDtl.OTSName[Line {invoiceLine}]`"
                       + originalDtl.OTSName.ToString()
                       + "`"
                       + modifiedDtl.OTSName.ToString().Replace("`", "-"));
        }

        // OTSAddress1
        if (modifiedDtl.OTSAddress1 != originalDtl.OTSAddress1)
        {
            changes.Add($"InvcDtl.OTSAddress1[Line {invoiceLine}]`"
                       + originalDtl.OTSAddress1.ToString()
                       + "`"
                       + modifiedDtl.OTSAddress1.ToString().Replace("`", "-"));
        }

        // OTSAddress2
        if (modifiedDtl.OTSAddress2 != originalDtl.OTSAddress2)
        {
            changes.Add($"InvcDtl.OTSAddress2[Line {invoiceLine}]`"
                       + originalDtl.OTSAddress2.ToString()
                       + "`"
                       + modifiedDtl.OTSAddress2.ToString().Replace("`", "-"));
        }

        // OTSAddress3
        if (modifiedDtl.OTSAddress3 != originalDtl.OTSAddress3)
        {
            changes.Add($"InvcDtl.OTSAddress3[Line {invoiceLine}]`"
                       + originalDtl.OTSAddress3.ToString()
                       + "`"
                       + modifiedDtl.OTSAddress3.ToString().Replace("`", "-"));
        }

        // OTSCity
        if (modifiedDtl.OTSCity != originalDtl.OTSCity)
        {
            changes.Add($"InvcDtl.OTSCity[Line {invoiceLine}]`"
                       + originalDtl.OTSCity.ToString()
                       + "`"
                       + modifiedDtl.OTSCity.ToString().Replace("`", "-"));
        }

        // OTSState
        if (modifiedDtl.OTSState != originalDtl.OTSState)
        {
            changes.Add($"InvcDtl.OTSState[Line {invoiceLine}]`"
                       + originalDtl.OTSState.ToString()
                       + "`"
                       + modifiedDtl.OTSState.ToString().Replace("`", "-"));
        }

        // OTSZIP
        if (modifiedDtl.OTSZIP != originalDtl.OTSZIP)
        {
            changes.Add($"InvcDtl.OTSZIP[Line {invoiceLine}]`"
                       + originalDtl.OTSZIP.ToString()
                       + "`"
                       + modifiedDtl.OTSZIP.ToString().Replace("`", "-"));
        }

        // OTSResaleID
        if (modifiedDtl.OTSResaleID != originalDtl.OTSResaleID)
        {
            changes.Add($"InvcDtl.OTSResaleID[Line {invoiceLine}]`"
                       + originalDtl.OTSResaleID.ToString()
                       + "`"
                       + modifiedDtl.OTSResaleID.ToString().Replace("`", "-"));
        }

        // OTSTaxRegionCode
        if (modifiedDtl.OTSTaxRegionCode != originalDtl.OTSTaxRegionCode)
        {
            changes.Add($"InvcDtl.OTSTaxRegionCode[Line {invoiceLine}]`"
                       + originalDtl.OTSTaxRegionCode.ToString()
                       + "`"
                       + modifiedDtl.OTSTaxRegionCode.ToString().Replace("`", "-"));
        }

        // OTSContact
        if (modifiedDtl.OTSContact != originalDtl.OTSContact)
        {
            changes.Add($"InvcDtl.OTSContact[Line {invoiceLine}]`"
                       + originalDtl.OTSContact.ToString()
                       + "`"
                       + modifiedDtl.OTSContact.ToString().Replace("`", "-"));
        }

        // OTSFaxNum
        if (modifiedDtl.OTSFaxNum != originalDtl.OTSFaxNum)
        {
            changes.Add($"InvcDtl.OTSFaxNum[Line {invoiceLine}]`"
                       + originalDtl.OTSFaxNum.ToString()
                       + "`"
                       + modifiedDtl.OTSFaxNum.ToString().Replace("`", "-"));
        }

        // OTSPhoneNum
        if (modifiedDtl.OTSPhoneNum != originalDtl.OTSPhoneNum)
        {
            changes.Add($"InvcDtl.OTSPhoneNum[Line {invoiceLine}]`"
                       + originalDtl.OTSPhoneNum.ToString()
                       + "`"
                       + modifiedDtl.OTSPhoneNum.ToString().Replace("`", "-"));
        }

        // OTSCountryNum
        if (modifiedDtl.OTSCountryNum != originalDtl.OTSCountryNum)
        {
            changes.Add($"InvcDtl.OTSCountryNum[Line {invoiceLine}]`"
                       + originalDtl.OTSCountryNum.ToString()
                       + "`"
                       + modifiedDtl.OTSCountryNum.ToString().Replace("`", "-"));
        }

        // Plant
        if (modifiedDtl.Plant != originalDtl.Plant)
        {
            changes.Add($"InvcDtl.Plant[Line {invoiceLine}]`"
                       + originalDtl.Plant.ToString()
                       + "`"
                       + modifiedDtl.Plant.ToString().Replace("`", "-"));
        }

        // WarehouseCode
        if (modifiedDtl.WarehouseCode != originalDtl.WarehouseCode)
        {
            changes.Add($"InvcDtl.WarehouseCode[Line {invoiceLine}]`"
                       + originalDtl.WarehouseCode.ToString()
                       + "`"
                       + modifiedDtl.WarehouseCode.ToString().Replace("`", "-"));
        }

        // CallLine
        if (modifiedDtl.CallLine != originalDtl.CallLine)
        {
            changes.Add($"InvcDtl.CallLine[Line {invoiceLine}]`"
                       + originalDtl.CallLine.ToString()
                       + "`"
                       + modifiedDtl.CallLine.ToString().Replace("`", "-"));
        }

        // DropShipPackLine
        if (modifiedDtl.DropShipPackLine != originalDtl.DropShipPackLine)
        {
            changes.Add($"InvcDtl.DropShipPackLine[Line {invoiceLine}]`"
                       + originalDtl.DropShipPackLine.ToString()
                       + "`"
                       + modifiedDtl.DropShipPackLine.ToString().Replace("`", "-"));
        }

        // DropShipPackSlip
        if (modifiedDtl.DropShipPackSlip != originalDtl.DropShipPackSlip)
        {
            changes.Add($"InvcDtl.DropShipPackSlip[Line {invoiceLine}]`"
                       + originalDtl.DropShipPackSlip.ToString()
                       + "`"
                       + modifiedDtl.DropShipPackSlip.ToString().Replace("`", "-"));
        }

        // FinChargeCode
        if (modifiedDtl.FinChargeCode != originalDtl.FinChargeCode)
        {
            changes.Add($"InvcDtl.FinChargeCode[Line {invoiceLine}]`"
                       + originalDtl.FinChargeCode.ToString()
                       + "`"
                       + modifiedDtl.FinChargeCode.ToString().Replace("`", "-"));
        }

        // ABTUID
        if (modifiedDtl.ABTUID != originalDtl.ABTUID)
        {
            changes.Add($"InvcDtl.ABTUID[Line {invoiceLine}]`"
                       + originalDtl.ABTUID.ToString()
                       + "`"
                       + modifiedDtl.ABTUID.ToString().Replace("`", "-"));
        }

        // InUnitPrice
        if (modifiedDtl.InUnitPrice != originalDtl.InUnitPrice)
        {
            changes.Add($"InvcDtl.InUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.InUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.InUnitPrice.ToString().Replace("`", "-"));
        }

        // DocInUnitPrice
        if (modifiedDtl.DocInUnitPrice != originalDtl.DocInUnitPrice)
        {
            changes.Add($"InvcDtl.DocInUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.DocInUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.DocInUnitPrice.ToString().Replace("`", "-"));
        }

        // InExtPrice
        if (modifiedDtl.InExtPrice != originalDtl.InExtPrice)
        {
            changes.Add($"InvcDtl.InExtPrice[Line {invoiceLine}]`"
                       + originalDtl.InExtPrice.ToString()
                       + "`"
                       + modifiedDtl.InExtPrice.ToString().Replace("`", "-"));
        }

        // DocInExtPrice
        if (modifiedDtl.DocInExtPrice != originalDtl.DocInExtPrice)
        {
            changes.Add($"InvcDtl.DocInExtPrice[Line {invoiceLine}]`"
                       + originalDtl.DocInExtPrice.ToString()
                       + "`"
                       + modifiedDtl.DocInExtPrice.ToString().Replace("`", "-"));
        }

        // InDiscount
        if (modifiedDtl.InDiscount != originalDtl.InDiscount)
        {
            changes.Add($"InvcDtl.InDiscount[Line {invoiceLine}]`"
                       + originalDtl.InDiscount.ToString()
                       + "`"
                       + modifiedDtl.InDiscount.ToString().Replace("`", "-"));
        }

        // DocInDiscount
        if (modifiedDtl.DocInDiscount != originalDtl.DocInDiscount)
        {
            changes.Add($"InvcDtl.DocInDiscount[Line {invoiceLine}]`"
                       + originalDtl.DocInDiscount.ToString()
                       + "`"
                       + modifiedDtl.DocInDiscount.ToString().Replace("`", "-"));
        }

        // InTotalMiscChrg
        if (modifiedDtl.InTotalMiscChrg != originalDtl.InTotalMiscChrg)
        {
            changes.Add($"InvcDtl.InTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.InTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.InTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // DocInTotalMiscChrg
        if (modifiedDtl.DocInTotalMiscChrg != originalDtl.DocInTotalMiscChrg)
        {
            changes.Add($"InvcDtl.DocInTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.DocInTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.DocInTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // InListPrice
        if (modifiedDtl.InListPrice != originalDtl.InListPrice)
        {
            changes.Add($"InvcDtl.InListPrice[Line {invoiceLine}]`"
                       + originalDtl.InListPrice.ToString()
                       + "`"
                       + modifiedDtl.InListPrice.ToString().Replace("`", "-"));
        }

        // DocInListPrice
        if (modifiedDtl.DocInListPrice != originalDtl.DocInListPrice)
        {
            changes.Add($"InvcDtl.DocInListPrice[Line {invoiceLine}]`"
                       + originalDtl.DocInListPrice.ToString()
                       + "`"
                       + modifiedDtl.DocInListPrice.ToString().Replace("`", "-"));
        }

        // InOrdBasedPrice
        if (modifiedDtl.InOrdBasedPrice != originalDtl.InOrdBasedPrice)
        {
            changes.Add($"InvcDtl.InOrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.InOrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.InOrdBasedPrice.ToString().Replace("`", "-"));
        }

        // DocInOrdBasedPrice
        if (modifiedDtl.DocInOrdBasedPrice != originalDtl.DocInOrdBasedPrice)
        {
            changes.Add($"InvcDtl.DocInOrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.DocInOrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.DocInOrdBasedPrice.ToString().Replace("`", "-"));
        }

        // Rpt1InDiscount
        if (modifiedDtl.Rpt1InDiscount != originalDtl.Rpt1InDiscount)
        {
            changes.Add($"InvcDtl.Rpt1InDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InDiscount.ToString().Replace("`", "-"));
        }

        // Rpt2InDiscount
        if (modifiedDtl.Rpt2InDiscount != originalDtl.Rpt2InDiscount)
        {
            changes.Add($"InvcDtl.Rpt2InDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InDiscount.ToString().Replace("`", "-"));
        }

        // Rpt3InDiscount
        if (modifiedDtl.Rpt3InDiscount != originalDtl.Rpt3InDiscount)
        {
            changes.Add($"InvcDtl.Rpt3InDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InDiscount.ToString().Replace("`", "-"));
        }

        // Rpt1InExtPrice
        if (modifiedDtl.Rpt1InExtPrice != originalDtl.Rpt1InExtPrice)
        {
            changes.Add($"InvcDtl.Rpt1InExtPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InExtPrice.ToString().Replace("`", "-"));
        }

        // Rpt2InExtPrice
        if (modifiedDtl.Rpt2InExtPrice != originalDtl.Rpt2InExtPrice)
        {
            changes.Add($"InvcDtl.Rpt2InExtPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InExtPrice.ToString().Replace("`", "-"));
        }

        // Rpt3InExtPrice
        if (modifiedDtl.Rpt3InExtPrice != originalDtl.Rpt3InExtPrice)
        {
            changes.Add($"InvcDtl.Rpt3InExtPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InExtPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InExtPrice.ToString().Replace("`", "-"));
        }

        // Rpt1InListPrice
        if (modifiedDtl.Rpt1InListPrice != originalDtl.Rpt1InListPrice)
        {
            changes.Add($"InvcDtl.Rpt1InListPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InListPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InListPrice.ToString().Replace("`", "-"));
        }

        // Rpt2InListPrice
        if (modifiedDtl.Rpt2InListPrice != originalDtl.Rpt2InListPrice)
        {
            changes.Add($"InvcDtl.Rpt2InListPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InListPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InListPrice.ToString().Replace("`", "-"));
        }

        // Rpt3InListPrice
        if (modifiedDtl.Rpt3InListPrice != originalDtl.Rpt3InListPrice)
        {
            changes.Add($"InvcDtl.Rpt3InListPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InListPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InListPrice.ToString().Replace("`", "-"));
        }

        // Rpt1InOrdBasedPrice
        if (modifiedDtl.Rpt1InOrdBasedPrice != originalDtl.Rpt1InOrdBasedPrice)
        {
            changes.Add($"InvcDtl.Rpt1InOrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InOrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InOrdBasedPrice.ToString().Replace("`", "-"));
        }

        // Rpt2InOrdBasedPrice
        if (modifiedDtl.Rpt2InOrdBasedPrice != originalDtl.Rpt2InOrdBasedPrice)
        {
            changes.Add($"InvcDtl.Rpt2InOrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InOrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InOrdBasedPrice.ToString().Replace("`", "-"));
        }

        // Rpt3InOrdBasedPrice
        if (modifiedDtl.Rpt3InOrdBasedPrice != originalDtl.Rpt3InOrdBasedPrice)
        {
            changes.Add($"InvcDtl.Rpt3InOrdBasedPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InOrdBasedPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InOrdBasedPrice.ToString().Replace("`", "-"));
        }

        // Rpt1InTotalMiscChrg
        if (modifiedDtl.Rpt1InTotalMiscChrg != originalDtl.Rpt1InTotalMiscChrg)
        {
            changes.Add($"InvcDtl.Rpt1InTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt2InTotalMiscChrg
        if (modifiedDtl.Rpt2InTotalMiscChrg != originalDtl.Rpt2InTotalMiscChrg)
        {
            changes.Add($"InvcDtl.Rpt2InTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt3InTotalMiscChrg
        if (modifiedDtl.Rpt3InTotalMiscChrg != originalDtl.Rpt3InTotalMiscChrg)
        {
            changes.Add($"InvcDtl.Rpt3InTotalMiscChrg[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InTotalMiscChrg.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InTotalMiscChrg.ToString().Replace("`", "-"));
        }

        // Rpt1InUnitPrice
        if (modifiedDtl.Rpt1InUnitPrice != originalDtl.Rpt1InUnitPrice)
        {
            changes.Add($"InvcDtl.Rpt1InUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InUnitPrice.ToString().Replace("`", "-"));
        }

        // Rpt2InUnitPrice
        if (modifiedDtl.Rpt2InUnitPrice != originalDtl.Rpt2InUnitPrice)
        {
            changes.Add($"InvcDtl.Rpt2InUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InUnitPrice.ToString().Replace("`", "-"));
        }

        // Rpt3InUnitPrice
        if (modifiedDtl.Rpt3InUnitPrice != originalDtl.Rpt3InUnitPrice)
        {
            changes.Add($"InvcDtl.Rpt3InUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InUnitPrice.ToString().Replace("`", "-"));
        }

        // CorrectionDtl
        if (modifiedDtl.CorrectionDtl != originalDtl.CorrectionDtl)
        {
            changes.Add($"InvcDtl.CorrectionDtl[Line {invoiceLine}]`"
                       + originalDtl.CorrectionDtl.ToString()
                       + "`"
                       + modifiedDtl.CorrectionDtl.ToString().Replace("`", "-"));
        }

        // AssetNum
        if (modifiedDtl.AssetNum != originalDtl.AssetNum)
        {
            changes.Add($"InvcDtl.AssetNum[Line {invoiceLine}]`"
                       + originalDtl.AssetNum.ToString()
                       + "`"
                       + modifiedDtl.AssetNum.ToString().Replace("`", "-"));
        }

        // DisposalNum
        if (modifiedDtl.DisposalNum != originalDtl.DisposalNum)
        {
            changes.Add($"InvcDtl.DisposalNum[Line {invoiceLine}]`"
                       + originalDtl.DisposalNum.ToString()
                       + "`"
                       + modifiedDtl.DisposalNum.ToString().Replace("`", "-"));
        }

        // PBLineType
        if (modifiedDtl.PBLineType != originalDtl.PBLineType)
        {
            changes.Add($"InvcDtl.PBLineType[Line {invoiceLine}]`"
                       + originalDtl.PBLineType.ToString()
                       + "`"
                       + modifiedDtl.PBLineType.ToString().Replace("`", "-"));
        }

        // InvoiceLineRef
        if (modifiedDtl.InvoiceLineRef != originalDtl.InvoiceLineRef)
        {
            changes.Add($"InvcDtl.InvoiceLineRef[Line {invoiceLine}]`"
                       + originalDtl.InvoiceLineRef.ToString()
                       + "`"
                       + modifiedDtl.InvoiceLineRef.ToString().Replace("`", "-"));
        }

        // InvoiceRef
        if (modifiedDtl.InvoiceRef != originalDtl.InvoiceRef)
        {
            changes.Add($"InvcDtl.InvoiceRef[Line {invoiceLine}]`"
                       + originalDtl.InvoiceRef.ToString()
                       + "`"
                       + modifiedDtl.InvoiceRef.ToString().Replace("`", "-"));
        }

        // LotNum
        if (modifiedDtl.LotNum != originalDtl.LotNum)
        {
            changes.Add($"InvcDtl.LotNum[Line {invoiceLine}]`"
                       + originalDtl.LotNum.ToString()
                       + "`"
                       + modifiedDtl.LotNum.ToString().Replace("`", "-"));
        }

        // PBInvoiceLine
        if (modifiedDtl.PBInvoiceLine != originalDtl.PBInvoiceLine)
        {
            changes.Add($"InvcDtl.PBInvoiceLine[Line {invoiceLine}]`"
                       + originalDtl.PBInvoiceLine.ToString()
                       + "`"
                       + modifiedDtl.PBInvoiceLine.ToString().Replace("`", "-"));
        }

        // RAID
        if (modifiedDtl.RAID != originalDtl.RAID)
        {
            changes.Add($"InvcDtl.RAID[Line {invoiceLine}]`"
                       + originalDtl.RAID.ToString()
                       + "`"
                       + modifiedDtl.RAID.ToString().Replace("`", "-"));
        }

        // RADtlID
        if (modifiedDtl.RADtlID != originalDtl.RADtlID)
        {
            changes.Add($"InvcDtl.RADtlID[Line {invoiceLine}]`"
                       + originalDtl.RADtlID.ToString()
                       + "`"
                       + modifiedDtl.RADtlID.ToString().Replace("`", "-"));
        }

        // DeferredRev
        if (modifiedDtl.DeferredRev != originalDtl.DeferredRev)
        {
            changes.Add($"InvcDtl.DeferredRev[Line {invoiceLine}]`"
                       + originalDtl.DeferredRev.ToString()
                       + "`"
                       + modifiedDtl.DeferredRev.ToString().Replace("`", "-"));
        }

        // RACode
        if (modifiedDtl.RACode != originalDtl.RACode)
        {
            changes.Add($"InvcDtl.RACode[Line {invoiceLine}]`"
                       + originalDtl.RACode.ToString()
                       + "`"
                       + modifiedDtl.RACode.ToString().Replace("`", "-"));
        }

        // DefRevStart
        if (modifiedDtl.DefRevStart != originalDtl.DefRevStart)
        {
            changes.Add($"InvcDtl.DefRevStart[Line {invoiceLine}]`"
                       + originalDtl.DefRevStart.ToString()
                       + "`"
                       + modifiedDtl.DefRevStart.ToString().Replace("`", "-"));
        }

        // ChargeDefRev
        if (modifiedDtl.ChargeDefRev != originalDtl.ChargeDefRev)
        {
            changes.Add($"InvcDtl.ChargeDefRev[Line {invoiceLine}]`"
                       + originalDtl.ChargeDefRev.ToString()
                       + "`"
                       + modifiedDtl.ChargeDefRev.ToString().Replace("`", "-"));
        }

        // RenewalNbr
        if (modifiedDtl.RenewalNbr != originalDtl.RenewalNbr)
        {
            changes.Add($"InvcDtl.RenewalNbr[Line {invoiceLine}]`"
                       + originalDtl.RenewalNbr.ToString()
                       + "`"
                       + modifiedDtl.RenewalNbr.ToString().Replace("`", "-"));
        }

        // DefRevPosted
        if (modifiedDtl.DefRevPosted != originalDtl.DefRevPosted)
        {
            changes.Add($"InvcDtl.DefRevPosted[Line {invoiceLine}]`"
                       + originalDtl.DefRevPosted.ToString()
                       + "`"
                       + modifiedDtl.DefRevPosted.ToString().Replace("`", "-"));
        }

        // LinkedInvcUnitPrice
        if (modifiedDtl.LinkedInvcUnitPrice != originalDtl.LinkedInvcUnitPrice)
        {
            changes.Add($"InvcDtl.LinkedInvcUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.LinkedInvcUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.LinkedInvcUnitPrice.ToString().Replace("`", "-"));
        }

        // DspWithholdAmt
        if (modifiedDtl.DspWithholdAmt != originalDtl.DspWithholdAmt)
        {
            changes.Add($"InvcDtl.DspWithholdAmt[Line {invoiceLine}]`"
                       + originalDtl.DspWithholdAmt.ToString()
                       + "`"
                       + modifiedDtl.DspWithholdAmt.ToString().Replace("`", "-"));
        }

        // DocDspWithholdAmt
        if (modifiedDtl.DocDspWithholdAmt != originalDtl.DocDspWithholdAmt)
        {
            changes.Add($"InvcDtl.DocDspWithholdAmt[Line {invoiceLine}]`"
                       + originalDtl.DocDspWithholdAmt.ToString()
                       + "`"
                       + modifiedDtl.DocDspWithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt1DspWithholdAmt
        if (modifiedDtl.Rpt1DspWithholdAmt != originalDtl.Rpt1DspWithholdAmt)
        {
            changes.Add($"InvcDtl.Rpt1DspWithholdAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt1DspWithholdAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt1DspWithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt2DspWithholdAmt
        if (modifiedDtl.Rpt2DspWithholdAmt != originalDtl.Rpt2DspWithholdAmt)
        {
            changes.Add($"InvcDtl.Rpt2DspWithholdAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt2DspWithholdAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt2DspWithholdAmt.ToString().Replace("`", "-"));
        }

        // Rpt3DspWithholdAmt
        if (modifiedDtl.Rpt3DspWithholdAmt != originalDtl.Rpt3DspWithholdAmt)
        {
            changes.Add($"InvcDtl.Rpt3DspWithholdAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt3DspWithholdAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt3DspWithholdAmt.ToString().Replace("`", "-"));
        }

        // LinkedCurrencyCode
        if (modifiedDtl.LinkedCurrencyCode != originalDtl.LinkedCurrencyCode)
        {
            changes.Add($"InvcDtl.LinkedCurrencyCode[Line {invoiceLine}]`"
                       + originalDtl.LinkedCurrencyCode.ToString()
                       + "`"
                       + modifiedDtl.LinkedCurrencyCode.ToString().Replace("`", "-"));
        }

        // PhaseID
        if (modifiedDtl.PhaseID != originalDtl.PhaseID)
        {
            changes.Add($"InvcDtl.PhaseID[Line {invoiceLine}]`"
                       + originalDtl.PhaseID.ToString()
                       + "`"
                       + modifiedDtl.PhaseID.ToString().Replace("`", "-"));
        }

        // PEBOEHeadNum
        if (modifiedDtl.PEBOEHeadNum != originalDtl.PEBOEHeadNum)
        {
            changes.Add($"InvcDtl.PEBOEHeadNum[Line {invoiceLine}]`"
                       + originalDtl.PEBOEHeadNum.ToString()
                       + "`"
                       + modifiedDtl.PEBOEHeadNum.ToString().Replace("`", "-"));
        }

        // MXSellingShipQty
        if (modifiedDtl.MXSellingShipQty != originalDtl.MXSellingShipQty)
        {
            changes.Add($"InvcDtl.MXSellingShipQty[Line {invoiceLine}]`"
                       + originalDtl.MXSellingShipQty.ToString()
                       + "`"
                       + modifiedDtl.MXSellingShipQty.ToString().Replace("`", "-"));
        }

        // MXUnitPrice
        if (modifiedDtl.MXUnitPrice != originalDtl.MXUnitPrice)
        {
            changes.Add($"InvcDtl.MXUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.MXUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.MXUnitPrice.ToString().Replace("`", "-"));
        }

        // DocMXUnitPrice
        if (modifiedDtl.DocMXUnitPrice != originalDtl.DocMXUnitPrice)
        {
            changes.Add($"InvcDtl.DocMXUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.DocMXUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.DocMXUnitPrice.ToString().Replace("`", "-"));
        }

        // Rpt1MXUnitPrice
        if (modifiedDtl.Rpt1MXUnitPrice != originalDtl.Rpt1MXUnitPrice)
        {
            changes.Add($"InvcDtl.Rpt1MXUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt1MXUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt1MXUnitPrice.ToString().Replace("`", "-"));
        }

        // Rpt2MXUnitPrice
        if (modifiedDtl.Rpt2MXUnitPrice != originalDtl.Rpt2MXUnitPrice)
        {
            changes.Add($"InvcDtl.Rpt2MXUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt2MXUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt2MXUnitPrice.ToString().Replace("`", "-"));
        }

        // Rpt3MXUnitPrice
        if (modifiedDtl.Rpt3MXUnitPrice != originalDtl.Rpt3MXUnitPrice)
        {
            changes.Add($"InvcDtl.Rpt3MXUnitPrice[Line {invoiceLine}]`"
                       + originalDtl.Rpt3MXUnitPrice.ToString()
                       + "`"
                       + modifiedDtl.Rpt3MXUnitPrice.ToString().Replace("`", "-"));
        }

        // CustCostCenter
        if (modifiedDtl.CustCostCenter != originalDtl.CustCostCenter)
        {
            changes.Add($"InvcDtl.CustCostCenter[Line {invoiceLine}]`"
                       + originalDtl.CustCostCenter.ToString()
                       + "`"
                       + modifiedDtl.CustCostCenter.ToString().Replace("`", "-"));
        }

        // DEIsSecurityFinancialDerivative
        if (modifiedDtl.DEIsSecurityFinancialDerivative != originalDtl.DEIsSecurityFinancialDerivative)
        {
            changes.Add($"InvcDtl.DEIsSecurityFinancialDerivative[Line {invoiceLine}]`"
                       + originalDtl.DEIsSecurityFinancialDerivative.ToString()
                       + "`"
                       + modifiedDtl.DEIsSecurityFinancialDerivative.ToString().Replace("`", "-"));
        }

        // DEInternationalSecuritiesID
        if (modifiedDtl.DEInternationalSecuritiesID != originalDtl.DEInternationalSecuritiesID)
        {
            changes.Add($"InvcDtl.DEInternationalSecuritiesID[Line {invoiceLine}]`"
                       + originalDtl.DEInternationalSecuritiesID.ToString()
                       + "`"
                       + modifiedDtl.DEInternationalSecuritiesID.ToString().Replace("`", "-"));
        }

        // DEIsInvestment
        if (modifiedDtl.DEIsInvestment != originalDtl.DEIsInvestment)
        {
            changes.Add($"InvcDtl.DEIsInvestment[Line {invoiceLine}]`"
                       + originalDtl.DEIsInvestment.ToString()
                       + "`"
                       + modifiedDtl.DEIsInvestment.ToString().Replace("`", "-"));
        }

        // DEPayStatCode
        if (modifiedDtl.DEPayStatCode != originalDtl.DEPayStatCode)
        {
            changes.Add($"InvcDtl.DEPayStatCode[Line {invoiceLine}]`"
                       + originalDtl.DEPayStatCode.ToString()
                       + "`"
                       + modifiedDtl.DEPayStatCode.ToString().Replace("`", "-"));
        }

        // DefRevEndDate
        if (modifiedDtl.DefRevEndDate != originalDtl.DefRevEndDate)
        {
            changes.Add($"InvcDtl.DefRevEndDate[Line {invoiceLine}]`"
                       + originalDtl.DefRevEndDate.ToString()
                       + "`"
                       + modifiedDtl.DefRevEndDate.ToString().Replace("`", "-"));
        }

        // EntityUseCode
        if (modifiedDtl.EntityUseCode != originalDtl.EntityUseCode)
        {
            changes.Add($"InvcDtl.EntityUseCode[Line {invoiceLine}]`"
                       + originalDtl.EntityUseCode.ToString()
                       + "`"
                       + modifiedDtl.EntityUseCode.ToString().Replace("`", "-"));
        }

        // Reclassified
        if (modifiedDtl.Reclassified != originalDtl.Reclassified)
        {
            changes.Add($"InvcDtl.Reclassified[Line {invoiceLine}]`"
                       + originalDtl.Reclassified.ToString()
                       + "`"
                       + modifiedDtl.Reclassified.ToString().Replace("`", "-"));
        }

        // PartiallyDefer
        if (modifiedDtl.PartiallyDefer != originalDtl.PartiallyDefer)
        {
            changes.Add($"InvcDtl.PartiallyDefer[Line {invoiceLine}]`"
                       + originalDtl.PartiallyDefer.ToString()
                       + "`"
                       + modifiedDtl.PartiallyDefer.ToString().Replace("`", "-"));
        }

        // DeferredPercent
        if (modifiedDtl.DeferredPercent != originalDtl.DeferredPercent)
        {
            changes.Add($"InvcDtl.DeferredPercent[Line {invoiceLine}]`"
                       + originalDtl.DeferredPercent.ToString()
                       + "`"
                       + modifiedDtl.DeferredPercent.ToString().Replace("`", "-"));
        }

        // Reclass
        if (modifiedDtl.Reclass != originalDtl.Reclass)
        {
            changes.Add($"InvcDtl.Reclass[Line {invoiceLine}]`"
                       + originalDtl.Reclass.ToString()
                       + "`"
                       + modifiedDtl.Reclass.ToString().Replace("`", "-"));
        }

        // DeferredOnly
        if (modifiedDtl.DeferredOnly != originalDtl.DeferredOnly)
        {
            changes.Add($"InvcDtl.DeferredOnly[Line {invoiceLine}]`"
                       + originalDtl.DeferredOnly.ToString()
                       + "`"
                       + modifiedDtl.DeferredOnly.ToString().Replace("`", "-"));
        }

        // ReclassCodeID
        if (modifiedDtl.ReclassCodeID != originalDtl.ReclassCodeID)
        {
            changes.Add($"InvcDtl.ReclassCodeID[Line {invoiceLine}]`"
                       + originalDtl.ReclassCodeID.ToString()
                       + "`"
                       + modifiedDtl.ReclassCodeID.ToString().Replace("`", "-"));
        }

        // ReclassReasonCode
        if (modifiedDtl.ReclassReasonCode != originalDtl.ReclassReasonCode)
        {
            changes.Add($"InvcDtl.ReclassReasonCode[Line {invoiceLine}]`"
                       + originalDtl.ReclassReasonCode.ToString()
                       + "`"
                       + modifiedDtl.ReclassReasonCode.ToString().Replace("`", "-"));
        }

        // ReclassComments
        if (modifiedDtl.ReclassComments != originalDtl.ReclassComments)
        {
            changes.Add($"InvcDtl.ReclassComments[Line {invoiceLine}]`"
                       + originalDtl.ReclassComments.ToString()
                       + "`"
                       + modifiedDtl.ReclassComments.ToString().Replace("`", "-"));
        }

        // DeferredRevAmt
        if (modifiedDtl.DeferredRevAmt != originalDtl.DeferredRevAmt)
        {
            changes.Add($"InvcDtl.DeferredRevAmt[Line {invoiceLine}]`"
                       + originalDtl.DeferredRevAmt.ToString()
                       + "`"
                       + modifiedDtl.DeferredRevAmt.ToString().Replace("`", "-"));
        }

        // DocDeferredRevAmt
        if (modifiedDtl.DocDeferredRevAmt != originalDtl.DocDeferredRevAmt)
        {
            changes.Add($"InvcDtl.DocDeferredRevAmt[Line {invoiceLine}]`"
                       + originalDtl.DocDeferredRevAmt.ToString()
                       + "`"
                       + modifiedDtl.DocDeferredRevAmt.ToString().Replace("`", "-"));
        }

        // Rpt1DeferredRevAmt
        if (modifiedDtl.Rpt1DeferredRevAmt != originalDtl.Rpt1DeferredRevAmt)
        {
            changes.Add($"InvcDtl.Rpt1DeferredRevAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt1DeferredRevAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt1DeferredRevAmt.ToString().Replace("`", "-"));
        }

        // Rpt2DeferredRevAmt
        if (modifiedDtl.Rpt2DeferredRevAmt != originalDtl.Rpt2DeferredRevAmt)
        {
            changes.Add($"InvcDtl.Rpt2DeferredRevAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt2DeferredRevAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt2DeferredRevAmt.ToString().Replace("`", "-"));
        }

        // Rpt3DeferredRevAmt
        if (modifiedDtl.Rpt3DeferredRevAmt != originalDtl.Rpt3DeferredRevAmt)
        {
            changes.Add($"InvcDtl.Rpt3DeferredRevAmt[Line {invoiceLine}]`"
                       + originalDtl.Rpt3DeferredRevAmt.ToString()
                       + "`"
                       + modifiedDtl.Rpt3DeferredRevAmt.ToString().Replace("`", "-"));
        }

        // ChargeReclass
        if (modifiedDtl.ChargeReclass != originalDtl.ChargeReclass)
        {
            changes.Add($"InvcDtl.ChargeReclass[Line {invoiceLine}]`"
                       + originalDtl.ChargeReclass.ToString()
                       + "`"
                       + modifiedDtl.ChargeReclass.ToString().Replace("`", "-"));
        }

        // DEDenomination
        if (modifiedDtl.DEDenomination != originalDtl.DEDenomination)
        {
            changes.Add($"InvcDtl.DEDenomination[Line {invoiceLine}]`"
                       + originalDtl.DEDenomination.ToString()
                       + "`"
                       + modifiedDtl.DEDenomination.ToString().Replace("`", "-"));
        }

        // DropShipPONum
        if (modifiedDtl.DropShipPONum != originalDtl.DropShipPONum)
        {
            changes.Add($"InvcDtl.DropShipPONum[Line {invoiceLine}]`"
                       + originalDtl.DropShipPONum.ToString()
                       + "`"
                       + modifiedDtl.DropShipPONum.ToString().Replace("`", "-"));
        }

        // DocInAdvanceBillCredit
        if (modifiedDtl.DocInAdvanceBillCredit != originalDtl.DocInAdvanceBillCredit)
        {
            changes.Add($"InvcDtl.DocInAdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.DocInAdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.DocInAdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // InAdvanceBillCredit
        if (modifiedDtl.InAdvanceBillCredit != originalDtl.InAdvanceBillCredit)
        {
            changes.Add($"InvcDtl.InAdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.InAdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.InAdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // Rpt1InAdvanceBillCredit
        if (modifiedDtl.Rpt1InAdvanceBillCredit != originalDtl.Rpt1InAdvanceBillCredit)
        {
            changes.Add($"InvcDtl.Rpt1InAdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InAdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InAdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // Rpt2InAdvanceBillCredit
        if (modifiedDtl.Rpt2InAdvanceBillCredit != originalDtl.Rpt2InAdvanceBillCredit)
        {
            changes.Add($"InvcDtl.Rpt2InAdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InAdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InAdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // Rpt3InAdvanceBillCredit
        if (modifiedDtl.Rpt3InAdvanceBillCredit != originalDtl.Rpt3InAdvanceBillCredit)
        {
            changes.Add($"InvcDtl.Rpt3InAdvanceBillCredit[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InAdvanceBillCredit.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InAdvanceBillCredit.ToString().Replace("`", "-"));
        }

        // MYIndustryCode
        if (modifiedDtl.MYIndustryCode != originalDtl.MYIndustryCode)
        {
            changes.Add($"InvcDtl.MYIndustryCode[Line {invoiceLine}]`"
                       + originalDtl.MYIndustryCode.ToString()
                       + "`"
                       + modifiedDtl.MYIndustryCode.ToString().Replace("`", "-"));
        }

        // DockingStation
        if (modifiedDtl.DockingStation != originalDtl.DockingStation)
        {
            changes.Add($"InvcDtl.DockingStation[Line {invoiceLine}]`"
                       + originalDtl.DockingStation.ToString()
                       + "`"
                       + modifiedDtl.DockingStation.ToString().Replace("`", "-"));
        }

        // ConsolidateLines
        if (modifiedDtl.ConsolidateLines != originalDtl.ConsolidateLines)
        {
            changes.Add($"InvcDtl.ConsolidateLines[Line {invoiceLine}]`"
                       + originalDtl.ConsolidateLines.ToString()
                       + "`"
                       + modifiedDtl.ConsolidateLines.ToString().Replace("`", "-"));
        }

        // MXCustomsDuty
        if (modifiedDtl.MXCustomsDuty != originalDtl.MXCustomsDuty)
        {
            changes.Add($"InvcDtl.MXCustomsDuty[Line {invoiceLine}]`"
                       + originalDtl.MXCustomsDuty.ToString()
                       + "`"
                       + modifiedDtl.MXCustomsDuty.ToString().Replace("`", "-"));
        }

        // CommodityCode
        if (modifiedDtl.CommodityCode != originalDtl.CommodityCode)
        {
            changes.Add($"InvcDtl.CommodityCode[Line {invoiceLine}]`"
                       + originalDtl.CommodityCode.ToString()
                       + "`"
                       + modifiedDtl.CommodityCode.ToString().Replace("`", "-"));
        }

        // MXProdServCode
        if (modifiedDtl.MXProdServCode != originalDtl.MXProdServCode)
        {
            changes.Add($"InvcDtl.MXProdServCode[Line {invoiceLine}]`"
                       + originalDtl.MXProdServCode.ToString()
                       + "`"
                       + modifiedDtl.MXProdServCode.ToString().Replace("`", "-"));
        }

        // QuoteNum
        if (modifiedDtl.QuoteNum != originalDtl.QuoteNum)
        {
            changes.Add($"InvcDtl.QuoteNum[Line {invoiceLine}]`"
                       + originalDtl.QuoteNum.ToString()
                       + "`"
                       + modifiedDtl.QuoteNum.ToString().Replace("`", "-"));
        }

        // QuoteLine
        if (modifiedDtl.QuoteLine != originalDtl.QuoteLine)
        {
            changes.Add($"InvcDtl.QuoteLine[Line {invoiceLine}]`"
                       + originalDtl.QuoteLine.ToString()
                       + "`"
                       + modifiedDtl.QuoteLine.ToString().Replace("`", "-"));
        }

        // EpicorFSA
        if (modifiedDtl.EpicorFSA != originalDtl.EpicorFSA)
        {
            changes.Add($"InvcDtl.EpicorFSA[Line {invoiceLine}]`"
                       + originalDtl.EpicorFSA.ToString()
                       + "`"
                       + modifiedDtl.EpicorFSA.ToString().Replace("`", "-"));
        }

        // MXCustomsUMFrom
        if (modifiedDtl.MXCustomsUMFrom != originalDtl.MXCustomsUMFrom)
        {
            changes.Add($"InvcDtl.MXCustomsUMFrom[Line {invoiceLine}]`"
                       + originalDtl.MXCustomsUMFrom.ToString()
                       + "`"
                       + modifiedDtl.MXCustomsUMFrom.ToString().Replace("`", "-"));
        }

        // PEDetrGoodServiceCode
        if (modifiedDtl.PEDetrGoodServiceCode != originalDtl.PEDetrGoodServiceCode)
        {
            changes.Add($"InvcDtl.PEDetrGoodServiceCode[Line {invoiceLine}]`"
                       + originalDtl.PEDetrGoodServiceCode.ToString()
                       + "`"
                       + modifiedDtl.PEDetrGoodServiceCode.ToString().Replace("`", "-"));
        }

        // PETaxExempt
        if (modifiedDtl.PETaxExempt != originalDtl.PETaxExempt)
        {
            changes.Add($"InvcDtl.PETaxExempt[Line {invoiceLine}]`"
                       + originalDtl.PETaxExempt.ToString()
                       + "`"
                       + modifiedDtl.PETaxExempt.ToString().Replace("`", "-"));
        }

        // CColOrderNum
        if (modifiedDtl.CColOrderNum != originalDtl.CColOrderNum)
        {
            changes.Add($"InvcDtl.CColOrderNum[Line {invoiceLine}]`"
                       + originalDtl.CColOrderNum.ToString()
                       + "`"
                       + modifiedDtl.CColOrderNum.ToString().Replace("`", "-"));
        }

        // CColOrderLine
        if (modifiedDtl.CColOrderLine != originalDtl.CColOrderLine)
        {
            changes.Add($"InvcDtl.CColOrderLine[Line {invoiceLine}]`"
                       + originalDtl.CColOrderLine.ToString()
                       + "`"
                       + modifiedDtl.CColOrderLine.ToString().Replace("`", "-"));
        }

        // CColOrderRel
        if (modifiedDtl.CColOrderRel != originalDtl.CColOrderRel)
        {
            changes.Add($"InvcDtl.CColOrderRel[Line {invoiceLine}]`"
                       + originalDtl.CColOrderRel.ToString()
                       + "`"
                       + modifiedDtl.CColOrderRel.ToString().Replace("`", "-"));
        }

        // CColInvoiceLineRef
        if (modifiedDtl.CColInvoiceLineRef != originalDtl.CColInvoiceLineRef)
        {
            changes.Add($"InvcDtl.CColInvoiceLineRef[Line {invoiceLine}]`"
                       + originalDtl.CColInvoiceLineRef.ToString()
                       + "`"
                       + modifiedDtl.CColInvoiceLineRef.ToString().Replace("`", "-"));
        }

        // CColPackNum
        if (modifiedDtl.CColPackNum != originalDtl.CColPackNum)
        {
            changes.Add($"InvcDtl.CColPackNum[Line {invoiceLine}]`"
                       + originalDtl.CColPackNum.ToString()
                       + "`"
                       + modifiedDtl.CColPackNum.ToString().Replace("`", "-"));
        }

        // CColPackLine
        if (modifiedDtl.CColPackLine != originalDtl.CColPackLine)
        {
            changes.Add($"InvcDtl.CColPackLine[Line {invoiceLine}]`"
                       + originalDtl.CColPackLine.ToString()
                       + "`"
                       + modifiedDtl.CColPackLine.ToString().Replace("`", "-"));
        }

        // CColDropShipPackSlip
        if (modifiedDtl.CColDropShipPackSlip != originalDtl.CColDropShipPackSlip)
        {
            changes.Add($"InvcDtl.CColDropShipPackSlip[Line {invoiceLine}]`"
                       + originalDtl.CColDropShipPackSlip.ToString()
                       + "`"
                       + modifiedDtl.CColDropShipPackSlip.ToString().Replace("`", "-"));
        }

        // CColDropShipPackSlipLine
        if (modifiedDtl.CColDropShipPackSlipLine != originalDtl.CColDropShipPackSlipLine)
        {
            changes.Add($"InvcDtl.CColDropShipPackSlipLine[Line {invoiceLine}]`"
                       + originalDtl.CColDropShipPackSlipLine.ToString()
                       + "`"
                       + modifiedDtl.CColDropShipPackSlipLine.ToString().Replace("`", "-"));
        }

        // CColShipToCustID
        if (modifiedDtl.CColShipToCustID != originalDtl.CColShipToCustID)
        {
            changes.Add($"InvcDtl.CColShipToCustID[Line {invoiceLine}]`"
                       + originalDtl.CColShipToCustID.ToString()
                       + "`"
                       + modifiedDtl.CColShipToCustID.ToString().Replace("`", "-"));
        }

        // CColShipToNum
        if (modifiedDtl.CColShipToNum != originalDtl.CColShipToNum)
        {
            changes.Add($"InvcDtl.CColShipToNum[Line {invoiceLine}]`"
                       + originalDtl.CColShipToNum.ToString()
                       + "`"
                       + modifiedDtl.CColShipToNum.ToString().Replace("`", "-"));
        }

        // AttributeSetID
        if (modifiedDtl.AttributeSetID != originalDtl.AttributeSetID)
        {
            changes.Add($"InvcDtl.AttributeSetID[Line {invoiceLine}]`"
                       + originalDtl.AttributeSetID.ToString()
                       + "`"
                       + modifiedDtl.AttributeSetID.ToString().Replace("`", "-"));
        }

        // ExemptReasonCode
        if (modifiedDtl.ExemptReasonCode != originalDtl.ExemptReasonCode)
        {
            changes.Add($"InvcDtl.ExemptReasonCode[Line {invoiceLine}]`"
                       + originalDtl.ExemptReasonCode.ToString()
                       + "`"
                       + modifiedDtl.ExemptReasonCode.ToString().Replace("`", "-"));
        }

        // JobNum
        if (modifiedDtl.JobNum != originalDtl.JobNum)
        {
            changes.Add($"InvcDtl.JobNum[Line {invoiceLine}]`"
                       + originalDtl.JobNum.ToString()
                       + "`"
                       + modifiedDtl.JobNum.ToString().Replace("`", "-"));
        }

        // ServiceSource
        if (modifiedDtl.ServiceSource != originalDtl.ServiceSource)
        {
            changes.Add($"InvcDtl.ServiceSource[Line {invoiceLine}]`"
                       + originalDtl.ServiceSource.ToString()
                       + "`"
                       + modifiedDtl.ServiceSource.ToString().Replace("`", "-"));
        }

        // OTSTaxValidationStatus
        if (modifiedDtl.OTSTaxValidationStatus != originalDtl.OTSTaxValidationStatus)
        {
            changes.Add($"InvcDtl.OTSTaxValidationStatus[Line {invoiceLine}]`"
                       + originalDtl.OTSTaxValidationStatus.ToString()
                       + "`"
                       + modifiedDtl.OTSTaxValidationStatus.ToString().Replace("`", "-"));
        }

        // OTSTaxValidationDate
        if (modifiedDtl.OTSTaxValidationDate != originalDtl.OTSTaxValidationDate)
        {
            changes.Add($"InvcDtl.OTSTaxValidationDate[Line {invoiceLine}]`"
                       + originalDtl.OTSTaxValidationDate.ToString()
                       + "`"
                       + modifiedDtl.OTSTaxValidationDate.ToString().Replace("`", "-"));
        }

        // AssemblySeq
        if (modifiedDtl.AssemblySeq != originalDtl.AssemblySeq)
        {
            changes.Add($"InvcDtl.AssemblySeq[Line {invoiceLine}]`"
                       + originalDtl.AssemblySeq.ToString()
                       + "`"
                       + modifiedDtl.AssemblySeq.ToString().Replace("`", "-"));
        }

        // MtlSeq
        if (modifiedDtl.MtlSeq != originalDtl.MtlSeq)
        {
            changes.Add($"InvcDtl.MtlSeq[Line {invoiceLine}]`"
                       + originalDtl.MtlSeq.ToString()
                       + "`"
                       + modifiedDtl.MtlSeq.ToString().Replace("`", "-"));
        }

        // OprSeq
        if (modifiedDtl.OprSeq != originalDtl.OprSeq)
        {
            changes.Add($"InvcDtl.OprSeq[Line {invoiceLine}]`"
                       + originalDtl.OprSeq.ToString()
                       + "`"
                       + modifiedDtl.OprSeq.ToString().Replace("`", "-"));
        }

        // LaborType
        if (modifiedDtl.LaborType != originalDtl.LaborType)
        {
            changes.Add($"InvcDtl.LaborType[Line {invoiceLine}]`"
                       + originalDtl.LaborType.ToString()
                       + "`"
                       + modifiedDtl.LaborType.ToString().Replace("`", "-"));
        }

        // BillableLaborHrs
        if (modifiedDtl.BillableLaborHrs != originalDtl.BillableLaborHrs)
        {
            changes.Add($"InvcDtl.BillableLaborHrs[Line {invoiceLine}]`"
                       + originalDtl.BillableLaborHrs.ToString()
                       + "`"
                       + modifiedDtl.BillableLaborHrs.ToString().Replace("`", "-"));
        }

        // BillableLaborRate
        if (modifiedDtl.BillableLaborRate != originalDtl.BillableLaborRate)
        {
            changes.Add($"InvcDtl.BillableLaborRate[Line {invoiceLine}]`"
                       + originalDtl.BillableLaborRate.ToString()
                       + "`"
                       + modifiedDtl.BillableLaborRate.ToString().Replace("`", "-"));
        }

        // ServiceSourceType
        if (modifiedDtl.ServiceSourceType != originalDtl.ServiceSourceType)
        {
            changes.Add($"InvcDtl.ServiceSourceType[Line {invoiceLine}]`"
                       + originalDtl.ServiceSourceType.ToString()
                       + "`"
                       + modifiedDtl.ServiceSourceType.ToString().Replace("`", "-"));
        }

        // TotalCovenantDiscount
        if (modifiedDtl.TotalCovenantDiscount != originalDtl.TotalCovenantDiscount)
        {
            changes.Add($"InvcDtl.TotalCovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.TotalCovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.TotalCovenantDiscount.ToString().Replace("`", "-"));
        }

        // DocCovenantDiscount
        if (modifiedDtl.DocCovenantDiscount != originalDtl.DocCovenantDiscount)
        {
            changes.Add($"InvcDtl.DocCovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.DocCovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.DocCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt1CovenantDiscount
        if (modifiedDtl.Rpt1CovenantDiscount != originalDtl.Rpt1CovenantDiscount)
        {
            changes.Add($"InvcDtl.Rpt1CovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt1CovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt1CovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt2CovenantDiscount
        if (modifiedDtl.Rpt2CovenantDiscount != originalDtl.Rpt2CovenantDiscount)
        {
            changes.Add($"InvcDtl.Rpt2CovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt2CovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt2CovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt3CovenantDiscount
        if (modifiedDtl.Rpt3CovenantDiscount != originalDtl.Rpt3CovenantDiscount)
        {
            changes.Add($"InvcDtl.Rpt3CovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt3CovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt3CovenantDiscount.ToString().Replace("`", "-"));
        }

        // TotalInCovenantDiscount
        if (modifiedDtl.TotalInCovenantDiscount != originalDtl.TotalInCovenantDiscount)
        {
            changes.Add($"InvcDtl.TotalInCovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.TotalInCovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.TotalInCovenantDiscount.ToString().Replace("`", "-"));
        }

        // DocInCovenantDiscount
        if (modifiedDtl.DocInCovenantDiscount != originalDtl.DocInCovenantDiscount)
        {
            changes.Add($"InvcDtl.DocInCovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.DocInCovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.DocInCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt1InCovenantDiscount
        if (modifiedDtl.Rpt1InCovenantDiscount != originalDtl.Rpt1InCovenantDiscount)
        {
            changes.Add($"InvcDtl.Rpt1InCovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt1InCovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt1InCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt2InCovenantDiscount
        if (modifiedDtl.Rpt2InCovenantDiscount != originalDtl.Rpt2InCovenantDiscount)
        {
            changes.Add($"InvcDtl.Rpt2InCovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt2InCovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt2InCovenantDiscount.ToString().Replace("`", "-"));
        }

        // Rpt3InCovenantDiscount
        if (modifiedDtl.Rpt3InCovenantDiscount != originalDtl.Rpt3InCovenantDiscount)
        {
            changes.Add($"InvcDtl.Rpt3InCovenantDiscount[Line {invoiceLine}]`"
                       + originalDtl.Rpt3InCovenantDiscount.ToString()
                       + "`"
                       + modifiedDtl.Rpt3InCovenantDiscount.ToString().Replace("`", "-"));
        }

        // OTSDistrictName
        if (modifiedDtl.OTSDistrictName != originalDtl.OTSDistrictName)
        {
            changes.Add($"InvcDtl.OTSDistrictName[Line {invoiceLine}]`"
                       + originalDtl.OTSDistrictName.ToString()
                       + "`"
                       + modifiedDtl.OTSDistrictName.ToString().Replace("`", "-"));
        }

        // OTSStreetName
        if (modifiedDtl.OTSStreetName != originalDtl.OTSStreetName)
        {
            changes.Add($"InvcDtl.OTSStreetName[Line {invoiceLine}]`"
                       + originalDtl.OTSStreetName.ToString()
                       + "`"
                       + modifiedDtl.OTSStreetName.ToString().Replace("`", "-"));
        }

        // OTSBuildingNumber
        if (modifiedDtl.OTSBuildingNumber != originalDtl.OTSBuildingNumber)
        {
            changes.Add($"InvcDtl.OTSBuildingNumber[Line {invoiceLine}]`"
                       + originalDtl.OTSBuildingNumber.ToString()
                       + "`"
                       + modifiedDtl.OTSBuildingNumber.ToString().Replace("`", "-"));
        }

        // OTSFloor
        if (modifiedDtl.OTSFloor != originalDtl.OTSFloor)
        {
            changes.Add($"InvcDtl.OTSFloor[Line {invoiceLine}]`"
                       + originalDtl.OTSFloor.ToString()
                       + "`"
                       + modifiedDtl.OTSFloor.ToString().Replace("`", "-"));
        }

        // OTSRoom
        if (modifiedDtl.OTSRoom != originalDtl.OTSRoom)
        {
            changes.Add($"InvcDtl.OTSRoom[Line {invoiceLine}]`"
                       + originalDtl.OTSRoom.ToString()
                       + "`"
                       + modifiedDtl.OTSRoom.ToString().Replace("`", "-"));
        }

        // OTSPostBox
        if (modifiedDtl.OTSPostBox != originalDtl.OTSPostBox)
        {
            changes.Add($"InvcDtl.OTSPostBox[Line {invoiceLine}]`"
                       + originalDtl.OTSPostBox.ToString()
                       + "`"
                       + modifiedDtl.OTSPostBox.ToString().Replace("`", "-"));
        }

    }
}

// =================================================================
// Process Changes
// =================================================================

// If any changes were found, set the flag to call the function
if (changes.Count > 0)
{
    callFunc = true;

    // Join all changes with the delimiter
    string allChanges = string.Join("`~`", changes);
    int totalLength = allChanges.Length;
    int maxChunkSize = 1000;

    // Assign chunks based on position - each exactly 1000 chars (or remaining)
    if (totalLength > 0)
    {
        changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
    }
    if (totalLength > 1000)
    {
        changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
    }
    if (totalLength > 2000)
    {
        changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
    }
    if (totalLength > 3000)
    {
        changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
    }
    if (totalLength > 4000)
    {
        changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
    }
    if (totalLength > 5000)
    {
        changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
    }
    if (totalLength > 6000)
    {
        changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
    }
    if (totalLength > 7000)
    {
        changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
    }
    if (totalLength > 8000)
    {
        changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
    }
    if (totalLength > 9000)
    {
        changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
    }

    // Set appropriate debug message
    if (totalLength > 10000)
    {
        test2 = $"Warning: {totalLength} characters of changes detected, only first 10000 logged";
    }
    else
    {
        test2 = $"Debug: {changes.Count} changes detected, {totalLength} characters logged";
    }
}
else
{
    test2 = "Debug: No changes detected";
}
