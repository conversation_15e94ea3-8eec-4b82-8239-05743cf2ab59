// =================================================================
// Purchase Order Change Tracking BPM
// =================================================================
// This BPM tracks changes to POHeader and PODetail records and logs
// them for auditing purposes. It compares the current dataset with
// the original database records to identify what has changed.
// =================================================================

// =================================================================
// Initialize Variables
// =================================================================

// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;
PONumber = "";

// Validate dataset before processing
if (ds.POHeader == null || ds.POHeader.Count == 0)
{
    test1 = "Error: No purchase order data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.POHeader.Count.ToString()} purchase order records";

// Track all changes in a list
List<string> changes = new List<string>();

// =================================================================
// Process POHeader Changes
// =================================================================

// Get the modified purchase order record
var modifiedPOHeader = ds.POHeader[0];
int poNum = modifiedPOHeader.PONum;
PONumber = poNum.ToString();
test2 = "Debug: Successfully accessed ds.POHeader[0]";

// Check if the purchase order row has been deleted (RowMod = "D" in original record)
if (ds.POHeader[0].RowMod == "D")
{
    changes.Add("POHeader deleted");
    callFunc = true;
}
else
{
    // Get the original purchase order record from database
    var originalPOHeader = (from dbPOHeader in Db.POHeader
                           where dbPOHeader.Company == companyID
                              && dbPOHeader.PONum == poNum
                           select dbPOHeader).FirstOrDefault();

    // Handle new purchase order creation
    if (originalPOHeader == null)
    {
        changes.Add($"New purchase order created: {modifiedPOHeader.PONum.ToString()}");
        callFunc = true;
    }
    else
    {
        // Determine which record to use as the modified version
        // Check if there's a second record (modified version)
        if (ds.POHeader.Count > 1)
        {
            try
            {
                modifiedPOHeader = ds.POHeader[1];
                test2 = "Debug: Successfully accessed ds.POHeader[1]";
            }
            catch (System.Exception ex)
            {
                test2 = $"Error accessing ds.POHeader[1]: {ex.Message}";
                return;
            }
        }
        else
        {
            test2 = "Debug: Using ds.POHeader[0] as modified (only 1 record)";
        }

        // =================================================================
        // POHeader Field Comparisons
        // =================================================================

        // Company
        if (modifiedPOHeader.Company != originalPOHeader.Company)
        {
            changes.Add("POHeader.Company`"
                       + originalPOHeader.Company.ToString()
                       + "`"
                       + modifiedPOHeader.Company.ToString().Replace("`", "-"));
        }

        // Open Order
        if (modifiedPOHeader.OpenOrder != originalPOHeader.OpenOrder)
        {
            changes.Add("POHeader.OpenOrder`"
                       + originalPOHeader.OpenOrder.ToString()
                       + "`"
                       + modifiedPOHeader.OpenOrder.ToString().Replace("`", "-"));
        }

        // Void Order
        if (modifiedPOHeader.VoidOrder != originalPOHeader.VoidOrder)
        {
            changes.Add("POHeader.VoidOrder`"
                       + originalPOHeader.VoidOrder.ToString()
                       + "`"
                       + modifiedPOHeader.VoidOrder.ToString().Replace("`", "-"));
        }

        // PO Number
        if (modifiedPOHeader.PONum != originalPOHeader.PONum)
        {
            changes.Add("POHeader.PONum`"
                       + originalPOHeader.PONum.ToString()
                       + "`"
                       + modifiedPOHeader.PONum.ToString().Replace("`", "-"));
        }

        // Entry Person
        if (modifiedPOHeader.EntryPerson != originalPOHeader.EntryPerson)
        {
            changes.Add("POHeader.EntryPerson`"
                       + originalPOHeader.EntryPerson.ToString()
                       + "`"
                       + modifiedPOHeader.EntryPerson.ToString().Replace("`", "-"));
        }

        // Order Date
        if (modifiedPOHeader.OrderDate != originalPOHeader.OrderDate)
        {
            changes.Add("POHeader.OrderDate`"
                       + originalPOHeader.OrderDate.ToString()
                       + "`"
                       + modifiedPOHeader.OrderDate.ToString().Replace("`", "-"));
        }

        // FOB
        if (modifiedPOHeader.FOB != originalPOHeader.FOB)
        {
            changes.Add("POHeader.FOB`"
                       + originalPOHeader.FOB.ToString()
                       + "`"
                       + modifiedPOHeader.FOB.ToString().Replace("`", "-"));
        }

        // Ship Via Code
        if (modifiedPOHeader.ShipViaCode != originalPOHeader.ShipViaCode)
        {
            changes.Add("POHeader.ShipViaCode`"
                       + originalPOHeader.ShipViaCode.ToString()
                       + "`"
                       + modifiedPOHeader.ShipViaCode.ToString().Replace("`", "-"));
        }

        // Terms Code
        if (modifiedPOHeader.TermsCode != originalPOHeader.TermsCode)
        {
            changes.Add("POHeader.TermsCode`"
                       + originalPOHeader.TermsCode.ToString()
                       + "`"
                       + modifiedPOHeader.TermsCode.ToString().Replace("`", "-"));
        }

        // Ship Name
        if (modifiedPOHeader.ShipName != originalPOHeader.ShipName)
        {
            changes.Add("POHeader.ShipName`"
                       + originalPOHeader.ShipName.ToString()
                       + "`"
                       + modifiedPOHeader.ShipName.ToString().Replace("`", "-"));
        }

        // Ship Address 1
        if (modifiedPOHeader.ShipAddress1 != originalPOHeader.ShipAddress1)
        {
            changes.Add("POHeader.ShipAddress1`"
                       + originalPOHeader.ShipAddress1.ToString()
                       + "`"
                       + modifiedPOHeader.ShipAddress1.ToString().Replace("`", "-"));
        }

        // Ship Address 2
        if (modifiedPOHeader.ShipAddress2 != originalPOHeader.ShipAddress2)
        {
            changes.Add("POHeader.ShipAddress2`"
                       + originalPOHeader.ShipAddress2.ToString()
                       + "`"
                       + modifiedPOHeader.ShipAddress2.ToString().Replace("`", "-"));
        }

        // Ship Address 3
        if (modifiedPOHeader.ShipAddress3 != originalPOHeader.ShipAddress3)
        {
            changes.Add("POHeader.ShipAddress3`"
                       + originalPOHeader.ShipAddress3.ToString()
                       + "`"
                       + modifiedPOHeader.ShipAddress3.ToString().Replace("`", "-"));
        }

        // Ship City
        if (modifiedPOHeader.ShipCity != originalPOHeader.ShipCity)
        {
            changes.Add("POHeader.ShipCity`"
                       + originalPOHeader.ShipCity.ToString()
                       + "`"
                       + modifiedPOHeader.ShipCity.ToString().Replace("`", "-"));
        }

        // Ship State
        if (modifiedPOHeader.ShipState != originalPOHeader.ShipState)
        {
            changes.Add("POHeader.ShipState`"
                       + originalPOHeader.ShipState.ToString()
                       + "`"
                       + modifiedPOHeader.ShipState.ToString().Replace("`", "-"));
        }

        // Ship ZIP
        if (modifiedPOHeader.ShipZIP != originalPOHeader.ShipZIP)
        {
            changes.Add("POHeader.ShipZIP`"
                       + originalPOHeader.ShipZIP.ToString()
                       + "`"
                       + modifiedPOHeader.ShipZIP.ToString().Replace("`", "-"));
        }

        // Ship Country
        if (modifiedPOHeader.ShipCountry != originalPOHeader.ShipCountry)
        {
            changes.Add("POHeader.ShipCountry`"
                       + originalPOHeader.ShipCountry.ToString()
                       + "`"
                       + modifiedPOHeader.ShipCountry.ToString().Replace("`", "-"));
        }

        // Buyer ID
        if (modifiedPOHeader.BuyerID != originalPOHeader.BuyerID)
        {
            changes.Add("POHeader.BuyerID`"
                       + originalPOHeader.BuyerID.ToString()
                       + "`"
                       + modifiedPOHeader.BuyerID.ToString().Replace("`", "-"));
        }

        // Freight Prepaid
        if (modifiedPOHeader.FreightPP != originalPOHeader.FreightPP)
        {
            changes.Add("POHeader.FreightPP`"
                       + originalPOHeader.FreightPP.ToString()
                       + "`"
                       + modifiedPOHeader.FreightPP.ToString().Replace("`", "-"));
        }

        // Purchase Contact Number
        if (modifiedPOHeader.PrcConNum != originalPOHeader.PrcConNum)
        {
            changes.Add("POHeader.PrcConNum`"
                       + originalPOHeader.PrcConNum.ToString()
                       + "`"
                       + modifiedPOHeader.PrcConNum.ToString().Replace("`", "-"));
        }

        // Vendor Number
        if (modifiedPOHeader.VendorNum != originalPOHeader.VendorNum)
        {
            changes.Add("POHeader.VendorNum`"
                       + originalPOHeader.VendorNum.ToString()
                       + "`"
                       + modifiedPOHeader.VendorNum.ToString().Replace("`", "-"));
        }

        // Purchase Point
        if (modifiedPOHeader.PurPoint != originalPOHeader.PurPoint)
        {
            changes.Add("POHeader.PurPoint`"
                       + originalPOHeader.PurPoint.ToString()
                       + "`"
                       + modifiedPOHeader.PurPoint.ToString().Replace("`", "-"));
        }

        // Comment Text
        if (modifiedPOHeader.CommentText != originalPOHeader.CommentText)
        {
            changes.Add("POHeader.CommentText`"
                       + originalPOHeader.CommentText.ToString()
                       + "`"
                       + modifiedPOHeader.CommentText.ToString().Replace("`", "-"));
        }

        // Order Held
        if (modifiedPOHeader.OrderHeld != originalPOHeader.OrderHeld)
        {
            changes.Add("POHeader.OrderHeld`"
                       + originalPOHeader.OrderHeld.ToString()
                       + "`"
                       + modifiedPOHeader.OrderHeld.ToString().Replace("`", "-"));
        }

        // Ship To Contact Name
        if (modifiedPOHeader.ShipToConName != originalPOHeader.ShipToConName)
        {
            changes.Add("POHeader.ShipToConName`"
                       + originalPOHeader.ShipToConName.ToString()
                       + "`"
                       + modifiedPOHeader.ShipToConName.ToString().Replace("`", "-"));
        }

        // Ready To Print
        if (modifiedPOHeader.ReadyToPrint != originalPOHeader.ReadyToPrint)
        {
            changes.Add("POHeader.ReadyToPrint`"
                       + originalPOHeader.ReadyToPrint.ToString()
                       + "`"
                       + modifiedPOHeader.ReadyToPrint.ToString().Replace("`", "-"));
        }

        // Print As
        if (modifiedPOHeader.PrintAs != originalPOHeader.PrintAs)
        {
            changes.Add("POHeader.PrintAs`"
                       + originalPOHeader.PrintAs.ToString()
                       + "`"
                       + modifiedPOHeader.PrintAs.ToString().Replace("`", "-"));
        }

        // Currency Code
        if (modifiedPOHeader.CurrencyCode != originalPOHeader.CurrencyCode)
        {
            changes.Add("POHeader.CurrencyCode`"
                       + originalPOHeader.CurrencyCode.ToString()
                       + "`"
                       + modifiedPOHeader.CurrencyCode.ToString().Replace("`", "-"));
        }

        // Exchange Rate
        if (modifiedPOHeader.ExchangeRate != originalPOHeader.ExchangeRate)
        {
            changes.Add("POHeader.ExchangeRate`"
                       + originalPOHeader.ExchangeRate.ToString()
                       + "`"
                       + modifiedPOHeader.ExchangeRate.ToString().Replace("`", "-"));
        }

        // Lock Rate
        if (modifiedPOHeader.LockRate != originalPOHeader.LockRate)
        {
            changes.Add("POHeader.LockRate`"
                       + originalPOHeader.LockRate.ToString()
                       + "`"
                       + modifiedPOHeader.LockRate.ToString().Replace("`", "-"));
        }

        // Ship Country Number
        if (modifiedPOHeader.ShipCountryNum != originalPOHeader.ShipCountryNum)
        {
            changes.Add("POHeader.ShipCountryNum`"
                       + originalPOHeader.ShipCountryNum.ToString()
                       + "`"
                       + modifiedPOHeader.ShipCountryNum.ToString().Replace("`", "-"));
        }

        // Log Changes
        if (modifiedPOHeader.LogChanges != originalPOHeader.LogChanges)
        {
            changes.Add("POHeader.LogChanges`"
                       + originalPOHeader.LogChanges.ToString()
                       + "`"
                       + modifiedPOHeader.LogChanges.ToString().Replace("`", "-"));
        }

        // Approved Date
        if (modifiedPOHeader.ApprovedDate != originalPOHeader.ApprovedDate)
        {
            changes.Add("POHeader.ApprovedDate`"
                       + originalPOHeader.ApprovedDate.ToString()
                       + "`"
                       + modifiedPOHeader.ApprovedDate.ToString().Replace("`", "-"));
        }

        // Approved By
        if (modifiedPOHeader.ApprovedBy != originalPOHeader.ApprovedBy)
        {
            changes.Add("POHeader.ApprovedBy`"
                       + originalPOHeader.ApprovedBy.ToString()
                       + "`"
                       + modifiedPOHeader.ApprovedBy.ToString().Replace("`", "-"));
        }

        // Approve
        if (modifiedPOHeader.Approve != originalPOHeader.Approve)
        {
            changes.Add("POHeader.Approve`"
                       + originalPOHeader.Approve.ToString()
                       + "`"
                       + modifiedPOHeader.Approve.ToString().Replace("`", "-"));
        }

        // Approval Status
        if (modifiedPOHeader.ApprovalStatus != originalPOHeader.ApprovalStatus)
        {
            changes.Add("POHeader.ApprovalStatus`"
                       + originalPOHeader.ApprovalStatus.ToString()
                       + "`"
                       + modifiedPOHeader.ApprovalStatus.ToString().Replace("`", "-"));
        }

        // Approved Amount
        if (modifiedPOHeader.ApprovedAmount != originalPOHeader.ApprovedAmount)
        {
            changes.Add("POHeader.ApprovedAmount`"
                       + originalPOHeader.ApprovedAmount.ToString()
                       + "`"
                       + modifiedPOHeader.ApprovedAmount.ToString().Replace("`", "-"));
        }

        // Post To Web
        if (modifiedPOHeader.PostToWeb != originalPOHeader.PostToWeb)
        {
            changes.Add("POHeader.PostToWeb`"
                       + originalPOHeader.PostToWeb.ToString()
                       + "`"
                       + modifiedPOHeader.PostToWeb.ToString().Replace("`", "-"));
        }

        // Post Date
        if (modifiedPOHeader.PostDate != originalPOHeader.PostDate)
        {
            changes.Add("POHeader.PostDate`"
                       + originalPOHeader.PostDate.ToString()
                       + "`"
                       + modifiedPOHeader.PostDate.ToString().Replace("`", "-"));
        }

        // Vendor Reference Number
        if (modifiedPOHeader.VendorRefNum != originalPOHeader.VendorRefNum)
        {
            changes.Add("POHeader.VendorRefNum`"
                       + originalPOHeader.VendorRefNum.ToString()
                       + "`"
                       + modifiedPOHeader.VendorRefNum.ToString().Replace("`", "-"));
        }

        // Confirm Required
        if (modifiedPOHeader.ConfirmReq != originalPOHeader.ConfirmReq)
        {
            changes.Add("POHeader.ConfirmReq`"
                       + originalPOHeader.ConfirmReq.ToString()
                       + "`"
                       + modifiedPOHeader.ConfirmReq.ToString().Replace("`", "-"));
        }

        // Confirmed
        if (modifiedPOHeader.Confirmed != originalPOHeader.Confirmed)
        {
            changes.Add("POHeader.Confirmed`"
                       + originalPOHeader.Confirmed.ToString()
                       + "`"
                       + modifiedPOHeader.Confirmed.ToString().Replace("`", "-"));
        }

        // Confirm Via
        if (modifiedPOHeader.ConfirmVia != originalPOHeader.ConfirmVia)
        {
            changes.Add("POHeader.ConfirmVia`"
                       + originalPOHeader.ConfirmVia.ToString()
                       + "`"
                       + modifiedPOHeader.ConfirmVia.ToString().Replace("`", "-"));
        }

        // Order Number
        if (modifiedPOHeader.OrderNum != originalPOHeader.OrderNum)
        {
            changes.Add("POHeader.OrderNum`"
                       + originalPOHeader.OrderNum.ToString()
                       + "`"
                       + modifiedPOHeader.OrderNum.ToString().Replace("`", "-"));
        }

        // Legal Number
        if (modifiedPOHeader.LegalNumber != originalPOHeader.LegalNumber)
        {
            changes.Add("POHeader.LegalNumber`"
                       + originalPOHeader.LegalNumber.ToString()
                       + "`"
                       + modifiedPOHeader.LegalNumber.ToString().Replace("`", "-"));
        }

        // Linked
        if (modifiedPOHeader.Linked != originalPOHeader.Linked)
        {
            changes.Add("POHeader.Linked`"
                       + originalPOHeader.Linked.ToString()
                       + "`"
                       + modifiedPOHeader.Linked.ToString().Replace("`", "-"));
        }

        // External Company
        if (modifiedPOHeader.ExtCompany != originalPOHeader.ExtCompany)
        {
            changes.Add("POHeader.ExtCompany`"
                       + originalPOHeader.ExtCompany.ToString()
                       + "`"
                       + modifiedPOHeader.ExtCompany.ToString().Replace("`", "-"));
        }



        // Global Company
        if (modifiedPOHeader.GlbCompany != originalPOHeader.GlbCompany)
        {
            changes.Add("POHeader.GlbCompany`"
                       + originalPOHeader.GlbCompany.ToString()
                       + "`"
                       + modifiedPOHeader.GlbCompany.ToString().Replace("`", "-"));
        }

        // Contract Order
        if (modifiedPOHeader.ContractOrder != originalPOHeader.ContractOrder)
        {
            changes.Add("POHeader.ContractOrder`"
                       + originalPOHeader.ContractOrder.ToString()
                       + "`"
                       + modifiedPOHeader.ContractOrder.ToString().Replace("`", "-"));
        }





        // Due Date
        if (modifiedPOHeader.DueDate != originalPOHeader.DueDate)
        {
            changes.Add("POHeader.DueDate`"
                       + originalPOHeader.DueDate.ToString()
                       + "`"
                       + modifiedPOHeader.DueDate.ToString().Replace("`", "-"));
        }

        // Promise Date
        if (modifiedPOHeader.PromiseDate != originalPOHeader.PromiseDate)
        {
            changes.Add("POHeader.PromiseDate`"
                       + originalPOHeader.PromiseDate.ToString()
                       + "`"
                       + modifiedPOHeader.PromiseDate.ToString().Replace("`", "-"));
        }

        // Changed By
        if (modifiedPOHeader.ChangedBy != originalPOHeader.ChangedBy)
        {
            changes.Add("POHeader.ChangedBy`"
                       + originalPOHeader.ChangedBy.ToString()
                       + "`"
                       + modifiedPOHeader.ChangedBy.ToString().Replace("`", "-"));
        }

        // Change Date
        if (modifiedPOHeader.ChangeDate != originalPOHeader.ChangeDate)
        {
            changes.Add("POHeader.ChangeDate`"
                       + originalPOHeader.ChangeDate.ToString()
                       + "`"
                       + modifiedPOHeader.ChangeDate.ToString().Replace("`", "-"));
        }

        // PO Tax Ready To Process
        if (modifiedPOHeader.POTaxReadyToProcess != originalPOHeader.POTaxReadyToProcess)
        {
            changes.Add("POHeader.POTaxReadyToProcess`"
                       + originalPOHeader.POTaxReadyToProcess.ToString()
                       + "`"
                       + modifiedPOHeader.POTaxReadyToProcess.ToString().Replace("`", "-"));
        }

        // Tax Region Code
        if (modifiedPOHeader.TaxRegionCode != originalPOHeader.TaxRegionCode)
        {
            changes.Add("POHeader.TaxRegionCode`"
                       + originalPOHeader.TaxRegionCode.ToString()
                       + "`"
                       + modifiedPOHeader.TaxRegionCode.ToString().Replace("`", "-"));
        }

        // Tax Point
        if (modifiedPOHeader.TaxPoint != originalPOHeader.TaxPoint)
        {
            changes.Add("POHeader.TaxPoint`"
                       + originalPOHeader.TaxPoint.ToString()
                       + "`"
                       + modifiedPOHeader.TaxPoint.ToString().Replace("`", "-"));
        }

        // Tax Rate Date
        if (modifiedPOHeader.TaxRateDate != originalPOHeader.TaxRateDate)
        {
            changes.Add("POHeader.TaxRateDate`"
                       + originalPOHeader.TaxRateDate.ToString()
                       + "`"
                       + modifiedPOHeader.TaxRateDate.ToString().Replace("`", "-"));
        }

        // Total Tax
        if (modifiedPOHeader.TotalTax != originalPOHeader.TotalTax)
        {
            changes.Add("POHeader.TotalTax`"
                       + originalPOHeader.TotalTax.ToString()
                       + "`"
                       + modifiedPOHeader.TotalTax.ToString().Replace("`", "-"));
        }

        // Document Total Tax
        if (modifiedPOHeader.DocTotalTax != originalPOHeader.DocTotalTax)
        {
            changes.Add("POHeader.DocTotalTax`"
                       + originalPOHeader.DocTotalTax.ToString()
                       + "`"
                       + modifiedPOHeader.DocTotalTax.ToString().Replace("`", "-"));
        }

        // Report 1 Total Tax
        if (modifiedPOHeader.Rpt1TotalTax != originalPOHeader.Rpt1TotalTax)
        {
            changes.Add("POHeader.Rpt1TotalTax`"
                       + originalPOHeader.Rpt1TotalTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt1TotalTax.ToString().Replace("`", "-"));
        }

        // Report 2 Total Tax
        if (modifiedPOHeader.Rpt2TotalTax != originalPOHeader.Rpt2TotalTax)
        {
            changes.Add("POHeader.Rpt2TotalTax`"
                       + originalPOHeader.Rpt2TotalTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt2TotalTax.ToString().Replace("`", "-"));
        }

        // Report 3 Total Tax
        if (modifiedPOHeader.Rpt3TotalTax != originalPOHeader.Rpt3TotalTax)
        {
            changes.Add("POHeader.Rpt3TotalTax`"
                       + originalPOHeader.Rpt3TotalTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt3TotalTax.ToString().Replace("`", "-"));
        }

        // Total Withholding Tax
        if (modifiedPOHeader.TotalWhTax != originalPOHeader.TotalWhTax)
        {
            changes.Add("POHeader.TotalWhTax`"
                       + originalPOHeader.TotalWhTax.ToString()
                       + "`"
                       + modifiedPOHeader.TotalWhTax.ToString().Replace("`", "-"));
        }

        // Document Total Withholding Tax
        if (modifiedPOHeader.DocTotalWhTax != originalPOHeader.DocTotalWhTax)
        {
            changes.Add("POHeader.DocTotalWhTax`"
                       + originalPOHeader.DocTotalWhTax.ToString()
                       + "`"
                       + modifiedPOHeader.DocTotalWhTax.ToString().Replace("`", "-"));
        }

        // Report 1 Total Withholding Tax
        if (modifiedPOHeader.Rpt1TotalWhTax != originalPOHeader.Rpt1TotalWhTax)
        {
            changes.Add("POHeader.Rpt1TotalWhTax`"
                       + originalPOHeader.Rpt1TotalWhTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt1TotalWhTax.ToString().Replace("`", "-"));
        }

        // Report 2 Total Withholding Tax
        if (modifiedPOHeader.Rpt2TotalWhTax != originalPOHeader.Rpt2TotalWhTax)
        {
            changes.Add("POHeader.Rpt2TotalWhTax`"
                       + originalPOHeader.Rpt2TotalWhTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt2TotalWhTax.ToString().Replace("`", "-"));
        }

        // Report 3 Total Withholding Tax
        if (modifiedPOHeader.Rpt3TotalWhTax != originalPOHeader.Rpt3TotalWhTax)
        {
            changes.Add("POHeader.Rpt3TotalWhTax`"
                       + originalPOHeader.Rpt3TotalWhTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt3TotalWhTax.ToString().Replace("`", "-"));
        }

        // Total SA Tax
        if (modifiedPOHeader.TotalSATax != originalPOHeader.TotalSATax)
        {
            changes.Add("POHeader.TotalSATax`"
                       + originalPOHeader.TotalSATax.ToString()
                       + "`"
                       + modifiedPOHeader.TotalSATax.ToString().Replace("`", "-"));
        }

        // Document Total SA Tax
        if (modifiedPOHeader.DocTotalSATax != originalPOHeader.DocTotalSATax)
        {
            changes.Add("POHeader.DocTotalSATax`"
                       + originalPOHeader.DocTotalSATax.ToString()
                       + "`"
                       + modifiedPOHeader.DocTotalSATax.ToString().Replace("`", "-"));
        }

        // Report 1 Total SA Tax
        if (modifiedPOHeader.Rpt1TotalSATax != originalPOHeader.Rpt1TotalSATax)
        {
            changes.Add("POHeader.Rpt1TotalSATax`"
                       + originalPOHeader.Rpt1TotalSATax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt1TotalSATax.ToString().Replace("`", "-"));
        }

        // Report 2 Total SA Tax
        if (modifiedPOHeader.Rpt2TotalSATax != originalPOHeader.Rpt2TotalSATax)
        {
            changes.Add("POHeader.Rpt2TotalSATax`"
                       + originalPOHeader.Rpt2TotalSATax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt2TotalSATax.ToString().Replace("`", "-"));
        }

        // Report 3 Total SA Tax
        if (modifiedPOHeader.Rpt3TotalSATax != originalPOHeader.Rpt3TotalSATax)
        {
            changes.Add("POHeader.Rpt3TotalSATax`"
                       + originalPOHeader.Rpt3TotalSATax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt3TotalSATax.ToString().Replace("`", "-"));
        }

        // In Price
        if (modifiedPOHeader.InPrice != originalPOHeader.InPrice)
        {
            changes.Add("POHeader.InPrice`"
                       + originalPOHeader.InPrice.ToString()
                       + "`"
                       + modifiedPOHeader.InPrice.ToString().Replace("`", "-"));
        }

        // Header Tax No Update
        if (modifiedPOHeader.HdrTaxNoUpdt != originalPOHeader.HdrTaxNoUpdt)
        {
            changes.Add("POHeader.HdrTaxNoUpdt`"
                       + originalPOHeader.HdrTaxNoUpdt.ToString()
                       + "`"
                       + modifiedPOHeader.HdrTaxNoUpdt.ToString().Replace("`", "-"));
        }

        // Tax Rate Group Code
        if (modifiedPOHeader.TaxRateGrpCode != originalPOHeader.TaxRateGrpCode)
        {
            changes.Add("POHeader.TaxRateGrpCode`"
                       + originalPOHeader.TaxRateGrpCode.ToString()
                       + "`"
                       + modifiedPOHeader.TaxRateGrpCode.ToString().Replace("`", "-"));
        }

        // Total Deductible Tax
        if (modifiedPOHeader.TotalDedTax != originalPOHeader.TotalDedTax)
        {
            changes.Add("POHeader.TotalDedTax`"
                       + originalPOHeader.TotalDedTax.ToString()
                       + "`"
                       + modifiedPOHeader.TotalDedTax.ToString().Replace("`", "-"));
        }

        // Document Total Deductible Tax
        if (modifiedPOHeader.DocTotalDedTax != originalPOHeader.DocTotalDedTax)
        {
            changes.Add("POHeader.DocTotalDedTax`"
                       + originalPOHeader.DocTotalDedTax.ToString()
                       + "`"
                       + modifiedPOHeader.DocTotalDedTax.ToString().Replace("`", "-"));
        }

        // Report 1 Total Deductible Tax
        if (modifiedPOHeader.Rpt1TotalDedTax != originalPOHeader.Rpt1TotalDedTax)
        {
            changes.Add("POHeader.Rpt1TotalDedTax`"
                       + originalPOHeader.Rpt1TotalDedTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt1TotalDedTax.ToString().Replace("`", "-"));
        }

        // Report 2 Total Deductible Tax
        if (modifiedPOHeader.Rpt2TotalDedTax != originalPOHeader.Rpt2TotalDedTax)
        {
            changes.Add("POHeader.Rpt2TotalDedTax`"
                       + originalPOHeader.Rpt2TotalDedTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt2TotalDedTax.ToString().Replace("`", "-"));
        }

        // Report 3 Total Deductible Tax
        if (modifiedPOHeader.Rpt3TotalDedTax != originalPOHeader.Rpt3TotalDedTax)
        {
            changes.Add("POHeader.Rpt3TotalDedTax`"
                       + originalPOHeader.Rpt3TotalDedTax.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt3TotalDedTax.ToString().Replace("`", "-"));
        }

        // Total Charges
        if (modifiedPOHeader.TotalCharges != originalPOHeader.TotalCharges)
        {
            changes.Add("POHeader.TotalCharges`"
                       + originalPOHeader.TotalCharges.ToString()
                       + "`"
                       + modifiedPOHeader.TotalCharges.ToString().Replace("`", "-"));
        }

        // Total Miscellaneous Charges
        if (modifiedPOHeader.TotalMiscCharges != originalPOHeader.TotalMiscCharges)
        {
            changes.Add("POHeader.TotalMiscCharges`"
                       + originalPOHeader.TotalMiscCharges.ToString()
                       + "`"
                       + modifiedPOHeader.TotalMiscCharges.ToString().Replace("`", "-"));
        }

        // Total Order
        if (modifiedPOHeader.TotalOrder != originalPOHeader.TotalOrder)
        {
            changes.Add("POHeader.TotalOrder`"
                       + originalPOHeader.TotalOrder.ToString()
                       + "`"
                       + modifiedPOHeader.TotalOrder.ToString().Replace("`", "-"));
        }

        // Document Total Charges
        if (modifiedPOHeader.DocTotalCharges != originalPOHeader.DocTotalCharges)
        {
            changes.Add("POHeader.DocTotalCharges`"
                       + originalPOHeader.DocTotalCharges.ToString()
                       + "`"
                       + modifiedPOHeader.DocTotalCharges.ToString().Replace("`", "-"));
        }

        // Document Total Miscellaneous
        if (modifiedPOHeader.DocTotalMisc != originalPOHeader.DocTotalMisc)
        {
            changes.Add("POHeader.DocTotalMisc`"
                       + originalPOHeader.DocTotalMisc.ToString()
                       + "`"
                       + modifiedPOHeader.DocTotalMisc.ToString().Replace("`", "-"));
        }

        // Document Total Order
        if (modifiedPOHeader.DocTotalOrder != originalPOHeader.DocTotalOrder)
        {
            changes.Add("POHeader.DocTotalOrder`"
                       + originalPOHeader.DocTotalOrder.ToString()
                       + "`"
                       + modifiedPOHeader.DocTotalOrder.ToString().Replace("`", "-"));
        }

        // Report 1 Total Charges
        if (modifiedPOHeader.Rpt1TotalCharges != originalPOHeader.Rpt1TotalCharges)
        {
            changes.Add("POHeader.Rpt1TotalCharges`"
                       + originalPOHeader.Rpt1TotalCharges.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt1TotalCharges.ToString().Replace("`", "-"));
        }

        // Report 2 Total Charges
        if (modifiedPOHeader.Rpt2TotalCharges != originalPOHeader.Rpt2TotalCharges)
        {
            changes.Add("POHeader.Rpt2TotalCharges`"
                       + originalPOHeader.Rpt2TotalCharges.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt2TotalCharges.ToString().Replace("`", "-"));
        }

        // Report 3 Total Charges
        if (modifiedPOHeader.Rpt3TotalCharges != originalPOHeader.Rpt3TotalCharges)
        {
            changes.Add("POHeader.Rpt3TotalCharges`"
                       + originalPOHeader.Rpt3TotalCharges.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt3TotalCharges.ToString().Replace("`", "-"));
        }

        // Report 1 Total Miscellaneous Charges
        if (modifiedPOHeader.Rpt1TotalMiscCharges != originalPOHeader.Rpt1TotalMiscCharges)
        {
            changes.Add("POHeader.Rpt1TotalMiscCharges`"
                       + originalPOHeader.Rpt1TotalMiscCharges.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt1TotalMiscCharges.ToString().Replace("`", "-"));
        }

        // Report 2 Total Miscellaneous Charges
        if (modifiedPOHeader.Rpt2TotalMiscCharges != originalPOHeader.Rpt2TotalMiscCharges)
        {
            changes.Add("POHeader.Rpt2TotalMiscCharges`"
                       + originalPOHeader.Rpt2TotalMiscCharges.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt2TotalMiscCharges.ToString().Replace("`", "-"));
        }

        // Report 3 Total Miscellaneous Charges
        if (modifiedPOHeader.Rpt3TotalMiscCharges != originalPOHeader.Rpt3TotalMiscCharges)
        {
            changes.Add("POHeader.Rpt3TotalMiscCharges`"
                       + originalPOHeader.Rpt3TotalMiscCharges.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt3TotalMiscCharges.ToString().Replace("`", "-"));
        }

        // Report 1 Total Order
        if (modifiedPOHeader.Rpt1TotalOrder != originalPOHeader.Rpt1TotalOrder)
        {
            changes.Add("POHeader.Rpt1TotalOrder`"
                       + originalPOHeader.Rpt1TotalOrder.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt1TotalOrder.ToString().Replace("`", "-"));
        }

        // Report 2 Total Order
        if (modifiedPOHeader.Rpt2TotalOrder != originalPOHeader.Rpt2TotalOrder)
        {
            changes.Add("POHeader.Rpt2TotalOrder`"
                       + originalPOHeader.Rpt2TotalOrder.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt2TotalOrder.ToString().Replace("`", "-"));
        }

        // Report 3 Total Order
        if (modifiedPOHeader.Rpt3TotalOrder != originalPOHeader.Rpt3TotalOrder)
        {
            changes.Add("POHeader.Rpt3TotalOrder`"
                       + originalPOHeader.Rpt3TotalOrder.ToString()
                       + "`"
                       + modifiedPOHeader.Rpt3TotalOrder.ToString().Replace("`", "-"));
        }

        // AP Tax Round Option
        if (modifiedPOHeader.APTaxRoundOption != originalPOHeader.APTaxRoundOption)
        {
            changes.Add("POHeader.APTaxRoundOption`"
                       + originalPOHeader.APTaxRoundOption.ToString()
                       + "`"
                       + modifiedPOHeader.APTaxRoundOption.ToString().Replace("`", "-"));
        }

        // CN Bonded
        if (modifiedPOHeader.CNBonded != originalPOHeader.CNBonded)
        {
            changes.Add("POHeader.CNBonded`"
                       + originalPOHeader.CNBonded.ToString()
                       + "`"
                       + modifiedPOHeader.CNBonded.ToString().Replace("`", "-"));
        }



    }
}

// =================================================================
// Process PODetail Changes
// =================================================================

// Check if PODetail data exists
if (ds.PODetail != null && ds.PODetail.Count > 0)
{
    // Process each PODetail record
    foreach (var modifiedDtl in ds.PODetail)
    {
        int poLine = modifiedDtl.POLine;

        // Check if the detail row has been deleted
        if (modifiedDtl.RowMod == "D")
        {
            changes.Add($"PODetail deleted: Line {poLine}");
            callFunc = true;
            continue;
        }

        // Get the original detail record from database
        var originalDtl = (from dbPODetail in Db.PODetail
                          where dbPODetail.Company == companyID
                             && dbPODetail.POLine == poLine
                          select dbPODetail).FirstOrDefault();

        // Handle new detail creation
        if (originalDtl == null)
        {
            changes.Add($"New PODetail created: Line {poLine}");
            callFunc = true;
            continue;
        }

        // =================================================================
        // PODetail Field Comparisons
        // =================================================================

        // Company
        if (modifiedDtl.Company != originalDtl.Company)
        {
            changes.Add($"PODetail.Company[Line {poLine}]`"
                       + originalDtl.Company.ToString()
                       + "`"
                       + modifiedDtl.Company.ToString().Replace("`", "-"));
        }



        // PO Line
        if (modifiedDtl.POLine != originalDtl.POLine)
        {
            changes.Add($"PODetail.POLine[Line {poLine}]`"
                       + originalDtl.POLine.ToString()
                       + "`"
                       + modifiedDtl.POLine.ToString().Replace("`", "-"));
        }



        // Line Description
        if (modifiedDtl.LineDesc != originalDtl.LineDesc)
        {
            changes.Add($"PODetail.LineDesc[Line {poLine}]`"
                       + originalDtl.LineDesc.ToString()
                       + "`"
                       + modifiedDtl.LineDesc.ToString().Replace("`", "-"));
        }

        // IUM
        if (modifiedDtl.IUM != originalDtl.IUM)
        {
            changes.Add($"PODetail.IUM[Line {poLine}]`"
                       + originalDtl.IUM.ToString()
                       + "`"
                       + modifiedDtl.IUM.ToString().Replace("`", "-"));
        }

        // Unit Cost
        if (modifiedDtl.UnitCost != originalDtl.UnitCost)
        {
            changes.Add($"PODetail.UnitCost[Line {poLine}]`"
                       + originalDtl.UnitCost.ToString()
                       + "`"
                       + modifiedDtl.UnitCost.ToString().Replace("`", "-"));
        }

        // Document Unit Cost
        if (modifiedDtl.DocUnitCost != originalDtl.DocUnitCost)
        {
            changes.Add($"PODetail.DocUnitCost[Line {poLine}]`"
                       + originalDtl.DocUnitCost.ToString()
                       + "`"
                       + modifiedDtl.DocUnitCost.ToString().Replace("`", "-"));
        }

        // Order Quantity
        if (modifiedDtl.OrderQty != originalDtl.OrderQty)
        {
            changes.Add($"PODetail.OrderQty[Line {poLine}]`"
                       + originalDtl.OrderQty.ToString()
                       + "`"
                       + modifiedDtl.OrderQty.ToString().Replace("`", "-"));
        }

        // XOrder Quantity
        if (modifiedDtl.XOrderQty != originalDtl.XOrderQty)
        {
            changes.Add($"PODetail.XOrderQty[Line {poLine}]`"
                       + originalDtl.XOrderQty.ToString()
                       + "`"
                       + modifiedDtl.XOrderQty.ToString().Replace("`", "-"));
        }



        // Part Number
        if (modifiedDtl.PartNum != originalDtl.PartNum)
        {
            changes.Add($"PODetail.PartNum[Line {poLine}]`"
                       + originalDtl.PartNum.ToString()
                       + "`"
                       + modifiedDtl.PartNum.ToString().Replace("`", "-"));
        }

        // Vendor Number
        if (modifiedDtl.VendorNum != originalDtl.VendorNum)
        {
            changes.Add($"PODetail.VendorNum[Line {poLine}]`"
                       + originalDtl.VendorNum.ToString()
                       + "`"
                       + modifiedDtl.VendorNum.ToString().Replace("`", "-"));
        }

        // Comment Text
        if (modifiedDtl.CommentText != originalDtl.CommentText)
        {
            changes.Add($"PODetail.CommentText[Line {poLine}]`"
                       + originalDtl.CommentText.ToString()
                       + "`"
                       + modifiedDtl.CommentText.ToString().Replace("`", "-"));
        }

        // Class ID
        if (modifiedDtl.ClassID != originalDtl.ClassID)
        {
            changes.Add($"PODetail.ClassID[Line {poLine}]`"
                       + originalDtl.ClassID.ToString()
                       + "`"
                       + modifiedDtl.ClassID.ToString().Replace("`", "-"));
        }

        // Revision Number
        if (modifiedDtl.RevisionNum != originalDtl.RevisionNum)
        {
            changes.Add($"PODetail.RevisionNum[Line {poLine}]`"
                       + originalDtl.RevisionNum.ToString()
                       + "`"
                       + modifiedDtl.RevisionNum.ToString().Replace("`", "-"));
        }

        // Confirmed
        if (modifiedDtl.Confirmed != originalDtl.Confirmed)
        {
            changes.Add($"PODetail.Confirmed[Line {poLine}]`"
                       + originalDtl.Confirmed.ToString()
                       + "`"
                       + modifiedDtl.Confirmed.ToString().Replace("`", "-"));
        }

        // Taxable
        if (modifiedDtl.Taxable != originalDtl.Taxable)
        {
            changes.Add($"PODetail.Taxable[Line {poLine}]`"
                       + originalDtl.Taxable.ToString()
                       + "`"
                       + modifiedDtl.Taxable.ToString().Replace("`", "-"));
        }

        // Purchase Unit of Measure
        if (modifiedDtl.PUM != originalDtl.PUM)
        {
            changes.Add($"PODetail.PUM[Line {poLine}]`"
                       + originalDtl.PUM.ToString()
                       + "`"
                       + modifiedDtl.PUM.ToString().Replace("`", "-"));
        }

        // Open Line
        if (modifiedDtl.OpenLine != originalDtl.OpenLine)
        {
            changes.Add($"PODetail.OpenLine[Line {poLine}]`"
                       + originalDtl.OpenLine.ToString()
                       + "`"
                       + modifiedDtl.OpenLine.ToString().Replace("`", "-"));
        }

        // Void Line
        if (modifiedDtl.VoidLine != originalDtl.VoidLine)
        {
            changes.Add($"PODetail.VoidLine[Line {poLine}]`"
                       + originalDtl.VoidLine.ToString()
                       + "`"
                       + modifiedDtl.VoidLine.ToString().Replace("`", "-"));
        }

        // Due Date
        if (modifiedDtl.DueDate != originalDtl.DueDate)
        {
            changes.Add($"PODetail.DueDate[Line {poLine}]`"
                       + originalDtl.DueDate.ToString()
                       + "`"
                       + modifiedDtl.DueDate.ToString().Replace("`", "-"));
        }



        // Linked
        if (modifiedDtl.Linked != originalDtl.Linked)
        {
            changes.Add($"PODetail.Linked[Line {poLine}]`"
                       + originalDtl.Linked.ToString()
                       + "`"
                       + modifiedDtl.Linked.ToString().Replace("`", "-"));
        }

        // External Company
        if (modifiedDtl.ExtCompany != originalDtl.ExtCompany)
        {
            changes.Add($"PODetail.ExtCompany[Line {poLine}]`"
                       + originalDtl.ExtCompany.ToString()
                       + "`"
                       + modifiedDtl.ExtCompany.ToString().Replace("`", "-"));
        }

        // Global Company
        if (modifiedDtl.GlbCompany != originalDtl.GlbCompany)
        {
            changes.Add($"PODetail.GlbCompany[Line {poLine}]`"
                       + originalDtl.GlbCompany.ToString()
                       + "`"
                       + modifiedDtl.GlbCompany.ToString().Replace("`", "-"));
        }

        // Tax Category ID
        if (modifiedDtl.TaxCatID != originalDtl.TaxCatID)
        {
            changes.Add($"PODetail.TaxCatID[Line {poLine}]`"
                       + originalDtl.TaxCatID.ToString()
                       + "`"
                       + modifiedDtl.TaxCatID.ToString().Replace("`", "-"));
        }

    }
}

// =================================================================
// Final Processing
// =================================================================

// If changes were detected, call the function and log the changes
if (changes.Count > 0)
{
    callFunc = true;

    // Join all changes with the delimiter
    string allChanges = string.Join("`~`", changes);
    int totalLength = allChanges.Length;
    int maxChunkSize = 1000;

    // Assign chunks based on position - each exactly 1000 chars (or remaining)
    if (totalLength > 0)
    {
        changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
    }
    if (totalLength > 1000)
    {
        changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
    }
    if (totalLength > 2000)
    {
        changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
    }
    if (totalLength > 3000)
    {
        changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
    }
    if (totalLength > 4000)
    {
        changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
    }
    if (totalLength > 5000)
    {
        changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
    }
    if (totalLength > 6000)
    {
        changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
    }
    if (totalLength > 7000)
    {
        changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
    }
    if (totalLength > 8000)
    {
        changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
    }
    if (totalLength > 9000)
    {
        changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
    }

    // Set appropriate debug message
    if (totalLength > 10000)
    {
        test2 = $"Warning: {totalLength} characters of changes detected, only first 10000 logged";
    }
    else
    {
        test2 = $"Debug: {changes.Count} changes detected, {totalLength} characters logged";
    }
}
else
{
    test2 = "Debug: No changes detected";
}

// =================================================================
// End of BPM Script
// =================================================================
