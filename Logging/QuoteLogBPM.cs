changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;

// Debug: Check dataset before accessing
if (ds.QuoteHed == null || ds.QuoteHed.Count == 0)
{
    test1 = "Error: No quote data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.QuoteHed.Count.ToString()} quote records";

var modified = ds.QuoteHed[0];
quoteNum = modified.QuoteNum.ToString();
test2 = "Debug: Successfully accessed ds.QuoteHed[0]";

var original = (from dbCust in Db.QuoteHed
                where dbCust.Company == companyID
                && dbCust.QuoteNum.ToString() == quoteNum
                select dbCust).FirstOrDefault();

if (original == null)
{
    changesMade = $"New quote created: {modified.QuoteNum.ToString()}";
	callFunc = true;
}
else
{
    List<string> changes = new List<string>();

    // Check if there's a second record (modified version)
    // If not, use the first record (which should be the modified version)
    if (ds.QuoteHed.Count > 1)
    {
        try
        {
            modified = ds.QuoteHed[1];
            test2 = "Debug: Successfully accessed ds.QuoteHed[1]";
        }
        catch (System.Exception ex)
        {
            test2 = $"Error accessing ds.QuoteHed[1]: {ex.Message}";
            return;
        }
    }
    else
    {
        test2 = "Debug: Using ds.QuoteHed[0] as modified (only 1 record)";
    }
    if (modified.CustNum != original.CustNum)
        changes.Add("CustNum`"
                    + (original.CustNum.ToString())
                    + "`"
                    + (modified.CustNum.ToString()).Replace("`", "-"));

    if (modified.EntryDate != original.EntryDate)
        changes.Add("EntryDate`"
                    + (original.EntryDate.ToString())
                    + "`"
                    + (modified.EntryDate.ToString()).Replace("`", "-"));

    if (modified.PrcConNum != original.PrcConNum)
        changes.Add("PrcConNum`"
                    + (original.PrcConNum.ToString())
                    + "`"
                    + (modified.PrcConNum.ToString()).Replace("`", "-"));

    if (modified.QuoteComment != original.QuoteComment)
        changes.Add("QuoteComment`"
                    + (original.QuoteComment.ToString())
                    + "`"
                    + (modified.QuoteComment.ToString()).Replace("`", "-"));

    if (modified.DueDate != original.DueDate)
        changes.Add("DueDate`"
                    + (original.DueDate.ToString())
                    + "`"
                    + (modified.DueDate.ToString()).Replace("`", "-"));

    if (modified.Quoted != original.Quoted)
        changes.Add("Quoted`"
                    + (original.Quoted.ToString())
                    + "`"
                    + (modified.Quoted.ToString()).Replace("`", "-"));

    if (modified.DateQuoted != original.DateQuoted)
        changes.Add("DateQuoted`"
                    + (original.DateQuoted.ToString())
                    + "`"
                    + (modified.DateQuoted.ToString()).Replace("`", "-"));

    if (modified.ExpirationDate != original.ExpirationDate)
        changes.Add("ExpirationDate`"
                    + (original.ExpirationDate.ToString())
                    + "`"
                    + (modified.ExpirationDate.ToString()).Replace("`", "-"));

    if (modified.FollowUpDate != original.FollowUpDate)
        changes.Add("FollowUpDate`"
                    + (original.FollowUpDate.ToString())
                    + "`"
                    + (modified.FollowUpDate.ToString()).Replace("`", "-"));

    if (modified.Reference != original.Reference)
        changes.Add("Reference`"
                    + (original.Reference.ToString())
                    + "`"
                    + (modified.Reference.ToString()).Replace("`", "-"));

    if (modified.CheckOff1 != original.CheckOff1)
        changes.Add("CheckOff1`"
                    + (original.CheckOff1.ToString())
                    + "`"
                    + (modified.CheckOff1.ToString()).Replace("`", "-"));

    if (modified.CheckOff2 != original.CheckOff2)
        changes.Add("CheckOff2`"
                    + (original.CheckOff2.ToString())
                    + "`"
                    + (modified.CheckOff2.ToString()).Replace("`", "-"));

    if (modified.CheckOff3 != original.CheckOff3)
        changes.Add("CheckOff3`"
                    + (original.CheckOff3.ToString())
                    + "`"
                    + (modified.CheckOff3.ToString()).Replace("`", "-"));

    if (modified.CheckOff4 != original.CheckOff4)
        changes.Add("CheckOff4`"
                    + (original.CheckOff4.ToString())
                    + "`"
                    + (modified.CheckOff4.ToString()).Replace("`", "-"));

    if (modified.CheckOff5 != original.CheckOff5)
        changes.Add("CheckOff5`"
                    + (original.CheckOff5.ToString())
                    + "`"
                    + (modified.CheckOff5.ToString()).Replace("`", "-"));

    if (modified.Expired != original.Expired)
        changes.Add("Expired`"
                    + (original.Expired.ToString())
                    + "`"
                    + (modified.Expired.ToString()).Replace("`", "-"));

    if (modified.FlwAlrtSnt != original.FlwAlrtSnt)
        changes.Add("FlwAlrtSnt`"
                    + (original.FlwAlrtSnt.ToString())
                    + "`"
                    + (modified.FlwAlrtSnt.ToString()).Replace("`", "-"));

    if (modified.DueAlrtSnt != original.DueAlrtSnt)
        changes.Add("DueAlrtSnt`"
                    + (original.DueAlrtSnt.ToString())
                    + "`"
                    + (modified.DueAlrtSnt.ToString()).Replace("`", "-"));

    if (modified.CurrencyCode != original.CurrencyCode)
        changes.Add("CurrencyCode`"
                    + (original.CurrencyCode.ToString())
                    + "`"
                    + (modified.CurrencyCode.ToString()).Replace("`", "-"));

    if (modified.ExchangeRate != original.ExchangeRate)
        changes.Add("ExchangeRate`"
                    + (original.ExchangeRate.ToString())
                    + "`"
                    + (modified.ExchangeRate.ToString()).Replace("`", "-"));

    if (modified.LeadRating != original.LeadRating)
        changes.Add("LeadRating`"
                    + (original.LeadRating.ToString())
                    + "`"
                    + (modified.LeadRating.ToString()).Replace("`", "-"));

    if (modified.TerritoryID != original.TerritoryID)
        changes.Add("TerritoryID`"
                    + (original.TerritoryID.ToString())
                    + "`"
                    + (modified.TerritoryID.ToString()).Replace("`", "-"));

    if (modified.TaskSetID != original.TaskSetID)
        changes.Add("TaskSetID`"
                    + (original.TaskSetID.ToString())
                    + "`"
                    + (modified.TaskSetID.ToString()).Replace("`", "-"));

    if (modified.CurrentStage != original.CurrentStage)
        changes.Add("CurrentStage`"
                    + (original.CurrentStage.ToString())
                    + "`"
                    + (modified.CurrentStage.ToString()).Replace("`", "-"));

    if (modified.ParentQuoteNum != original.ParentQuoteNum)
        changes.Add("ParentQuoteNum`"
                    + (original.ParentQuoteNum.ToString())
                    + "`"
                    + (modified.ParentQuoteNum.ToString()).Replace("`", "-"));

    if (modified.ActiveTaskID != original.ActiveTaskID)
        changes.Add("ActiveTaskID`"
                    + (original.ActiveTaskID.ToString())
                    + "`"
                    + (modified.ActiveTaskID.ToString()).Replace("`", "-"));

    if (modified.LastTaskID != original.LastTaskID)
        changes.Add("LastTaskID`"
                    + (original.LastTaskID.ToString())
                    + "`"
                    + (modified.LastTaskID.ToString()).Replace("`", "-"));

    if (modified.ExpectedClose != original.ExpectedClose)
        changes.Add("ExpectedClose`"
                    + (original.ExpectedClose.ToString())
                    + "`"
                    + (modified.ExpectedClose.ToString()).Replace("`", "-"));

    if (modified.ReasonType != original.ReasonType)
        changes.Add("ReasonType`"
                    + (original.ReasonType.ToString())
                    + "`"
                    + (modified.ReasonType.ToString()).Replace("`", "-"));

    if (modified.ReasonCode != original.ReasonCode)
        changes.Add("ReasonCode`"
                    + (original.ReasonCode.ToString())
                    + "`"
                    + (modified.ReasonCode.ToString()).Replace("`", "-"));

    if (modified.ConfidencePct != original.ConfidencePct)
        changes.Add("ConfidencePct`"
                    + (original.ConfidencePct.ToString())
                    + "`"
                    + (modified.ConfidencePct.ToString()).Replace("`", "-"));

    if (modified.DiscountPercent != original.DiscountPercent)
        changes.Add("DiscountPercent`"
                    + (original.DiscountPercent.ToString())
                    + "`"
                    + (modified.DiscountPercent.ToString()).Replace("`", "-"));

    if (modified.ShipToNum != original.ShipToNum)
        changes.Add("ShipToNum`"
                    + (original.ShipToNum.ToString())
                    + "`"
                    + (modified.ShipToNum.ToString()).Replace("`", "-"));

    if (modified.ShpConNum != original.ShpConNum)
        changes.Add("ShpConNum`"
                    + (original.ShpConNum.ToString())
                    + "`"
                    + (modified.ShpConNum.ToString()).Replace("`", "-"));

    if (modified.QuoteClosed != original.QuoteClosed)
        changes.Add("QuoteClosed`"
                    + (original.QuoteClosed.ToString())
                    + "`"
                    + (modified.QuoteClosed.ToString()).Replace("`", "-"));

    if (modified.ClosedDate != original.ClosedDate)
        changes.Add("ClosedDate`"
                    + (original.ClosedDate.ToString())
                    + "`"
                    + (modified.ClosedDate.ToString()).Replace("`", "-"));

    if (modified.ShipViaCode != original.ShipViaCode)
        changes.Add("ShipViaCode`"
                    + (original.ShipViaCode.ToString())
                    + "`"
                    + (modified.ShipViaCode.ToString()).Replace("`", "-"));

    if (modified.MktgCampaignID != original.MktgCampaignID)
        changes.Add("MktgCampaignID`"
                    + (original.MktgCampaignID.ToString())
                    + "`"
                    + (modified.MktgCampaignID.ToString()).Replace("`", "-"));

    if (modified.MktgEvntSeq != original.MktgEvntSeq)
        changes.Add("MktgEvntSeq`"
                    + (original.MktgEvntSeq.ToString())
                    + "`"
                    + (modified.MktgEvntSeq.ToString()).Replace("`", "-"));

    if (modified.CallTypeCode != original.CallTypeCode)
        changes.Add("CallTypeCode`"
                    + (original.CallTypeCode.ToString())
                    + "`"
                    + (modified.CallTypeCode.ToString()).Replace("`", "-"));

    if (modified.PONum != original.PONum)
        changes.Add("PONum`"
                    + (original.PONum.ToString())
                    + "`"
                    + (modified.PONum.ToString()).Replace("`", "-"));

    if (modified.TermsCode != original.TermsCode)
        changes.Add("TermsCode`"
                    + (original.TermsCode.ToString())
                    + "`"
                    + (modified.TermsCode.ToString()).Replace("`", "-"));

    if (modified.Ordered != original.Ordered)
        changes.Add("Ordered`"
                    + (original.Ordered.ToString())
                    + "`"
                    + (modified.Ordered.ToString()).Replace("`", "-"));

    if (modified.ApplyOrderBasedDisc != original.ApplyOrderBasedDisc)
        changes.Add("ApplyOrderBasedDisc`"
                    + (original.ApplyOrderBasedDisc.ToString())
                    + "`"
                    + (modified.ApplyOrderBasedDisc.ToString()).Replace("`", "-"));

    if (modified.AutoOrderBasedDisc != original.AutoOrderBasedDisc)
        changes.Add("AutoOrderBasedDisc`"
                    + (original.AutoOrderBasedDisc.ToString())
                    + "`"
                    + (modified.AutoOrderBasedDisc.ToString()).Replace("`", "-"));

    if (modified.HDCaseNum != original.HDCaseNum)
        changes.Add("HDCaseNum`"
                    + (original.HDCaseNum.ToString())
                    + "`"
                    + (modified.HDCaseNum.ToString()).Replace("`", "-"));

    if (modified.BTCustNum != original.BTCustNum)
        changes.Add("BTCustNum`"
                    + (original.BTCustNum.ToString())
                    + "`"
                    + (modified.BTCustNum.ToString()).Replace("`", "-"));

    if (modified.LockRate != original.LockRate)
        changes.Add("LockRate`"
                    + (original.LockRate.ToString())
                    + "`"
                    + (modified.LockRate.ToString()).Replace("`", "-"));

    if (modified.ChangedBy != original.ChangedBy)
        changes.Add("ChangedBy`"
                    + (original.ChangedBy.ToString())
                    + "`"
                    + (modified.ChangedBy.ToString()).Replace("`", "-"));

    if (modified.ChangeDate != original.ChangeDate)
        changes.Add("ChangeDate`"
                    + (original.ChangeDate.ToString())
                    + "`"
                    + (modified.ChangeDate.ToString()).Replace("`", "-"));

    if (modified.ChangeTime != original.ChangeTime)
        changes.Add("ChangeTime`"
                    + (original.ChangeTime.ToString())
                    + "`"
                    + (modified.ChangeTime.ToString()).Replace("`", "-"));
    if (modified.ReadyToCalc != original.ReadyToCalc)
        changes.Add("ReadyToCalc`"
                    + (original.ReadyToCalc.ToString())
                    + "`"
                    + (modified.ReadyToCalc.ToString()).Replace("`", "-"));

    if (modified.ExportRequested != original.ExportRequested)
        changes.Add("ExportRequested`"
                    + (original.ExportRequested.ToString())
                    + "`"
                    + (modified.ExportRequested.ToString()).Replace("`", "-"));

    if (modified.Rounding != original.Rounding)
        changes.Add("Rounding`"
                    + (original.Rounding.ToString())
                    + "`"
                    + (modified.Rounding.ToString()).Replace("`", "-"));

    if (modified.DocRounding != original.DocRounding)
        changes.Add("DocRounding`"
                    + (original.DocRounding.ToString())
                    + "`"
                    + (modified.DocRounding.ToString()).Replace("`", "-"));

    if (modified.Rpt1Rounding != original.Rpt1Rounding)
        changes.Add("Rpt1Rounding`"
                    + (original.Rpt1Rounding.ToString())
                    + "`"
                    + (modified.Rpt1Rounding.ToString()).Replace("`", "-"));

    if (modified.Rpt2Rounding != original.Rpt2Rounding)
        changes.Add("Rpt2Rounding`"
                    + (original.Rpt2Rounding.ToString())
                    + "`"
                    + (modified.Rpt2Rounding.ToString()).Replace("`", "-"));

    if (modified.Rpt3Rounding != original.Rpt3Rounding)
        changes.Add("Rpt3Rounding`"
                    + (original.Rpt3Rounding.ToString())
                    + "`"
                    + (modified.Rpt3Rounding.ToString()).Replace("`", "-"));

    if (modified.RateGrpCode != original.RateGrpCode)
        changes.Add("RateGrpCode`"
                    + (original.RateGrpCode.ToString())
                    + "`"
                    + (modified.RateGrpCode.ToString()).Replace("`", "-"));

    if (modified.QuoteAmt != original.QuoteAmt)
        changes.Add("QuoteAmt`"
                    + (original.QuoteAmt.ToString())
                    + "`"
                    + (modified.QuoteAmt.ToString()).Replace("`", "-"));

    if (modified.DocQuoteAmt != original.DocQuoteAmt)
        changes.Add("DocQuoteAmt`"
                    + (original.DocQuoteAmt.ToString())
                    + "`"
                    + (modified.DocQuoteAmt.ToString()).Replace("`", "-"));

    if (modified.Rpt1QuoteAmt != original.Rpt1QuoteAmt)
        changes.Add("Rpt1QuoteAmt`"
                    + (original.Rpt1QuoteAmt.ToString())
                    + "`"
                    + (modified.Rpt1QuoteAmt.ToString()).Replace("`", "-"));

    if (modified.Rpt2QuoteAmt != original.Rpt2QuoteAmt)
        changes.Add("Rpt2QuoteAmt`"
                    + (original.Rpt2QuoteAmt.ToString())
                    + "`"
                    + (modified.Rpt2QuoteAmt.ToString()).Replace("`", "-"));

    if (modified.Rpt3QuoteAmt != original.Rpt3QuoteAmt)
        changes.Add("Rpt3QuoteAmt`"
                    + (original.Rpt3QuoteAmt.ToString())
                    + "`"
                    + (modified.Rpt3QuoteAmt.ToString()).Replace("`", "-"));

    if (modified.UseOTS != original.UseOTS)
        changes.Add("UseOTS`"
                    + (original.UseOTS.ToString())
                    + "`"
                    + (modified.UseOTS.ToString()).Replace("`", "-"));

    if (modified.OTSName != original.OTSName)
        changes.Add("OTSName`"
                    + (original.OTSName.ToString())
                    + "`"
                    + (modified.OTSName.ToString()).Replace("`", "-"));

    if (modified.OTSAddress1 != original.OTSAddress1)
        changes.Add("OTSAddress1`"
                    + (original.OTSAddress1.ToString())
                    + "`"
                    + (modified.OTSAddress1.ToString()).Replace("`", "-"));

    if (modified.OTSAddress2 != original.OTSAddress2)
        changes.Add("OTSAddress2`"
                    + (original.OTSAddress2.ToString())
                    + "`"
                    + (modified.OTSAddress2.ToString()).Replace("`", "-"));

    if (modified.OTSAddress3 != original.OTSAddress3)
        changes.Add("OTSAddress3`"
                    + (original.OTSAddress3.ToString())
                    + "`"
                    + (modified.OTSAddress3.ToString()).Replace("`", "-"));

    if (modified.OTSCity != original.OTSCity)
        changes.Add("OTSCity`"
                    + (original.OTSCity.ToString())
                    + "`"
                    + (modified.OTSCity.ToString()).Replace("`", "-"));

    if (modified.OTSState != original.OTSState)
        changes.Add("OTSState`"
                    + (original.OTSState.ToString())
                    + "`"
                    + (modified.OTSState.ToString()).Replace("`", "-"));

    if (modified.OTSZIP != original.OTSZIP)
        changes.Add("OTSZIP`"
                    + (original.OTSZIP.ToString())
                    + "`"
                    + (modified.OTSZIP.ToString()).Replace("`", "-"));

    if (modified.OTSResaleID != original.OTSResaleID)
        changes.Add("OTSResaleID`"
                    + (original.OTSResaleID.ToString())
                    + "`"
                    + (modified.OTSResaleID.ToString()).Replace("`", "-"));

    if (modified.OTSTaxRegionCode != original.OTSTaxRegionCode)
        changes.Add("OTSTaxRegionCode`"
                    + (original.OTSTaxRegionCode.ToString())
                    + "`"
                    + (modified.OTSTaxRegionCode.ToString()).Replace("`", "-"));

    if (modified.OTSContact != original.OTSContact)
        changes.Add("OTSContact`"
                    + (original.OTSContact.ToString())
                    + "`"
                    + (modified.OTSContact.ToString()).Replace("`", "-"));

    if (modified.OTSFaxNum != original.OTSFaxNum)
        changes.Add("OTSFaxNum`"
                    + (original.OTSFaxNum.ToString())
                    + "`"
                    + (modified.OTSFaxNum.ToString()).Replace("`", "-"));

    if (modified.OTSPhoneNum != original.OTSPhoneNum)
        changes.Add("OTSPhoneNum`"
                    + (original.OTSPhoneNum.ToString())
                    + "`"
                    + (modified.OTSPhoneNum.ToString()).Replace("`", "-"));

    if (modified.OTSCountryNum != original.OTSCountryNum)
        changes.Add("OTSCountryNum`"
                    + (original.OTSCountryNum.ToString())
                    + "`"
                    + (modified.OTSCountryNum.ToString()).Replace("`", "-"));

    if (modified.ShipToCustNum != original.ShipToCustNum)
        changes.Add("ShipToCustNum`"
                    + (original.ShipToCustNum.ToString())
                    + "`"
                    + (modified.ShipToCustNum.ToString()).Replace("`", "-"));

    if (modified.InPrice != original.InPrice)
        changes.Add("InPrice`"
                    + (original.InPrice.ToString())
                    + "`"
                    + (modified.InPrice.ToString()).Replace("`", "-"));

    if (modified.WorstCsPct != original.WorstCsPct)
        changes.Add("WorstCsPct`"
                    + (original.WorstCsPct.ToString())
                    + "`"
                    + (modified.WorstCsPct.ToString()).Replace("`", "-"));

    if (modified.BestCsPct != original.BestCsPct)
        changes.Add("BestCsPct`"
                    + (original.BestCsPct.ToString())
                    + "`"
                    + (modified.BestCsPct.ToString()).Replace("`", "-"));

    if (modified.DemandContractNum != original.DemandContractNum)
        changes.Add("DemandContractNum`"
                    + (original.DemandContractNum.ToString())
                    + "`"
                    + (modified.DemandContractNum.ToString()).Replace("`", "-"));

    if (modified.DemandHeadSeq != original.DemandHeadSeq)
        changes.Add("DemandHeadSeq`"
                    + (original.DemandHeadSeq.ToString())
                    + "`"
                    + (modified.DemandHeadSeq.ToString()).Replace("`", "-"));

    if (modified.EDIReady != original.EDIReady)
        changes.Add("EDIReady`"
                    + (original.EDIReady.ToString())
                    + "`"
                    + (modified.EDIReady.ToString()).Replace("`", "-"));

    if (modified.EDIQuote != original.EDIQuote)
        changes.Add("EDIQuote`"
                    + (original.EDIQuote.ToString())
                    + "`"
                    + (modified.EDIQuote.ToString()).Replace("`", "-"));

    if (modified.EDIAck != original.EDIAck)
        changes.Add("EDIAck`"
                    + (original.EDIAck.ToString())
                    + "`"
                    + (modified.EDIAck.ToString()).Replace("`", "-"));

    if (modified.OutboundQuoteDocCtr != original.OutboundQuoteDocCtr)
        changes.Add("OutboundQuoteDocCtr`"
                    + (original.OutboundQuoteDocCtr.ToString())
                    + "`"
                    + (modified.OutboundQuoteDocCtr.ToString()).Replace("`", "-"));

    if (modified.DemandProcessDate != original.DemandProcessDate)
        changes.Add("DemandProcessDate`"
                    + (original.DemandProcessDate.ToString())
                    + "`"
                    + (modified.DemandProcessDate.ToString()).Replace("`", "-"));

    if (modified.DemandProcessTime != original.DemandProcessTime)
        changes.Add("DemandProcessTime`"
                    + (original.DemandProcessTime.ToString())
                    + "`"
                    + (modified.DemandProcessTime.ToString()).Replace("`", "-"));

    if (modified.LastTCtrlNum != original.LastTCtrlNum)
        changes.Add("LastTCtrlNum`"
                    + (original.LastTCtrlNum.ToString())
                    + "`"
                    + (modified.LastTCtrlNum.ToString()).Replace("`", "-"));

    if (modified.LastBatchNum != original.LastBatchNum)
        changes.Add("LastBatchNum`"
                    + (original.LastBatchNum.ToString())
                    + "`"
                    + (modified.LastBatchNum.ToString()).Replace("`", "-"));

    if (modified.AutoPrintReady != original.AutoPrintReady)
        changes.Add("AutoPrintReady`"
                    + (original.AutoPrintReady.ToString())
                    + "`"
                    + (modified.AutoPrintReady.ToString()).Replace("`", "-"));

    if (modified.DocTotalSATax != original.DocTotalSATax)
        changes.Add("DocTotalSATax`"
                    + (original.DocTotalSATax.ToString())
                    + "`"
                    + (modified.DocTotalSATax.ToString()).Replace("`", "-"));

    if (modified.DocTotalTax != original.DocTotalTax)
        changes.Add("DocTotalTax`"
                    + (original.DocTotalTax.ToString())
                    + "`"
                    + (modified.DocTotalTax.ToString()).Replace("`", "-"));

    if (modified.DocTotalWHTax != original.DocTotalWHTax)
        changes.Add("DocTotalWHTax`"
                    + (original.DocTotalWHTax.ToString())
                    + "`"
                    + (modified.DocTotalWHTax.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalSATax != original.Rpt1TotalSATax)
        changes.Add("Rpt1TotalSATax`"
                    + (original.Rpt1TotalSATax.ToString())
                    + "`"
                    + (modified.Rpt1TotalSATax.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalTax != original.Rpt1TotalTax)
        changes.Add("Rpt1TotalTax`"
                    + (original.Rpt1TotalTax.ToString())
                    + "`"
                    + (modified.Rpt1TotalTax.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalWHTax != original.Rpt1TotalWHTax)
        changes.Add("Rpt1TotalWHTax`"
                    + (original.Rpt1TotalWHTax.ToString())
                    + "`"
                    + (modified.Rpt1TotalWHTax.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalSATax != original.Rpt2TotalSATax)
        changes.Add("Rpt2TotalSATax`"
                    + (original.Rpt2TotalSATax.ToString())
                    + "`"
                    + (modified.Rpt2TotalSATax.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalTax != original.Rpt2TotalTax)
        changes.Add("Rpt2TotalTax`"
                    + (original.Rpt2TotalTax.ToString())
                    + "`"
                    + (modified.Rpt2TotalTax.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalWHTax != original.Rpt2TotalWHTax)
        changes.Add("Rpt2TotalWHTax`"
                    + (original.Rpt2TotalWHTax.ToString())
                    + "`"
                    + (modified.Rpt2TotalWHTax.ToString()).Replace("`", "-"));
    if (modified.Rpt3TotalSATax != original.Rpt3TotalSATax)
        changes.Add("Rpt3TotalSATax`"
                    + (original.Rpt3TotalSATax.ToString())
                    + "`"
                    + (modified.Rpt3TotalSATax.ToString()).Replace("`", "-"));

    if (modified.DeclaredAmt != original.DeclaredAmt)
        changes.Add("DeclaredAmt`"
                    + (original.DeclaredAmt.ToString())
                    + "`"
                    + (modified.DeclaredAmt.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalTax != original.Rpt3TotalTax)
        changes.Add("Rpt3TotalTax`"
                    + (original.Rpt3TotalTax.ToString())
                    + "`"
                    + (modified.Rpt3TotalTax.ToString()).Replace("`", "-"));

    if (modified.DeclaredIns != original.DeclaredIns)
        changes.Add("DeclaredIns`"
                    + (original.DeclaredIns.ToString())
                    + "`"
                    + (modified.DeclaredIns.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalWHTax != original.Rpt3TotalWHTax)
        changes.Add("Rpt3TotalWHTax`"
                    + (original.Rpt3TotalWHTax.ToString())
                    + "`"
                    + (modified.Rpt3TotalWHTax.ToString()).Replace("`", "-"));

    if (modified.DeliveryConf != original.DeliveryConf)
        changes.Add("DeliveryConf`"
                    + (original.DeliveryConf.ToString())
                    + "`"
                    + (modified.DeliveryConf.ToString()).Replace("`", "-"));

    if (modified.TaxPoint != original.TaxPoint)
        changes.Add("TaxPoint`"
                    + (original.TaxPoint.ToString())
                    + "`"
                    + (modified.TaxPoint.ToString()).Replace("`", "-"));

    if (modified.DeliveryType != original.DeliveryType)
        changes.Add("DeliveryType`"
                    + (original.DeliveryType.ToString())
                    + "`"
                    + (modified.DeliveryType.ToString()).Replace("`", "-"));

    if (modified.TaxRateDate != original.TaxRateDate)
        changes.Add("TaxRateDate`"
                    + (original.TaxRateDate.ToString())
                    + "`"
                    + (modified.TaxRateDate.ToString()).Replace("`", "-"));

    if (modified.DocOnly != original.DocOnly)
        changes.Add("DocOnly`"
                    + (original.DocOnly.ToString())
                    + "`"
                    + (modified.DocOnly.ToString()).Replace("`", "-"));

    if (modified.TaxRegionCode != original.TaxRegionCode)
        changes.Add("TaxRegionCode`"
                    + (original.TaxRegionCode.ToString())
                    + "`"
                    + (modified.TaxRegionCode.ToString()).Replace("`", "-"));

    if (modified.DropShip != original.DropShip)
        changes.Add("DropShip`"
                    + (original.DropShip.ToString())
                    + "`"
                    + (modified.DropShip.ToString()).Replace("`", "-"));

    if (modified.TotalSATax != original.TotalSATax)
        changes.Add("TotalSATax`"
                    + (original.TotalSATax.ToString())
                    + "`"
                    + (modified.TotalSATax.ToString()).Replace("`", "-"));

    if (modified.ExtCompany != original.ExtCompany)
        changes.Add("ExtCompany`"
                    + (original.ExtCompany.ToString())
                    + "`"
                    + (modified.ExtCompany.ToString()).Replace("`", "-"));

    if (modified.TotalTax != original.TotalTax)
        changes.Add("TotalTax`"
                    + (original.TotalTax.ToString())
                    + "`"
                    + (modified.TotalTax.ToString()).Replace("`", "-"));

    if (modified.GroundType != original.GroundType)
        changes.Add("GroundType`"
                    + (original.GroundType.ToString())
                    + "`"
                    + (modified.GroundType.ToString()).Replace("`", "-"));

    if (modified.TotalWHTax != original.TotalWHTax)
        changes.Add("TotalWHTax`"
                    + (original.TotalWHTax.ToString())
                    + "`"
                    + (modified.TotalWHTax.ToString()).Replace("`", "-"));

    if (modified.Hazmat != original.Hazmat)
        changes.Add("Hazmat`"
                    + (original.Hazmat.ToString())
                    + "`"
                    + (modified.Hazmat.ToString()).Replace("`", "-"));

    if (modified.ICPONum != original.ICPONum)
        changes.Add("ICPONum`"
                    + (original.ICPONum.ToString())
                    + "`"
                    + (modified.ICPONum.ToString()).Replace("`", "-"));

    if (modified.Linked != original.Linked)
        changes.Add("Linked`"
                    + (original.Linked.ToString())
                    + "`"
                    + (modified.Linked.ToString()).Replace("`", "-"));

    if (modified.LockQty != original.LockQty)
        changes.Add("LockQty`"
                    + (original.LockQty.ToString())
                    + "`"
                    + (modified.LockQty.ToString()).Replace("`", "-"));

    if (modified.NeedByDate != original.NeedByDate)
        changes.Add("NeedByDate`"
                    + (original.NeedByDate.ToString())
                    + "`"
                    + (modified.NeedByDate.ToString()).Replace("`", "-"));

    if (modified.NotifyEMail != original.NotifyEMail)
        changes.Add("NotifyEMail`"
                    + (original.NotifyEMail.ToString())
                    + "`"
                    + (modified.NotifyEMail.ToString()).Replace("`", "-"));

    if (modified.NotifyFlag != original.NotifyFlag)
        changes.Add("NotifyFlag`"
                    + (original.NotifyFlag.ToString())
                    + "`"
                    + (modified.NotifyFlag.ToString()).Replace("`", "-"));

    if (modified.OTSCustSaved != original.OTSCustSaved)
        changes.Add("OTSCustSaved`"
                    + (original.OTSCustSaved.ToString())
                    + "`"
                    + (modified.OTSCustSaved.ToString()).Replace("`", "-"));

    if (modified.OTSSaveAs != original.OTSSaveAs)
        changes.Add("OTSSaveAs`"
                    + (original.OTSSaveAs.ToString())
                    + "`"
                    + (modified.OTSSaveAs.ToString()).Replace("`", "-"));

    if (modified.OTSSaveCustID != original.OTSSaveCustID)
        changes.Add("OTSSaveCustID`"
                    + (original.OTSSaveCustID.ToString())
                    + "`"
                    + (modified.OTSSaveCustID.ToString()).Replace("`", "-"));

    if (modified.OverrideCarrier != original.OverrideCarrier)
        changes.Add("OverrideCarrier`"
                    + (original.OverrideCarrier.ToString())
                    + "`"
                    + (modified.OverrideCarrier.ToString()).Replace("`", "-"));

    if (modified.OverrideService != original.OverrideService)
        changes.Add("OverrideService`"
                    + (original.OverrideService.ToString())
                    + "`"
                    + (modified.OverrideService.ToString()).Replace("`", "-"));

    if (modified.RefNotes != original.RefNotes)
        changes.Add("RefNotes`"
                    + (original.RefNotes.ToString())
                    + "`"
                    + (modified.RefNotes.ToString()).Replace("`", "-"));

    if (modified.RequestDate != original.RequestDate)
        changes.Add("RequestDate`"
                    + (original.RequestDate.ToString())
                    + "`"
                    + (modified.RequestDate.ToString()).Replace("`", "-"));

    if (modified.ResDelivery != original.ResDelivery)
        changes.Add("ResDelivery`"
                    + (original.ResDelivery.ToString())
                    + "`"
                    + (modified.ResDelivery.ToString()).Replace("`", "-"));

    if (modified.SatPickup != original.SatPickup)
        changes.Add("SatPickup`"
                    + (original.SatPickup.ToString())
                    + "`"
                    + (modified.SatPickup.ToString()).Replace("`", "-"));

    if (modified.ServAlert != original.ServAlert)
        changes.Add("ServAlert`"
                    + (original.ServAlert.ToString())
                    + "`"
                    + (modified.ServAlert.ToString()).Replace("`", "-"));

    if (modified.ServAuthNum != original.ServAuthNum)
        changes.Add("ServAuthNum`"
                    + (original.ServAuthNum.ToString())
                    + "`"
                    + (modified.ServAuthNum.ToString()).Replace("`", "-"));

    if (modified.ServDeliveryDate != original.ServDeliveryDate)
        changes.Add("ServDeliveryDate`"
                    + (original.ServDeliveryDate.ToString())
                    + "`"
                    + (modified.ServDeliveryDate.ToString()).Replace("`", "-"));

    if (modified.ServHomeDel != original.ServHomeDel)
        changes.Add("ServHomeDel`"
                    + (original.ServHomeDel.ToString())
                    + "`"
                    + (modified.ServHomeDel.ToString()).Replace("`", "-"));

    if (modified.ServInstruct != original.ServInstruct)
        changes.Add("ServInstruct`"
                    + (original.ServInstruct.ToString())
                    + "`"
                    + (modified.ServInstruct.ToString()).Replace("`", "-"));

    if (modified.ServPhone != original.ServPhone)
        changes.Add("ServPhone`"
                    + (original.ServPhone.ToString())
                    + "`"
                    + (modified.ServPhone.ToString()).Replace("`", "-"));

    if (modified.ServRef1 != original.ServRef1)
        changes.Add("ServRef1`"
                    + (original.ServRef1.ToString())
                    + "`"
                    + (modified.ServRef1.ToString()).Replace("`", "-"));

    if (modified.ServRef2 != original.ServRef2)
        changes.Add("ServRef2`"
                    + (original.ServRef2.ToString())
                    + "`"
                    + (modified.ServRef2.ToString()).Replace("`", "-"));

    if (modified.ServRef3 != original.ServRef3)
        changes.Add("ServRef3`"
                    + (original.ServRef3.ToString())
                    + "`"
                    + (modified.ServRef3.ToString()).Replace("`", "-"));

    if (modified.ServRef4 != original.ServRef4)
        changes.Add("ServRef4`"
                    + (original.ServRef4.ToString())
                    + "`"
                    + (modified.ServRef4.ToString()).Replace("`", "-"));

    if (modified.ServRef5 != original.ServRef5)
        changes.Add("ServRef5`"
                    + (original.ServRef5.ToString())
                    + "`"
                    + (modified.ServRef5.ToString()).Replace("`", "-"));

    if (modified.ServRelease != original.ServRelease)
        changes.Add("ServRelease`"
                    + (original.ServRelease.ToString())
                    + "`"
                    + (modified.ServRelease.ToString()).Replace("`", "-"));

    if (modified.ServSignature != original.ServSignature)
        changes.Add("ServSignature`"
                    + (original.ServSignature.ToString())
                    + "`"
                    + (modified.ServSignature.ToString()).Replace("`", "-"));

    if (modified.VoidQuote != original.VoidQuote)
        changes.Add("VoidQuote`"
                    + (original.VoidQuote.ToString())
                    + "`"
                    + (modified.VoidQuote.ToString()).Replace("`", "-"));

    if (modified.ApplyChrg != original.ApplyChrg)
        changes.Add("ApplyChrg`"
                    + (original.ApplyChrg.ToString())
                    + "`"
                    + (modified.ApplyChrg.ToString()).Replace("`", "-"));

    if (modified.ChrgAmount != original.ChrgAmount)
        changes.Add("ChrgAmount`"
                    + (original.ChrgAmount.ToString())
                    + "`"
                    + (modified.ChrgAmount.ToString()).Replace("`", "-"));

    if (modified.COD != original.COD)
        changes.Add("COD`"
                    + (original.COD.ToString())
                    + "`"
                    + (modified.COD.ToString()).Replace("`", "-"));
    if (modified.TotalDiscPct != original.TotalDiscPct)
        changes.Add("TotalDiscPct`"
                    + (original.TotalDiscPct.ToString())
                    + "`"
                    + (modified.TotalDiscPct.ToString()).Replace("`", "-"));

    if (modified.TotalExpected != original.TotalExpected)
        changes.Add("TotalExpected`"
                    + (original.TotalExpected.ToString())
                    + "`"
                    + (modified.TotalExpected.ToString()).Replace("`", "-"));

    if (modified.TotalGrossValue != original.TotalGrossValue)
        changes.Add("TotalGrossValue`"
                    + (original.TotalGrossValue.ToString())
                    + "`"
                    + (modified.TotalGrossValue.ToString()).Replace("`", "-"));

    if (modified.TotalMiscAmt != original.TotalMiscAmt)
        changes.Add("TotalMiscAmt`"
                    + (original.TotalMiscAmt.ToString())
                    + "`"
                    + (modified.TotalMiscAmt.ToString()).Replace("`", "-"));

    if (modified.TotalPotential != original.TotalPotential)
        changes.Add("TotalPotential`"
                    + (original.TotalPotential.ToString())
                    + "`"
                    + (modified.TotalPotential.ToString()).Replace("`", "-"));

    if (modified.TotalWorstCs != original.TotalWorstCs)
        changes.Add("TotalWorstCs`"
                    + (original.TotalWorstCs.ToString())
                    + "`"
                    + (modified.TotalWorstCs.ToString()).Replace("`", "-"));

    if (modified.DocTotalBestCs != original.DocTotalBestCs)
        changes.Add("DocTotalBestCs`"
                    + (original.DocTotalBestCs.ToString())
                    + "`"
                    + (modified.DocTotalBestCs.ToString()).Replace("`", "-"));

    if (modified.DocTotalDiscount != original.DocTotalDiscount)
        changes.Add("DocTotalDiscount`"
                    + (original.DocTotalDiscount.ToString())
                    + "`"
                    + (modified.DocTotalDiscount.ToString()).Replace("`", "-"));

    if (modified.DocTotalDiscPct != original.DocTotalDiscPct)
        changes.Add("DocTotalDiscPct`"
                    + (original.DocTotalDiscPct.ToString())
                    + "`"
                    + (modified.DocTotalDiscPct.ToString()).Replace("`", "-"));

    if (modified.DocTotalExpected != original.DocTotalExpected)
        changes.Add("DocTotalExpected`"
                    + (original.DocTotalExpected.ToString())
                    + "`"
                    + (modified.DocTotalExpected.ToString()).Replace("`", "-"));

    if (modified.DocTotalGrossValue != original.DocTotalGrossValue)
        changes.Add("DocTotalGrossValue`"
                    + (original.DocTotalGrossValue.ToString())
                    + "`"
                    + (modified.DocTotalGrossValue.ToString()).Replace("`", "-"));

    if (modified.DocTotalMiscAmt != original.DocTotalMiscAmt)
        changes.Add("DocTotalMiscAmt`"
                    + (original.DocTotalMiscAmt.ToString())
                    + "`"
                    + (modified.DocTotalMiscAmt.ToString()).Replace("`", "-"));

    if (modified.DocTotalPotential != original.DocTotalPotential)
        changes.Add("DocTotalPotential`"
                    + (original.DocTotalPotential.ToString())
                    + "`"
                    + (modified.DocTotalPotential.ToString()).Replace("`", "-"));

    if (modified.DocTotalWorstCs != original.DocTotalWorstCs)
        changes.Add("DocTotalWorstCs`"
                    + (original.DocTotalWorstCs.ToString())
                    + "`"
                    + (modified.DocTotalWorstCs.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalBestCs != original.Rpt1TotalBestCs)
        changes.Add("Rpt1TotalBestCs`"
                    + (original.Rpt1TotalBestCs.ToString())
                    + "`"
                    + (modified.Rpt1TotalBestCs.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalDiscount != original.Rpt1TotalDiscount)
        changes.Add("Rpt1TotalDiscount`"
                    + (original.Rpt1TotalDiscount.ToString())
                    + "`"
                    + (modified.Rpt1TotalDiscount.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalDiscPct != original.Rpt1TotalDiscPct)
        changes.Add("Rpt1TotalDiscPct`"
                    + (original.Rpt1TotalDiscPct.ToString())
                    + "`"
                    + (modified.Rpt1TotalDiscPct.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalExpected != original.Rpt1TotalExpected)
        changes.Add("Rpt1TotalExpected`"
                    + (original.Rpt1TotalExpected.ToString())
                    + "`"
                    + (modified.Rpt1TotalExpected.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalGrossValue != original.Rpt1TotalGrossValue)
        changes.Add("Rpt1TotalGrossValue`"
                    + (original.Rpt1TotalGrossValue.ToString())
                    + "`"
                    + (modified.Rpt1TotalGrossValue.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalMiscAmt != original.Rpt1TotalMiscAmt)
        changes.Add("Rpt1TotalMiscAmt`"
                    + (original.Rpt1TotalMiscAmt.ToString())
                    + "`"
                    + (modified.Rpt1TotalMiscAmt.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalPotential != original.Rpt1TotalPotential)
        changes.Add("Rpt1TotalPotential`"
                    + (original.Rpt1TotalPotential.ToString())
                    + "`"
                    + (modified.Rpt1TotalPotential.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalWorstCs != original.Rpt1TotalWorstCs)
        changes.Add("Rpt1TotalWorstCs`"
                    + (original.Rpt1TotalWorstCs.ToString())
                    + "`"
                    + (modified.Rpt1TotalWorstCs.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalBestCs != original.Rpt2TotalBestCs)
        changes.Add("Rpt2TotalBestCs`"
                    + (original.Rpt2TotalBestCs.ToString())
                    + "`"
                    + (modified.Rpt2TotalBestCs.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalDiscount != original.Rpt2TotalDiscount)
        changes.Add("Rpt2TotalDiscount`"
                    + (original.Rpt2TotalDiscount.ToString())
                    + "`"
                    + (modified.Rpt2TotalDiscount.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalDiscPct != original.Rpt2TotalDiscPct)
        changes.Add("Rpt2TotalDiscPct`"
                    + (original.Rpt2TotalDiscPct.ToString())
                    + "`"
                    + (modified.Rpt2TotalDiscPct.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalExpected != original.Rpt2TotalExpected)
        changes.Add("Rpt2TotalExpected`"
                    + (original.Rpt2TotalExpected.ToString())
                    + "`"
                    + (modified.Rpt2TotalExpected.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalGrossValue != original.Rpt2TotalGrossValue)
        changes.Add("Rpt2TotalGrossValue`"
                    + (original.Rpt2TotalGrossValue.ToString())
                    + "`"
                    + (modified.Rpt2TotalGrossValue.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalMiscAmt != original.Rpt2TotalMiscAmt)
        changes.Add("Rpt2TotalMiscAmt`"
                    + (original.Rpt2TotalMiscAmt.ToString())
                    + "`"
                    + (modified.Rpt2TotalMiscAmt.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalPotential != original.Rpt2TotalPotential)
        changes.Add("Rpt2TotalPotential`"
                    + (original.Rpt2TotalPotential.ToString())
                    + "`"
                    + (modified.Rpt2TotalPotential.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalWorstCs != original.Rpt2TotalWorstCs)
        changes.Add("Rpt2TotalWorstCs`"
                    + (original.Rpt2TotalWorstCs.ToString())
                    + "`"
                    + (modified.Rpt2TotalWorstCs.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalBestCs != original.Rpt3TotalBestCs)
        changes.Add("Rpt3TotalBestCs`"
                    + (original.Rpt3TotalBestCs.ToString())
                    + "`"
                    + (modified.Rpt3TotalBestCs.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalDiscount != original.Rpt3TotalDiscount)
        changes.Add("Rpt3TotalDiscount`"
                    + (original.Rpt3TotalDiscount.ToString())
                    + "`"
                    + (modified.Rpt3TotalDiscount.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalDiscPct != original.Rpt3TotalDiscPct)
        changes.Add("Rpt3TotalDiscPct`"
                    + (original.Rpt3TotalDiscPct.ToString())
                    + "`"
                    + (modified.Rpt3TotalDiscPct.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalExpected != original.Rpt3TotalExpected)
        changes.Add("Rpt3TotalExpected`"
                    + (original.Rpt3TotalExpected.ToString())
                    + "`"
                    + (modified.Rpt3TotalExpected.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalGrossValue != original.Rpt3TotalGrossValue)
        changes.Add("Rpt3TotalGrossValue`"
                    + (original.Rpt3TotalGrossValue.ToString())
                    + "`"
                    + (modified.Rpt3TotalGrossValue.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalMiscAmt != original.Rpt3TotalMiscAmt)
        changes.Add("Rpt3TotalMiscAmt`"
                    + (original.Rpt3TotalMiscAmt.ToString())
                    + "`"
                    + (modified.Rpt3TotalMiscAmt.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalPotential != original.Rpt3TotalPotential)
        changes.Add("Rpt3TotalPotential`"
                    + (original.Rpt3TotalPotential.ToString())
                    + "`"
                    + (modified.Rpt3TotalPotential.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalWorstCs != original.Rpt3TotalWorstCs)
        changes.Add("Rpt3TotalWorstCs`"
                    + (original.Rpt3TotalWorstCs.ToString())
                    + "`"
                    + (modified.Rpt3TotalWorstCs.ToString()).Replace("`", "-"));

    if (modified.TotalBestCs != original.TotalBestCs)
        changes.Add("TotalBestCs`"
                    + (original.TotalBestCs.ToString())
                    + "`"
                    + (modified.TotalBestCs.ToString()).Replace("`", "-"));

    if (modified.TotalDiscount != original.TotalDiscount)
        changes.Add("TotalDiscount`"
                    + (original.TotalDiscount.ToString())
                    + "`"
                    + (modified.TotalDiscount.ToString()).Replace("`", "-"));
    if (modified.CODAmount != original.CODAmount)
        changes.Add("CODAmount`"
                    + (original.CODAmount.ToString())
                    + "`"
                    + (modified.CODAmount.ToString()).Replace("`", "-"));

    if (modified.CODCheck != original.CODCheck)
        changes.Add("CODCheck`"
                    + (original.CODCheck.ToString())
                    + "`"
                    + (modified.CODCheck.ToString()).Replace("`", "-"));

    if (modified.CODFreight != original.CODFreight)
        changes.Add("CODFreight`"
                    + (original.CODFreight.ToString())
                    + "`"
                    + (modified.CODFreight.ToString()).Replace("`", "-"));

    if (modified.LOQPrepressText != original.LOQPrepressText)
        changes.Add("LOQPrepressText`"
                    + (original.LOQPrepressText.ToString())
                    + "`"
                    + (modified.LOQPrepressText.ToString()).Replace("`", "-"));

    if (modified.LOQNewPageOnQuoteLine != original.LOQNewPageOnQuoteLine)
        changes.Add("LOQNewPageOnQuoteLine`"
                    + (original.LOQNewPageOnQuoteLine.ToString())
                    + "`"
                    + (modified.LOQNewPageOnQuoteLine.ToString()).Replace("`", "-"));

    if (modified.LOQBookPCFinishing != original.LOQBookPCFinishing)
        changes.Add("LOQBookPCFinishing`"
                    + (original.LOQBookPCFinishing.ToString())
                    + "`"
                    + (modified.LOQBookPCFinishing.ToString()).Replace("`", "-"));

    if (modified.LOQBookPCPaper != original.LOQBookPCPaper)
        changes.Add("LOQBookPCPaper`"
                    + (original.LOQBookPCPaper.ToString())
                    + "`"
                    + (modified.LOQBookPCPaper.ToString()).Replace("`", "-"));

    if (modified.LOQBookPCPress != original.LOQBookPCPress)
        changes.Add("LOQBookPCPress`"
                    + (original.LOQBookPCPress.ToString())
                    + "`"
                    + (modified.LOQBookPCPress.ToString()).Replace("`", "-"));

    if (modified.LOQBookPCPlates != original.LOQBookPCPlates)
        changes.Add("LOQBookPCPlates`"
                    + (original.LOQBookPCPlates.ToString())
                    + "`"
                    + (modified.LOQBookPCPlates.ToString()).Replace("`", "-"));

    if (modified.LOQVariations != original.LOQVariations)
        changes.Add("LOQVariations`"
                    + (original.LOQVariations.ToString())
                    + "`"
                    + (modified.LOQVariations.ToString()).Replace("`", "-"));

    if (modified.AEPLOQType != original.AEPLOQType)
        changes.Add("AEPLOQType`"
                    + (original.AEPLOQType.ToString())
                    + "`"
                    + (modified.AEPLOQType.ToString()).Replace("`", "-"));

    if (modified.LOQPrepressStyle != original.LOQPrepressStyle)
        changes.Add("LOQPrepressStyle`"
                    + (original.LOQPrepressStyle.ToString())
                    + "`"
                    + (modified.LOQPrepressStyle.ToString()).Replace("`", "-"));

    if (modified.QuoteCSR != original.QuoteCSR)
        changes.Add("QuoteCSR`"
                    + (original.QuoteCSR.ToString())
                    + "`"
                    + (modified.QuoteCSR.ToString()).Replace("`", "-"));

    if (modified.DueHour != original.DueHour)
        changes.Add("DueHour`"
                    + (original.DueHour.ToString())
                    + "`"
                    + (modified.DueHour.ToString()).Replace("`", "-"));

    if (modified.ECCConfirmed != original.ECCConfirmed)
        changes.Add("ECCConfirmed`"
                    + (original.ECCConfirmed.ToString())
                    + "`"
                    + (modified.ECCConfirmed.ToString()).Replace("`", "-"));

    if (modified.ECCConfirmedBy != original.ECCConfirmedBy)
        changes.Add("ECCConfirmedBy`"
                    + (original.ECCConfirmedBy.ToString())
                    + "`"
                    + (modified.ECCConfirmedBy.ToString()).Replace("`", "-"));

    if (modified.ECCMsgType != original.ECCMsgType)
        changes.Add("ECCMsgType`"
                    + (original.ECCMsgType.ToString())
                    + "`"
                    + (modified.ECCMsgType.ToString()).Replace("`", "-"));

    if (modified.ECCWebReady != original.ECCWebReady)
        changes.Add("ECCWebReady`"
                    + (original.ECCWebReady.ToString())
                    + "`"
                    + (modified.ECCWebReady.ToString()).Replace("`", "-"));

    if (modified.ECCQuoteNum != original.ECCQuoteNum)
        changes.Add("ECCQuoteNum`"
                    + (original.ECCQuoteNum.ToString())
                    + "`"
                    + (modified.ECCQuoteNum.ToString()).Replace("`", "-"));

    if (modified.ECCCmmtRef != original.ECCCmmtRef)
        changes.Add("ECCCmmtRef`"
                    + (original.ECCCmmtRef.ToString())
                    + "`"
                    + (modified.ECCCmmtRef.ToString()).Replace("`", "-"));

    if (modified.ECCComment != original.ECCComment)
        changes.Add("ECCComment`"
                    + (original.ECCComment.ToString())
                    + "`"
                    + (modified.ECCComment.ToString()).Replace("`", "-"));

    if (modified.ECCStatus != original.ECCStatus)
        changes.Add("ECCStatus`"
                    + (original.ECCStatus.ToString())
                    + "`"
                    + (modified.ECCStatus.ToString()).Replace("`", "-"));

    if (modified.ECCExpirationDate != original.ECCExpirationDate)
        changes.Add("ECCExpirationDate`"
                    + (original.ECCExpirationDate.ToString())
                    + "`"
                    + (modified.ECCExpirationDate.ToString()).Replace("`", "-"));

    if (modified.ECCCmmtRefSK != original.ECCCmmtRefSK)
        changes.Add("ECCCmmtRefSK`"
                    + (original.ECCCmmtRefSK.ToString())
                    + "`"
                    + (modified.ECCCmmtRefSK.ToString()).Replace("`", "-"));

    if (modified.ExternalCRMQuote != original.ExternalCRMQuote)
        changes.Add("ExternalCRMQuote`"
                    + (original.ExternalCRMQuote.ToString())
                    + "`"
                    + (modified.ExternalCRMQuote.ToString()).Replace("`", "-"));

    if (modified.ExternalCRMQuoteID != original.ExternalCRMQuoteID)
        changes.Add("ExternalCRMQuoteID`"
                    + (original.ExternalCRMQuoteID.ToString())
                    + "`"
                    + (modified.ExternalCRMQuoteID.ToString()).Replace("`", "-"));

    if (modified.ExternalCRMOrderID != original.ExternalCRMOrderID)
        changes.Add("ExternalCRMOrderID`"
                    + (original.ExternalCRMOrderID.ToString())
                    + "`"
                    + (modified.ExternalCRMOrderID.ToString()).Replace("`", "-"));

    if (modified.ECCSalesRepID != original.ECCSalesRepID)
        changes.Add("ECCSalesRepID`"
                    + (original.ECCSalesRepID.ToString())
                    + "`"
                    + (modified.ECCSalesRepID.ToString()).Replace("`", "-"));

    if (modified.Tax != original.Tax)
        changes.Add("Tax`"
                    + (original.Tax.ToString())
                    + "`"
                    + (modified.Tax.ToString()).Replace("`", "-"));

    if (modified.DocTax != original.DocTax)
        changes.Add("DocTax`"
                    + (original.DocTax.ToString())
                    + "`"
                    + (modified.DocTax.ToString()).Replace("`", "-"));

    if (modified.Rpt1Tax != original.Rpt1Tax)
        changes.Add("Rpt1Tax`"
                    + (original.Rpt1Tax.ToString())
                    + "`"
                    + (modified.Rpt1Tax.ToString()).Replace("`", "-"));

    if (modified.Rpt2Tax != original.Rpt2Tax)
        changes.Add("Rpt2Tax`"
                    + (original.Rpt2Tax.ToString())
                    + "`"
                    + (modified.Rpt2Tax.ToString()).Replace("`", "-"));

    if (modified.Rpt3Tax != original.Rpt3Tax)
        changes.Add("Rpt3Tax`"
                    + (original.Rpt3Tax.ToString())
                    + "`"
                    + (modified.Rpt3Tax.ToString()).Replace("`", "-"));

    if (modified.HdrTaxNoUpdt != original.HdrTaxNoUpdt)
        changes.Add("HdrTaxNoUpdt`"
                    + (original.HdrTaxNoUpdt.ToString())
                    + "`"
                    + (modified.HdrTaxNoUpdt.ToString()).Replace("`", "-"));

    if (modified.ExternalCRMLastSync != original.ExternalCRMLastSync)
        changes.Add("ExternalCRMLastSync`"
                    + (original.ExternalCRMLastSync.ToString())
                    + "`"
                    + (modified.ExternalCRMLastSync.ToString()).Replace("`", "-"));

    if (modified.ExternalCRMSyncRequired != original.ExternalCRMSyncRequired)
        changes.Add("ExternalCRMSyncRequired`"
                    + (original.ExternalCRMSyncRequired.ToString())
                    + "`"
                    + (modified.ExternalCRMSyncRequired.ToString()).Replace("`", "-"));

    if (modified.TotalClaimsCredit != original.TotalClaimsCredit)
        changes.Add("TotalClaimsCredit`"
                    + (original.TotalClaimsCredit.ToString())
                    + "`"
                    + (modified.TotalClaimsCredit.ToString()).Replace("`", "-"));

    if (modified.DocTotalClaimsCredit != original.DocTotalClaimsCredit)
        changes.Add("DocTotalClaimsCredit`"
                    + (original.DocTotalClaimsCredit.ToString())
                    + "`"
                    + (modified.DocTotalClaimsCredit.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalClaimsCredit != original.Rpt1TotalClaimsCredit)
        changes.Add("Rpt1TotalClaimsCredit`"
                    + (original.Rpt1TotalClaimsCredit.ToString())
                    + "`"
                    + (modified.Rpt1TotalClaimsCredit.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalClaimsCredit != original.Rpt2TotalClaimsCredit)
        changes.Add("Rpt2TotalClaimsCredit`"
                    + (original.Rpt2TotalClaimsCredit.ToString())
                    + "`"
                    + (modified.Rpt2TotalClaimsCredit.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalClaimsCredit != original.Rpt3TotalClaimsCredit)
        changes.Add("Rpt3TotalClaimsCredit`"
                    + (original.Rpt3TotalClaimsCredit.ToString())
                    + "`"
                    + (modified.Rpt3TotalClaimsCredit.ToString()).Replace("`", "-"));

    if (modified.TotalClaimsTax != original.TotalClaimsTax)
        changes.Add("TotalClaimsTax`"
                    + (original.TotalClaimsTax.ToString())
                    + "`"
                    + (modified.TotalClaimsTax.ToString()).Replace("`", "-"));

    if (modified.DocTotalClaimsTax != original.DocTotalClaimsTax)
        changes.Add("DocTotalClaimsTax`"
                    + (original.DocTotalClaimsTax.ToString())
                    + "`"
                    + (modified.DocTotalClaimsTax.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalClaimsTax != original.Rpt1TotalClaimsTax)
        changes.Add("Rpt1TotalClaimsTax`"
                    + (original.Rpt1TotalClaimsTax.ToString())
                    + "`"
                    + (modified.Rpt1TotalClaimsTax.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalClaimsTax != original.Rpt2TotalClaimsTax)
        changes.Add("Rpt2TotalClaimsTax`"
                    + (original.Rpt2TotalClaimsTax.ToString())
                    + "`"
                    + (modified.Rpt2TotalClaimsTax.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalClaimsTax != original.Rpt3TotalClaimsTax)
        changes.Add("Rpt3TotalClaimsTax`"
                    + (original.Rpt3TotalClaimsTax.ToString())
                    + "`"
                    + (modified.Rpt3TotalClaimsTax.ToString()).Replace("`", "-"));

    if (modified.TotalClaimsSATax != original.TotalClaimsSATax)
        changes.Add("TotalClaimsSATax`"
                    + (original.TotalClaimsSATax.ToString())
                    + "`"
                    + (modified.TotalClaimsSATax.ToString()).Replace("`", "-"));

    if (modified.DocTotalClaimsSATax != original.DocTotalClaimsSATax)
        changes.Add("DocTotalClaimsSATax`"
                    + (original.DocTotalClaimsSATax.ToString())
                    + "`"
                    + (modified.DocTotalClaimsSATax.ToString()).Replace("`", "-"));
    if (modified.Rpt1TotalClaimsSATax != original.Rpt1TotalClaimsSATax)
        changes.Add("Rpt1TotalClaimsSATax`"
                    + (original.Rpt1TotalClaimsSATax.ToString())
                    + "`"
                    + (modified.Rpt1TotalClaimsSATax.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalClaimsSATax != original.Rpt2TotalClaimsSATax)
        changes.Add("Rpt2TotalClaimsSATax`"
                    + (original.Rpt2TotalClaimsSATax.ToString())
                    + "`"
                    + (modified.Rpt2TotalClaimsSATax.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalClaimsSATax != original.Rpt3TotalClaimsSATax)
        changes.Add("Rpt3TotalClaimsSATax`"
                    + (original.Rpt3TotalClaimsSATax.ToString())
                    + "`"
                    + (modified.Rpt3TotalClaimsSATax.ToString()).Replace("`", "-"));

    if (modified.TotalClaimsWHTax != original.TotalClaimsWHTax)
        changes.Add("TotalClaimsWHTax`"
                    + (original.TotalClaimsWHTax.ToString())
                    + "`"
                    + (modified.TotalClaimsWHTax.ToString()).Replace("`", "-"));

    if (modified.DocTotalClaimsWHTax != original.DocTotalClaimsWHTax)
        changes.Add("DocTotalClaimsWHTax`"
                    + (original.DocTotalClaimsWHTax.ToString())
                    + "`"
                    + (modified.DocTotalClaimsWHTax.ToString()).Replace("`", "-"));

    if (modified.Rpt1TotalClaimsWHTax != original.Rpt1TotalClaimsWHTax)
        changes.Add("Rpt1TotalClaimsWHTax`"
                    + (original.Rpt1TotalClaimsWHTax.ToString())
                    + "`"
                    + (modified.Rpt1TotalClaimsWHTax.ToString()).Replace("`", "-"));

    if (modified.Rpt2TotalClaimsWHTax != original.Rpt2TotalClaimsWHTax)
        changes.Add("Rpt2TotalClaimsWHTax`"
                    + (original.Rpt2TotalClaimsWHTax.ToString())
                    + "`"
                    + (modified.Rpt2TotalClaimsWHTax.ToString()).Replace("`", "-"));

    if (modified.Rpt3TotalClaimsWHTax != original.Rpt3TotalClaimsWHTax)
        changes.Add("Rpt3TotalClaimsWHTax`"
                    + (original.Rpt3TotalClaimsWHTax.ToString())
                    + "`"
                    + (modified.Rpt3TotalClaimsWHTax.ToString()).Replace("`", "-"));

    if (modified.OTSTaxValidationStatus != original.OTSTaxValidationStatus)
        changes.Add("OTSTaxValidationStatus`"
                    + (original.OTSTaxValidationStatus.ToString())
                    + "`"
                    + (modified.OTSTaxValidationStatus.ToString()).Replace("`", "-"));

    if (modified.OTSTaxValidationDate != original.OTSTaxValidationDate)
        changes.Add("OTSTaxValidationDate`"
                    + (original.OTSTaxValidationDate.ToString())
                    + "`"
                    + (modified.OTSTaxValidationDate.ToString()).Replace("`", "-"));

    if (modified.FSMSendTo != original.FSMSendTo)
        changes.Add("FSMSendTo`"
                    + (original.FSMSendTo.ToString())
                    + "`"
                    + (modified.FSMSendTo.ToString()).Replace("`", "-"));

    if (modified.IncotermCode != original.IncotermCode)
        changes.Add("IncotermCode`"
                    + (original.IncotermCode.ToString())
                    + "`"
                    + (modified.IncotermCode.ToString()).Replace("`", "-"));

    if (modified.IncotermLocation != original.IncotermLocation)
        changes.Add("IncotermLocation`"
                    + (original.IncotermLocation.ToString())
                    + "`"
                    + (modified.IncotermLocation.ToString()).Replace("`", "-"));

    if (modified.EntryPerson != original.EntryPerson)
        changes.Add("EntryPerson`"
                    + (original.EntryPerson.ToString())
                    + "`"
                    + (modified.EntryPerson.ToString()).Replace("`", "-"));

    // Process QuoteDtl changes
    if (ds.QuoteDtl != null && ds.QuoteDtl.Count > 0)
    {
        foreach (var modifiedDtl in ds.QuoteDtl)
        {
            var originalDtl = (from dbDtl in Db.QuoteDtl
                              where dbDtl.Company == companyID
                              && dbDtl.QuoteNum.ToString() == quoteNum
                              && dbDtl.QuoteLine == modifiedDtl.QuoteLine
                              select dbDtl).FirstOrDefault();

            if (originalDtl == null)
            {
                // New quote line added
                changes.Add($"QuoteLine{modifiedDtl.QuoteLine.ToString()}`NEW`{modifiedDtl.QuoteLine.ToString()}");
            }
            else
            {
                // Check for changes in QuoteDtl fields
                if (modifiedDtl.PartNum != originalDtl.PartNum)
                    changes.Add($"QuoteLine{modifiedDtl.QuoteLine.ToString()}_PartNum`"
                                + (originalDtl.PartNum.ToString())
                                + "`"
                                + (modifiedDtl.PartNum.ToString()).Replace("`", "-"));

                if (modifiedDtl.LineDesc != originalDtl.LineDesc)
                    changes.Add($"QuoteLine{modifiedDtl.QuoteLine.ToString()}_LineDesc`"
                                + (originalDtl.LineDesc.ToString())
                                + "`"
                                + (modifiedDtl.LineDesc.ToString()).Replace("`", "-"));

                if (modifiedDtl.OrderQty != originalDtl.OrderQty)
                    changes.Add($"QuoteLine{modifiedDtl.QuoteLine.ToString()}_OrderQty`"
                                + (originalDtl.OrderQty.ToString())
                                + "`"
                                + (modifiedDtl.OrderQty.ToString()).Replace("`", "-"));

                if (modifiedDtl.Discount != originalDtl.Discount)
                    changes.Add($"QuoteLine{modifiedDtl.QuoteLine.ToString()}_Discount`"
                                + (originalDtl.Discount.ToString())
                                + "`"
                                + (modifiedDtl.Discount.ToString()).Replace("`", "-"));

                if (modifiedDtl.DocDiscount != originalDtl.DocDiscount)
                    changes.Add($"QuoteLine{modifiedDtl.QuoteLine.ToString()}_DocDiscount`"
                                + (originalDtl.DocDiscount.ToString())
                                + "`"
                                + (modifiedDtl.DocDiscount.ToString()).Replace("`", "-"));
                
                if (modifiedDtl.DocExpUnitPrice != originalDtl.DocExpUnitPrice)
                    changes.Add($"QuoteLine{modifiedDtl.QuoteLine.ToString()}_DocExpUnitPrice`"
                                + (originalDtl.DocExpUnitPrice.ToString())
                                + "`"
                                + (modifiedDtl.DocExpUnitPrice.ToString()).Replace("`", "-"));

                if (modifiedDtl.ProdCode != originalDtl.ProdCode)
                    changes.Add($"QuoteLine{modifiedDtl.QuoteLine.ToString()}_ProdCode`"
                                + (originalDtl.ProdCode.ToString())
                                + "`"
                                + (modifiedDtl.ProdCode.ToString()).Replace("`", "-"));
            }
        }

        // Check for deleted quote lines
        var originalDtlLines = (from dbDtl in Db.QuoteDtl
                               where dbDtl.Company == companyID
                               && dbDtl.QuoteNum.ToString() == quoteNum
                               select dbDtl).ToList();

        foreach (var originalDtl in originalDtlLines)
        {
            var modifiedDtl = ds.QuoteDtl.FirstOrDefault(d => d.QuoteLine == originalDtl.QuoteLine);
            if (modifiedDtl == null)
            {
                // Quote line was deleted
                changes.Add($"QuoteLine{originalDtl.QuoteLine.ToString()}`DELETED`");
            }
        }
    }

    if (changes.Count > 0)
    {
        try
        {
            callFunc = true;

            string allChanges = string.Join("`~`", changes);
            test2 = $"Debug: Created allChanges string, length={allChanges.Length.ToString()}";

            // Simple splitting without complex logic
            if (allChanges.Length <= 1000)
            {
                changesMade = allChanges;
                test2 = "Debug: Single chunk assigned to changesMade";
            }
            else
            {
                // Split into 1000-character chunks with overflow handling
                const int maxChunkSize = 1000;
                int totalLength = allChanges.Length;

                // Clear all changesMade variables first
                changesMade = "";
                changesMade2 = "";
                changesMade3 = "";
                changesMade4 = "";
                changesMade5 = "";
                changesMade6 = "";
                changesMade7 = "";
                changesMade8 = "";
                changesMade9 = "";
                changesMade10 = "";

                // Assign chunks based on position - each exactly 1000 chars (or remaining)
                if (totalLength > 0)
                {
                    changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
                }
                if (totalLength > 1000)
                {
                    changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
                }
                if (totalLength > 2000)
                {
                    changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
                }
                if (totalLength > 3000)
                {
                    changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
                }
                if (totalLength > 4000)
                {
                    changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
                }
                if (totalLength > 5000)
                {
                    changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
                }
                if (totalLength > 6000)
                {
                    changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
                }
                if (totalLength > 7000)
                {
                    changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
                }
                if (totalLength > 8000)
                {
                    changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
                }
                if (totalLength > 9000)
                {
                    changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
                }

                test2 = $"Debug: Split {totalLength.ToString()} chars into chunks successfully";
            }
        }
        catch (System.Exception ex)
        {
            test2 = $"Debug: Exception in splitting logic: {ex.Message} - Type: {ex.GetType().Name}";
            changesMade = "Error in splitting logic";
        }
    }
}