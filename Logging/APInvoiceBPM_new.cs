// =================================================================
// AP Invoice BPM - Comprehensive Change Tracking
// =================================================================
// This BPM tracks changes to AP Invoice Header and Detail records
// and logs them using the backtick-tilde-backtick (`~`) delimiter
// with 1000-character chunking across changesMade variables.
// =================================================================

using System;
using System.Collections.Generic;
using System.Linq;

// Initialize variables
List<string> changes = new List<string>();
bool callFunc = false;

// =================================================================
// APInvHed (Header) Change Tracking
// =================================================================

if (ds.APInvHed.Any())
{
    var originalAPInvHed = ds.APInvHed[0];
    var modifiedAPInvHed = ds.APInvHed[0];

    // Check if this is a deletion
    if (modifiedAPInvHed.RowMod == "D")
    {
        changes.Add("APInvHed.DELETED`" + originalAPInvHed.InvoiceNum + "`DELETED");
    }
    else
    {
        // Company
        if (modifiedAPInvHed.Company != originalAPInvHed.Company)
        {
            changes.Add("APInvHed.Company`"
                       + originalAPInvHed.Company.ToString()
                       + "`"
                       + modifiedAPInvHed.Company.ToString().Replace("`", "-"));
        }

        // Open Payable
        if (modifiedAPInvHed.OpenPayable != originalAPInvHed.OpenPayable)
        {
            changes.Add("APInvHed.OpenPayable`"
                       + originalAPInvHed.OpenPayable.ToString()
                       + "`"
                       + modifiedAPInvHed.OpenPayable.ToString().Replace("`", "-"));
        }

        // Vendor Number
        if (modifiedAPInvHed.VendorNum != originalAPInvHed.VendorNum)
        {
            changes.Add("APInvHed.VendorNum`"
                       + originalAPInvHed.VendorNum.ToString()
                       + "`"
                       + modifiedAPInvHed.VendorNum.ToString().Replace("`", "-"));
        }

        // Invoice Number
        if (modifiedAPInvHed.InvoiceNum != originalAPInvHed.InvoiceNum)
        {
            changes.Add("APInvHed.InvoiceNum`"
                       + originalAPInvHed.InvoiceNum.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceNum.ToString().Replace("`", "-"));
        }

        // Debit Memo
        if (modifiedAPInvHed.DebitMemo != originalAPInvHed.DebitMemo)
        {
            changes.Add("APInvHed.DebitMemo`"
                       + originalAPInvHed.DebitMemo.ToString()
                       + "`"
                       + modifiedAPInvHed.DebitMemo.ToString().Replace("`", "-"));
        }

        // Invoice Date
        if (modifiedAPInvHed.InvoiceDate != originalAPInvHed.InvoiceDate)
        {
            changes.Add("APInvHed.InvoiceDate`"
                       + originalAPInvHed.InvoiceDate.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceDate.ToString().Replace("`", "-"));
        }

        // Terms Code
        if (modifiedAPInvHed.TermsCode != originalAPInvHed.TermsCode)
        {
            changes.Add("APInvHed.TermsCode`"
                       + originalAPInvHed.TermsCode.ToString()
                       + "`"
                       + modifiedAPInvHed.TermsCode.ToString().Replace("`", "-"));
        }

        // Tax Amount
        if (modifiedAPInvHed.TaxAmt != originalAPInvHed.TaxAmt)
        {
            changes.Add("APInvHed.TaxAmt`"
                       + originalAPInvHed.TaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.TaxAmt.ToString().Replace("`", "-"));
        }

        // Document Tax Amount
        if (modifiedAPInvHed.DocTaxAmt != originalAPInvHed.DocTaxAmt)
        {
            changes.Add("APInvHed.DocTaxAmt`"
                       + originalAPInvHed.DocTaxAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocTaxAmt.ToString().Replace("`", "-"));
        }

        // Discount Date
        if (modifiedAPInvHed.DiscountDate != originalAPInvHed.DiscountDate)
        {
            changes.Add("APInvHed.DiscountDate`"
                       + originalAPInvHed.DiscountDate.ToString()
                       + "`"
                       + modifiedAPInvHed.DiscountDate.ToString().Replace("`", "-"));
        }

        // Discount Amount
        if (modifiedAPInvHed.DiscountAmt != originalAPInvHed.DiscountAmt)
        {
            changes.Add("APInvHed.DiscountAmt`"
                       + originalAPInvHed.DiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DiscountAmt.ToString().Replace("`", "-"));
        }

        // Document Discount Amount
        if (modifiedAPInvHed.DocDiscountAmt != originalAPInvHed.DocDiscountAmt)
        {
            changes.Add("APInvHed.DocDiscountAmt`"
                       + originalAPInvHed.DocDiscountAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocDiscountAmt.ToString().Replace("`", "-"));
        }

        // Due Date
        if (modifiedAPInvHed.DueDate != originalAPInvHed.DueDate)
        {
            changes.Add("APInvHed.DueDate`"
                       + originalAPInvHed.DueDate.ToString()
                       + "`"
                       + modifiedAPInvHed.DueDate.ToString().Replace("`", "-"));
        }

        // Pay Dates
        if (modifiedAPInvHed.PayDates != originalAPInvHed.PayDates)
        {
            changes.Add("APInvHed.PayDates`"
                       + originalAPInvHed.PayDates.ToString()
                       + "`"
                       + modifiedAPInvHed.PayDates.ToString().Replace("`", "-"));
        }

        // Pay Amounts
        if (modifiedAPInvHed.PayAmounts != originalAPInvHed.PayAmounts)
        {
            changes.Add("APInvHed.PayAmounts`"
                       + originalAPInvHed.PayAmounts.ToString()
                       + "`"
                       + modifiedAPInvHed.PayAmounts.ToString().Replace("`", "-"));
        }

        // Document Pay Amounts
        if (modifiedAPInvHed.DocPayAmounts != originalAPInvHed.DocPayAmounts)
        {
            changes.Add("APInvHed.DocPayAmounts`"
                       + originalAPInvHed.DocPayAmounts.ToString()
                       + "`"
                       + modifiedAPInvHed.DocPayAmounts.ToString().Replace("`", "-"));
        }

        // GL Posted
        if (modifiedAPInvHed.GLPosted != originalAPInvHed.GLPosted)
        {
            changes.Add("APInvHed.GLPosted`"
                       + originalAPInvHed.GLPosted.ToString()
                       + "`"
                       + modifiedAPInvHed.GLPosted.ToString().Replace("`", "-"));
        }

        // Group ID
        if (modifiedAPInvHed.GroupID != originalAPInvHed.GroupID)
        {
            changes.Add("APInvHed.GroupID`"
                       + originalAPInvHed.GroupID.ToString()
                       + "`"
                       + modifiedAPInvHed.GroupID.ToString().Replace("`", "-"));
        }

        // Posted
        if (modifiedAPInvHed.Posted != originalAPInvHed.Posted)
        {
            changes.Add("APInvHed.Posted`"
                       + originalAPInvHed.Posted.ToString()
                       + "`"
                       + modifiedAPInvHed.Posted.ToString().Replace("`", "-"));
        }

        // Fiscal Year
        if (modifiedAPInvHed.FiscalYear != originalAPInvHed.FiscalYear)
        {
            changes.Add("APInvHed.FiscalYear`"
                       + originalAPInvHed.FiscalYear.ToString()
                       + "`"
                       + modifiedAPInvHed.FiscalYear.ToString().Replace("`", "-"));
        }

        // Fiscal Period
        if (modifiedAPInvHed.FiscalPeriod != originalAPInvHed.FiscalPeriod)
        {
            changes.Add("APInvHed.FiscalPeriod`"
                       + originalAPInvHed.FiscalPeriod.ToString()
                       + "`"
                       + modifiedAPInvHed.FiscalPeriod.ToString().Replace("`", "-"));
        }

        // Start Up
        if (modifiedAPInvHed.StartUp != originalAPInvHed.StartUp)
        {
            changes.Add("APInvHed.StartUp`"
                       + originalAPInvHed.StartUp.ToString()
                       + "`"
                       + modifiedAPInvHed.StartUp.ToString().Replace("`", "-"));
        }

        // Invoice Reference
        if (modifiedAPInvHed.InvoiceRef != originalAPInvHed.InvoiceRef)
        {
            changes.Add("APInvHed.InvoiceRef`"
                       + originalAPInvHed.InvoiceRef.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceRef.ToString().Replace("`", "-"));
        }

        // Entry Person
        if (modifiedAPInvHed.EntryPerson != originalAPInvHed.EntryPerson)
        {
            changes.Add("APInvHed.EntryPerson`"
                       + originalAPInvHed.EntryPerson.ToString()
                       + "`"
                       + modifiedAPInvHed.EntryPerson.ToString().Replace("`", "-"));
        }

        // Invoice Comment
        if (modifiedAPInvHed.InvoiceComment != originalAPInvHed.InvoiceComment)
        {
            changes.Add("APInvHed.InvoiceComment`"
                       + originalAPInvHed.InvoiceComment.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceComment.ToString().Replace("`", "-"));
        }

        // Invoice Amount
        if (modifiedAPInvHed.InvoiceAmt != originalAPInvHed.InvoiceAmt)
        {
            changes.Add("APInvHed.InvoiceAmt`"
                       + originalAPInvHed.InvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceAmt.ToString().Replace("`", "-"));
        }

        // Document Invoice Amount
        if (modifiedAPInvHed.DocInvoiceAmt != originalAPInvHed.DocInvoiceAmt)
        {
            changes.Add("APInvHed.DocInvoiceAmt`"
                       + originalAPInvHed.DocInvoiceAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocInvoiceAmt.ToString().Replace("`", "-"));
        }

        // Document Invoice Vendor Amount
        if (modifiedAPInvHed.DocInvoiceVendorAmt != originalAPInvHed.DocInvoiceVendorAmt)
        {
            changes.Add("APInvHed.DocInvoiceVendorAmt`"
                       + originalAPInvHed.DocInvoiceVendorAmt.ToString()
                       + "`"
                       + modifiedAPInvHed.DocInvoiceVendorAmt.ToString().Replace("`", "-"));
        }

        // Invoice Balance
        if (modifiedAPInvHed.InvoiceBal != originalAPInvHed.InvoiceBal)
        {
            changes.Add("APInvHed.InvoiceBal`"
                       + originalAPInvHed.InvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceBal.ToString().Replace("`", "-"));
        }

        // Document Invoice Balance
        if (modifiedAPInvHed.DocInvoiceBal != originalAPInvHed.DocInvoiceBal)
        {
            changes.Add("APInvHed.DocInvoiceBal`"
                       + originalAPInvHed.DocInvoiceBal.ToString()
                       + "`"
                       + modifiedAPInvHed.DocInvoiceBal.ToString().Replace("`", "-"));
        }

        // Unposted Balance
        if (modifiedAPInvHed.UnpostedBal != originalAPInvHed.UnpostedBal)
        {
            changes.Add("APInvHed.UnpostedBal`"
                       + originalAPInvHed.UnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.UnpostedBal.ToString().Replace("`", "-"));
        }

        // Document Unposted Balance
        if (modifiedAPInvHed.DocUnpostedBal != originalAPInvHed.DocUnpostedBal)
        {
            changes.Add("APInvHed.DocUnpostedBal`"
                       + originalAPInvHed.DocUnpostedBal.ToString()
                       + "`"
                       + modifiedAPInvHed.DocUnpostedBal.ToString().Replace("`", "-"));
        }

        // Invoice Held
        if (modifiedAPInvHed.InvoiceHeld != originalAPInvHed.InvoiceHeld)
        {
            changes.Add("APInvHed.InvoiceHeld`"
                       + originalAPInvHed.InvoiceHeld.ToString()
                       + "`"
                       + modifiedAPInvHed.InvoiceHeld.ToString().Replace("`", "-"));
        }

        // Pay Hold
        if (modifiedAPInvHed.PayHold != originalAPInvHed.PayHold)
        {
            changes.Add("APInvHed.PayHold`"
                       + originalAPInvHed.PayHold.ToString()
                       + "`"
                       + modifiedAPInvHed.PayHold.ToString().Replace("`", "-"));
        }

        // Description
        if (modifiedAPInvHed.Description != originalAPInvHed.Description)
        {
            changes.Add("APInvHed.Description`"
                       + originalAPInvHed.Description.ToString()
                       + "`"
                       + modifiedAPInvHed.Description.ToString().Replace("`", "-"));
        }

        // Currency Code
        if (modifiedAPInvHed.CurrencyCode != originalAPInvHed.CurrencyCode)
        {
            changes.Add("APInvHed.CurrencyCode`"
                       + originalAPInvHed.CurrencyCode.ToString()
                       + "`"
                       + modifiedAPInvHed.CurrencyCode.ToString().Replace("`", "-"));
        }

        // Exchange Rate
        if (modifiedAPInvHed.ExchangeRate != originalAPInvHed.ExchangeRate)
        {
            changes.Add("APInvHed.ExchangeRate`"
                       + originalAPInvHed.ExchangeRate.ToString()
                       + "`"
                       + modifiedAPInvHed.ExchangeRate.ToString().Replace("`", "-"));
        }

        // Lock Rate
        if (modifiedAPInvHed.LockRate != originalAPInvHed.LockRate)
        {
            changes.Add("APInvHed.LockRate`"
                       + originalAPInvHed.LockRate.ToString()
                       + "`"
                       + modifiedAPInvHed.LockRate.ToString().Replace("`", "-"));
        }