result = "OK";

this.CallService<Ice.Contracts.UD03SvcContract>(svc =>
{
    try
    {
        var companyID = Session.CompanyID.ToString();  // Use the current company ID from the session
        string Key4 = "";
        string Key5 = "";

        // Call GetByID with the appropriate parameters
        var ts = svc.GetByID(Key1, Key2, Key3, Key4, Key5);

        // Check if the row exists
        if (ts.UD03.Any())
        {
            // Get the first row (if multiple rows are returned, you can loop through them)
            var row = ts.UD03[0];

            // Manually set RowMod to "D" to mark it for deletion
            row.RowMod = "D";

            // Save the changes (this will delete the row)
            svc.Update(ref ts);

            result = "Row deleted successfully.";
        }
        else
        {
            result = "Error: No row found with the specified Keys.";
        }
    }
    catch (Exception ex)
    {
        result = "Error: " + ex.Message;
    }
});