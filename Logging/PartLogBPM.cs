// Initialize change tracking variables
changesMade = "";
changesMade2 = "";
changesMade3 = "";
changesMade4 = "";
changesMade5 = "";
changesMade6 = "";
changesMade7 = "";
changesMade8 = "";
changesMade9 = "";
changesMade10 = "";

// Initialize context variables
companyID = callContextClient.CurrentCompany.ToString();
currentSite = callContextClient.CurrentPlant.ToString();
callFunc = false;

// Validate dataset before processing
if (ds.Part == null || ds.Part.Count == 0)
{
    test1 = "Error: No part data found in dataset";
    return;
}

test1 = $"Debug: Dataset has {ds.Part.Count.ToString()} part records";

// Get the modified part record
var modified = ds.Part[0];
partNum = modified.PartNum.ToString();
test2 = "Debug: Successfully accessed ds.Part[0]";

// Check if the row has been deleted (RowMod = "D" in original record)
if (ds.Part[0].RowMod == "D")
{
	changesMade = "Part deleted";
	callFunc = true;
}

// Get the original part record from database
var original = (from dbPart in Db.Part
                where dbPart.Company == companyID
                   && dbPart.PartNum.ToString() == partNum
                select dbPart).FirstOrDefault();

// Handle new part creation
if (original == null)
{
    changesMade = $"New part created: {modified.PartNum.ToString()}";
    callFunc = true;
}
else
{
    // Track all changes in a list
    List<string> changes = new List<string>();

    // Determine which record to use as the modified version
    // Check if there's a second record (modified version)
    if (ds.Part.Count > 1)
    {
        try
        {
            modified = ds.Part[1];
            test2 = "Debug: Successfully accessed ds.Part[1]";
        }
        catch (System.Exception ex)
        {
            test2 = $"Error accessing ds.Part[1]: {ex.Message}";
            return;
        }
    }
    else
    {
        test2 = "Debug: Using ds.Part[0] as modified (only 1 record)";
    }

    // =================================================================
    // Part Field Comparisons
    // =================================================================

    // Company
    if (modified.Company != original.Company)
    {
        changes.Add("Company`"
                   + original.Company.ToString()
                   + "`"
                   + modified.Company.ToString().Replace("`", "-"));
    }

    // Part Number
    if (modified.PartNum != original.PartNum)
    {
        changes.Add("PartNum`"
                   + original.PartNum.ToString()
                   + "`"
                   + modified.PartNum.ToString().Replace("`", "-"));
    }

    // Search Word
    if (modified.SearchWord != original.SearchWord)
    {
        changes.Add("SearchWord`"
                   + original.SearchWord.ToString()
                   + "`"
                   + modified.SearchWord.ToString().Replace("`", "-"));
    }

    // Part Description
    if (modified.PartDescription != original.PartDescription)
    {
        changes.Add("PartDescription`"
                   + original.PartDescription.ToString()
                   + "`"
                   + modified.PartDescription.ToString().Replace("`", "-"));
    }

    // Class ID
    if (modified.ClassID != original.ClassID)
    {
        changes.Add("ClassID`"
                   + original.ClassID.ToString()
                   + "`"
                   + modified.ClassID.ToString().Replace("`", "-"));
    }

    // Inventory Unit of Measure
    if (modified.IUM != original.IUM)
    {
        changes.Add("IUM`"
                   + original.IUM.ToString()
                   + "`"
                   + modified.IUM.ToString().Replace("`", "-"));
    }

    // Purchasing Unit of Measure
    if (modified.PUM != original.PUM)
    {
        changes.Add("PUM`"
                   + original.PUM.ToString()
                   + "`"
                   + modified.PUM.ToString().Replace("`", "-"));
    }

    // Type Code
    if (modified.TypeCode != original.TypeCode)
    {
        changes.Add("TypeCode`"
                   + original.TypeCode.ToString()
                   + "`"
                   + modified.TypeCode.ToString().Replace("`", "-"));
    }

    // Non Stock
    if (modified.NonStock != original.NonStock)
    {
        changes.Add("NonStock`"
                   + original.NonStock.ToString()
                   + "`"
                   + modified.NonStock.ToString().Replace("`", "-"));
    }

    // Purchasing Factor
    if (modified.PurchasingFactor != original.PurchasingFactor)
    {
        changes.Add("PurchasingFactor`"
                   + original.PurchasingFactor.ToString()
                   + "`"
                   + modified.PurchasingFactor.ToString().Replace("`", "-"));
    }

    // Unit Price
    if (modified.UnitPrice != original.UnitPrice)
    {
        changes.Add("UnitPrice`"
                   + original.UnitPrice.ToString()
                   + "`"
                   + modified.UnitPrice.ToString().Replace("`", "-"));
    }

    // Price Per Code
    if (modified.PricePerCode != original.PricePerCode)
    {
        changes.Add("PricePerCode`"
                   + original.PricePerCode.ToString()
                   + "`"
                   + modified.PricePerCode.ToString().Replace("`", "-"));
    }

    // Internal Unit Price
    if (modified.InternalUnitPrice != original.InternalUnitPrice)
    {
        changes.Add("InternalUnitPrice`"
                   + original.InternalUnitPrice.ToString()
                   + "`"
                   + modified.InternalUnitPrice.ToString().Replace("`", "-"));
    }

    // Internal Price Per Code
    if (modified.InternalPricePerCode != original.InternalPricePerCode)
    {
        changes.Add("InternalPricePerCode`"
                   + original.InternalPricePerCode.ToString()
                   + "`"
                   + modified.InternalPricePerCode.ToString().Replace("`", "-"));
    }

    // Product Code
    if (modified.ProdCode != original.ProdCode)
    {
        changes.Add("ProdCode`"
                   + original.ProdCode.ToString()
                   + "`"
                   + modified.ProdCode.ToString().Replace("`", "-"));
    }

    // Manufacturing Comment
    if (modified.MfgComment != original.MfgComment)
    {
        changes.Add("MfgComment`"
                   + original.MfgComment.ToString()
                   + "`"
                   + modified.MfgComment.ToString().Replace("`", "-"));
    }

    // Purchasing Comment
    if (modified.PurComment != original.PurComment)
    {
        changes.Add("PurComment`"
                   + original.PurComment.ToString()
                   + "`"
                   + modified.PurComment.ToString().Replace("`", "-"));
    }

    // Cost Method
    if (modified.CostMethod != original.CostMethod)
    {
        changes.Add("CostMethod`"
                   + original.CostMethod.ToString()
                   + "`"
                   + modified.CostMethod.ToString().Replace("`", "-"));
    }

    // User Character 1
    if (modified.UserChar1 != original.UserChar1)
    {
        changes.Add("UserChar1`"
                   + original.UserChar1.ToString()
                   + "`"
                   + modified.UserChar1.ToString().Replace("`", "-"));
    }

    // User Character 2
    if (modified.UserChar2 != original.UserChar2)
    {
        changes.Add("UserChar2`"
                   + original.UserChar2.ToString()
                   + "`"
                   + modified.UserChar2.ToString().Replace("`", "-"));
    }

    // User Character 3
    if (modified.UserChar3 != original.UserChar3)
    {
        changes.Add("UserChar3`"
                   + original.UserChar3.ToString()
                   + "`"
                   + modified.UserChar3.ToString().Replace("`", "-"));
    }

    // User Character 4
    if (modified.UserChar4 != original.UserChar4)
    {
        changes.Add("UserChar4`"
                   + original.UserChar4.ToString()
                   + "`"
                   + modified.UserChar4.ToString().Replace("`", "-"));
    }

    // User Date 1
    if (modified.UserDate1 != original.UserDate1)
    {
        changes.Add("UserDate1`"
                   + original.UserDate1.ToString()
                   + "`"
                   + modified.UserDate1.ToString().Replace("`", "-"));
    }

    // User Date 2
    if (modified.UserDate2 != original.UserDate2)
    {
        changes.Add("UserDate2`"
                   + original.UserDate2.ToString()
                   + "`"
                   + modified.UserDate2.ToString().Replace("`", "-"));
    }

    // User Date 3
    if (modified.UserDate3 != original.UserDate3)
    {
        changes.Add("UserDate3`"
                   + original.UserDate3.ToString()
                   + "`"
                   + modified.UserDate3.ToString().Replace("`", "-"));
    }

    // User Date 4
    if (modified.UserDate4 != original.UserDate4)
    {
        changes.Add("UserDate4`"
                   + original.UserDate4.ToString()
                   + "`"
                   + modified.UserDate4.ToString().Replace("`", "-"));
    }

    // User Decimal 1
    if (modified.UserDecimal1 != original.UserDecimal1)
    {
        changes.Add("UserDecimal1`"
                   + original.UserDecimal1.ToString()
                   + "`"
                   + modified.UserDecimal1.ToString().Replace("`", "-"));
    }

    // User Decimal 2
    if (modified.UserDecimal2 != original.UserDecimal2)
    {
        changes.Add("UserDecimal2`"
                   + original.UserDecimal2.ToString()
                   + "`"
                   + modified.UserDecimal2.ToString().Replace("`", "-"));
    }

    // User Decimal 3
    if (modified.UserDecimal3 != original.UserDecimal3)
    {
        changes.Add("UserDecimal3`"
                   + original.UserDecimal3.ToString()
                   + "`"
                   + modified.UserDecimal3.ToString().Replace("`", "-"));
    }

    // User Decimal 4
    if (modified.UserDecimal4 != original.UserDecimal4)
    {
        changes.Add("UserDecimal4`"
                   + original.UserDecimal4.ToString()
                   + "`"
                   + modified.UserDecimal4.ToString().Replace("`", "-"));
    }

    // User Integer 1
    if (modified.UserInteger1 != original.UserInteger1)
    {
        changes.Add("UserInteger1`"
                   + original.UserInteger1.ToString()
                   + "`"
                   + modified.UserInteger1.ToString().Replace("`", "-"));
    }

    // User Integer 2
    if (modified.UserInteger2 != original.UserInteger2)
    {
        changes.Add("UserInteger2`"
                   + original.UserInteger2.ToString()
                   + "`"
                   + modified.UserInteger2.ToString().Replace("`", "-"));
    }

    // Tax Category ID
    if (modified.TaxCatID != original.TaxCatID)
    {
        changes.Add("TaxCatID`"
                   + original.TaxCatID.ToString()
                   + "`"
                   + modified.TaxCatID.ToString().Replace("`", "-"));
    }

    // Inactive
    if (modified.InActive != original.InActive)
    {
        changes.Add("InActive`"
                   + original.InActive.ToString()
                   + "`"
                   + modified.InActive.ToString().Replace("`", "-"));
    }

    // Low Level Code
    if (modified.LowLevelCode != original.LowLevelCode)
    {
        changes.Add("LowLevelCode`"
                   + original.LowLevelCode.ToString()
                   + "`"
                   + modified.LowLevelCode.ToString().Replace("`", "-"));
    }

    // Method
    if (modified.Method != original.Method)
    {
        changes.Add("Method`"
                   + original.Method.ToString()
                   + "`"
                   + modified.Method.ToString().Replace("`", "-"));
    }

    // Track Lots
    if (modified.TrackLots != original.TrackLots)
    {
        changes.Add("TrackLots`"
                   + original.TrackLots.ToString()
                   + "`"
                   + modified.TrackLots.ToString().Replace("`", "-"));
    }

    // Track Dimension
    if (modified.TrackDimension != original.TrackDimension)
    {
        changes.Add("TrackDimension`"
                   + original.TrackDimension.ToString()
                   + "`"
                   + modified.TrackDimension.ToString().Replace("`", "-"));
    }

    // Default Dimension
    if (modified.DefaultDim != original.DefaultDim)
    {
        changes.Add("DefaultDim`"
                   + original.DefaultDim.ToString()
                   + "`"
                   + modified.DefaultDim.ToString().Replace("`", "-"));
    }

    // Track Serial Number
    if (modified.TrackSerialNum != original.TrackSerialNum)
    {
        changes.Add("TrackSerialNum`"
                   + original.TrackSerialNum.ToString()
                   + "`"
                   + modified.TrackSerialNum.ToString().Replace("`", "-"));
    }

    // Commodity Code
    if (modified.CommodityCode != original.CommodityCode)
    {
        changes.Add("CommodityCode`"
                   + original.CommodityCode.ToString()
                   + "`"
                   + modified.CommodityCode.ToString().Replace("`", "-"));
    }

    // Warranty Code
    if (modified.WarrantyCode != original.WarrantyCode)
    {
        changes.Add("WarrantyCode`"
                   + original.WarrantyCode.ToString()
                   + "`"
                   + modified.WarrantyCode.ToString().Replace("`", "-"));
    }

    // Phantom BOM
    if (modified.PhantomBOM != original.PhantomBOM)
    {
        changes.Add("PhantomBOM`"
                   + original.PhantomBOM.ToString()
                   + "`"
                   + modified.PhantomBOM.ToString().Replace("`", "-"));
    }

    // Sales Unit of Measure
    if (modified.SalesUM != original.SalesUM)
    {
        changes.Add("SalesUM`"
                   + original.SalesUM.ToString()
                   + "`"
                   + modified.SalesUM.ToString().Replace("`", "-"));
    }

    // Selling Factor
    if (modified.SellingFactor != original.SellingFactor)
    {
        changes.Add("SellingFactor`"
                   + original.SellingFactor.ToString()
                   + "`"
                   + modified.SellingFactor.ToString().Replace("`", "-"));
    }

    // Material Burden Rate
    if (modified.MtlBurRate != original.MtlBurRate)
    {
        changes.Add("MtlBurRate`"
                   + original.MtlBurRate.ToString()
                   + "`"
                   + modified.MtlBurRate.ToString().Replace("`", "-"));
    }

    // Net Weight
    if (modified.NetWeight != original.NetWeight)
    {
        changes.Add("NetWeight`"
                   + original.NetWeight.ToString()
                   + "`"
                   + modified.NetWeight.ToString().Replace("`", "-"));
    }

    // Use Part Revision
    if (modified.UsePartRev != original.UsePartRev)
    {
        changes.Add("UsePartRev`"
                   + original.UsePartRev.ToString()
                   + "`"
                   + modified.UsePartRev.ToString().Replace("`", "-"));
    }

    // Parts Per Container
    if (modified.PartsPerContainer != original.PartsPerContainer)
    {
        changes.Add("PartsPerContainer`"
                   + original.PartsPerContainer.ToString()
                   + "`"
                   + modified.PartsPerContainer.ToString().Replace("`", "-"));
    }

    // Part Length
    if (modified.PartLength != original.PartLength)
    {
        changes.Add("PartLength`"
                   + original.PartLength.ToString()
                   + "`"
                   + modified.PartLength.ToString().Replace("`", "-"));
    }

    // Part Width
    if (modified.PartWidth != original.PartWidth)
    {
        changes.Add("PartWidth`"
                   + original.PartWidth.ToString()
                   + "`"
                   + modified.PartWidth.ToString().Replace("`", "-"));
    }

    // Part Height
    if (modified.PartHeight != original.PartHeight)
    {
        changes.Add("PartHeight`"
                   + original.PartHeight.ToString()
                   + "`"
                   + modified.PartHeight.ToString().Replace("`", "-"));
    }

    // Lot Shelf Life
    if (modified.LotShelfLife != original.LotShelfLife)
    {
        changes.Add("LotShelfLife`"
                   + original.LotShelfLife.ToString()
                   + "`"
                   + modified.LotShelfLife.ToString().Replace("`", "-"));
    }

    // Web Part
    if (modified.WebPart != original.WebPart)
    {
        changes.Add("WebPart`"
                   + original.WebPart.ToString()
                   + "`"
                   + modified.WebPart.ToString().Replace("`", "-"));
    }

    // Run Out
    if (modified.RunOut != original.RunOut)
    {
        changes.Add("RunOut`"
                   + original.RunOut.ToString()
                   + "`"
                   + modified.RunOut.ToString().Replace("`", "-"));
    }

    // Sub Part
    if (modified.SubPart != original.SubPart)
    {
        changes.Add("SubPart`"
                   + original.SubPart.ToString()
                   + "`"
                   + modified.SubPart.ToString().Replace("`", "-"));
    }

    // Diameter
    if (modified.Diameter != original.Diameter)
    {
        changes.Add("Diameter`"
                   + original.Diameter.ToString()
                   + "`"
                   + modified.Diameter.ToString().Replace("`", "-"));
    }

    // Gravity
    if (modified.Gravity != original.Gravity)
    {
        changes.Add("Gravity`"
                   + original.Gravity.ToString()
                   + "`"
                   + modified.Gravity.ToString().Replace("`", "-"));
    }

    // On Hold
    if (modified.OnHold != original.OnHold)
    {
        changes.Add("OnHold`"
                   + original.OnHold.ToString()
                   + "`"
                   + modified.OnHold.ToString().Replace("`", "-"));
    }

    // On Hold Date
    if (modified.OnHoldDate != original.OnHoldDate)
    {
        changes.Add("OnHoldDate`"
                   + original.OnHoldDate.ToString()
                   + "`"
                   + modified.OnHoldDate.ToString().Replace("`", "-"));
    }

    // On Hold Reason Code
    if (modified.OnHoldReasonCode != original.OnHoldReasonCode)
    {
        changes.Add("OnHoldReasonCode`"
                   + original.OnHoldReasonCode.ToString()
                   + "`"
                   + modified.OnHoldReasonCode.ToString().Replace("`", "-"));
    }

    // Analysis Code
    if (modified.AnalysisCode != original.AnalysisCode)
    {
        changes.Add("AnalysisCode`"
                   + original.AnalysisCode.ToString()
                   + "`"
                   + modified.AnalysisCode.ToString().Replace("`", "-"));
    }

    // Global Part
    if (modified.GlobalPart != original.GlobalPart)
    {
        changes.Add("GlobalPart`"
                   + original.GlobalPart.ToString()
                   + "`"
                   + modified.GlobalPart.ToString().Replace("`", "-"));
    }

    // Material Analysis Code
    if (modified.MtlAnalysisCode != original.MtlAnalysisCode)
    {
        changes.Add("MtlAnalysisCode`"
                   + original.MtlAnalysisCode.ToString()
                   + "`"
                   + modified.MtlAnalysisCode.ToString().Replace("`", "-"));
    }

    // Global Lock
    if (modified.GlobalLock != original.GlobalLock)
    {
        changes.Add("GlobalLock`"
                   + original.GlobalLock.ToString()
                   + "`"
                   + modified.GlobalLock.ToString().Replace("`", "-"));
    }

    // IS Supplemental Units Factor
    if (modified.ISSuppUnitsFactor != original.ISSuppUnitsFactor)
    {
        changes.Add("ISSuppUnitsFactor`"
                   + original.ISSuppUnitsFactor.ToString()
                   + "`"
                   + modified.ISSuppUnitsFactor.ToString().Replace("`", "-"));
    }

    // PDM Object ID
    if (modified.PDMObjID != original.PDMObjID)
    {
        changes.Add("PDMObjID`"
                   + original.PDMObjID.ToString()
                   + "`"
                   + modified.PDMObjID.ToString().Replace("`", "-"));
    }

    // Image File Name
    if (modified.ImageFileName != original.ImageFileName)
    {
        changes.Add("ImageFileName`"
                   + original.ImageFileName.ToString()
                   + "`"
                   + modified.ImageFileName.ToString().Replace("`", "-"));
    }

    // IS Original Country
    if (modified.ISOrigCountry != original.ISOrigCountry)
    {
        changes.Add("ISOrigCountry`"
                   + original.ISOrigCountry.ToString()
                   + "`"
                   + modified.ISOrigCountry.ToString().Replace("`", "-"));
    }

    // Serial Number Prefix
    if (modified.SNPrefix != original.SNPrefix)
    {
        changes.Add("SNPrefix`"
                   + original.SNPrefix.ToString()
                   + "`"
                   + modified.SNPrefix.ToString().Replace("`", "-"));
    }

    // Serial Number Format
    if (modified.SNFormat != original.SNFormat)
    {
        changes.Add("SNFormat`"
                   + original.SNFormat.ToString()
                   + "`"
                   + modified.SNFormat.ToString().Replace("`", "-"));
    }

    // Serial Number Base Data Type
    if (modified.SNBaseDataType != original.SNBaseDataType)
    {
        changes.Add("SNBaseDataType`"
                   + original.SNBaseDataType.ToString()
                   + "`"
                   + modified.SNBaseDataType.ToString().Replace("`", "-"));
    }

    // Constrained
    if (modified.Constrained != original.Constrained)
    {
        changes.Add("Constrained`"
                   + original.Constrained.ToString()
                   + "`"
                   + modified.Constrained.ToString().Replace("`", "-"));
    }

    // UPC Code 1
    if (modified.UPCCode1 != original.UPCCode1)
    {
        changes.Add("UPCCode1`"
                   + original.UPCCode1.ToString()
                   + "`"
                   + modified.UPCCode1.ToString().Replace("`", "-"));
    }

    // UPC Code 2
    if (modified.UPCCode2 != original.UPCCode2)
    {
        changes.Add("UPCCode2`"
                   + original.UPCCode2.ToString()
                   + "`"
                   + modified.UPCCode2.ToString().Replace("`", "-"));
    }

    // UPC Code 3
    if (modified.UPCCode3 != original.UPCCode3)
    {
        changes.Add("UPCCode3`"
                   + original.UPCCode3.ToString()
                   + "`"
                   + modified.UPCCode3.ToString().Replace("`", "-"));
    }

    // EDI Code
    if (modified.EDICode != original.EDICode)
    {
        changes.Add("EDICode`"
                   + original.EDICode.ToString()
                   + "`"
                   + modified.EDICode.ToString().Replace("`", "-"));
    }

    // Web In Stock
    if (modified.WebInStock != original.WebInStock)
    {
        changes.Add("WebInStock`"
                   + original.WebInStock.ToString()
                   + "`"
                   + modified.WebInStock.ToString().Replace("`", "-"));
    }

    // Consolidated Purchasing
    if (modified.ConsolidatedPurchasing != original.ConsolidatedPurchasing)
    {
        changes.Add("ConsolidatedPurchasing`"
                   + original.ConsolidatedPurchasing.ToString()
                   + "`"
                   + modified.ConsolidatedPurchasing.ToString().Replace("`", "-"));
    }

    // Purchasing Factor Direction
    if (modified.PurchasingFactorDirection != original.PurchasingFactorDirection)
    {
        changes.Add("PurchasingFactorDirection`"
                   + original.PurchasingFactorDirection.ToString()
                   + "`"
                   + modified.PurchasingFactorDirection.ToString().Replace("`", "-"));
    }

    // Selling Factor Direction
    if (modified.SellingFactorDirection != original.SellingFactorDirection)
    {
        changes.Add("SellingFactorDirection`"
                   + original.SellingFactorDirection.ToString()
                   + "`"
                   + modified.SellingFactorDirection.ToString().Replace("`", "-"));
    }

    // Receipt Document Required
    if (modified.RecDocReq != original.RecDocReq)
    {
        changes.Add("RecDocReq`"
                   + original.RecDocReq.ToString()
                   + "`"
                   + modified.RecDocReq.ToString().Replace("`", "-"));
    }

    // MDPV
    if (modified.MDPV != original.MDPV)
    {
        changes.Add("MDPV`"
                   + original.MDPV.ToString()
                   + "`"
                   + modified.MDPV.ToString().Replace("`", "-"));
    }

    // Ship Document Required
    if (modified.ShipDocReq != original.ShipDocReq)
    {
        changes.Add("ShipDocReq`"
                   + original.ShipDocReq.ToString()
                   + "`"
                   + modified.ShipDocReq.ToString().Replace("`", "-"));
    }

    // Returnable Container
    if (modified.ReturnableContainer != original.ReturnableContainer)
    {
        changes.Add("ReturnableContainer`"
                   + original.ReturnableContainer.ToString()
                   + "`"
                   + modified.ReturnableContainer.ToString().Replace("`", "-"));
    }

    // Net Volume
    if (modified.NetVolume != original.NetVolume)
    {
        changes.Add("NetVolume`"
                   + original.NetVolume.ToString()
                   + "`"
                   + modified.NetVolume.ToString().Replace("`", "-"));
    }

    // Quantity Bearing
    if (modified.QtyBearing != original.QtyBearing)
    {
        changes.Add("QtyBearing`"
                   + original.QtyBearing.ToString()
                   + "`"
                   + modified.QtyBearing.ToString().Replace("`", "-"));
    }

    // NAFTA Original Country
    if (modified.NAFTAOrigCountry != original.NAFTAOrigCountry)
    {
        changes.Add("NAFTAOrigCountry`"
                   + original.NAFTAOrigCountry.ToString()
                   + "`"
                   + modified.NAFTAOrigCountry.ToString().Replace("`", "-"));
    }

    // NAFTA Producer
    if (modified.NAFTAProd != original.NAFTAProd)
    {
        changes.Add("NAFTAProd`"
                   + original.NAFTAProd.ToString()
                   + "`"
                   + modified.NAFTAProd.ToString().Replace("`", "-"));
    }

    // NAFTA Preference
    if (modified.NAFTAPref != original.NAFTAPref)
    {
        changes.Add("NAFTAPref`"
                   + original.NAFTAPref.ToString()
                   + "`"
                   + modified.NAFTAPref.ToString().Replace("`", "-"));
    }

    // Export License Type
    if (modified.ExpLicType != original.ExpLicType)
    {
        changes.Add("ExpLicType`"
                   + original.ExpLicType.ToString()
                   + "`"
                   + modified.ExpLicType.ToString().Replace("`", "-"));
    }

    // Export License Number
    if (modified.ExpLicNumber != original.ExpLicNumber)
    {
        changes.Add("ExpLicNumber`"
                   + original.ExpLicNumber.ToString()
                   + "`"
                   + modified.ExpLicNumber.ToString().Replace("`", "-"));
    }

    // ECCN Number
    if (modified.ECCNNumber != original.ECCNNumber)
    {
        changes.Add("ECCNNumber`"
                   + original.ECCNNumber.ToString()
                   + "`"
                   + modified.ECCNNumber.ToString().Replace("`", "-"));
    }

    // AES Export
    if (modified.AESExp != original.AESExp)
    {
        changes.Add("AESExp`"
                   + original.AESExp.ToString()
                   + "`"
                   + modified.AESExp.ToString().Replace("`", "-"));
    }

    // HTS
    if (modified.HTS != original.HTS)
    {
        changes.Add("HTS`"
                   + original.HTS.ToString()
                   + "`"
                   + modified.HTS.ToString().Replace("`", "-"));
    }

    // Use HTS Description
    if (modified.UseHTSDesc != original.UseHTSDesc)
    {
        changes.Add("UseHTSDesc`"
                   + original.UseHTSDesc.ToString()
                   + "`"
                   + modified.UseHTSDesc.ToString().Replace("`", "-"));
    }

    // Schedule B Code
    if (modified.SchedBcode != original.SchedBcode)
    {
        changes.Add("SchedBcode`"
                   + original.SchedBcode.ToString()
                   + "`"
                   + modified.SchedBcode.ToString().Replace("`", "-"));
    }

    // Hazardous Item
    if (modified.HazItem != original.HazItem)
    {
        changes.Add("HazItem`"
                   + original.HazItem.ToString()
                   + "`"
                   + modified.HazItem.ToString().Replace("`", "-"));
    }

    // Hazardous Technical Name
    if (modified.HazTechName != original.HazTechName)
    {
        changes.Add("HazTechName`"
                   + original.HazTechName.ToString()
                   + "`"
                   + modified.HazTechName.ToString().Replace("`", "-"));
    }

    // Hazardous Class
    if (modified.HazClass != original.HazClass)
    {
        changes.Add("HazClass`"
                   + original.HazClass.ToString()
                   + "`"
                   + modified.HazClass.ToString().Replace("`", "-"));
    }

    // Hazardous Sub
    if (modified.HazSub != original.HazSub)
    {
        changes.Add("HazSub`"
                   + original.HazSub.ToString()
                   + "`"
                   + modified.HazSub.ToString().Replace("`", "-"));
    }

    // Hazardous Government ID
    if (modified.HazGvrnmtID != original.HazGvrnmtID)
    {
        changes.Add("HazGvrnmtID`"
                   + original.HazGvrnmtID.ToString()
                   + "`"
                   + modified.HazGvrnmtID.ToString().Replace("`", "-"));
    }

    // Hazardous Pack Instructions
    if (modified.HazPackInstr != original.HazPackInstr)
    {
        changes.Add("HazPackInstr`"
                   + original.HazPackInstr.ToString()
                   + "`"
                   + modified.HazPackInstr.ToString().Replace("`", "-"));
    }

    // Reverse Charge Method
    if (modified.RevChargeMethod != original.RevChargeMethod)
    {
        changes.Add("RevChargeMethod`"
                   + original.RevChargeMethod.ToString()
                   + "`"
                   + modified.RevChargeMethod.ToString().Replace("`", "-"));
    }

    // RC Under Threshold
    if (modified.RCUnderThreshold != original.RCUnderThreshold)
    {
        changes.Add("RCUnderThreshold`"
                   + original.RCUnderThreshold.ToString()
                   + "`"
                   + modified.RCUnderThreshold.ToString().Replace("`", "-"));
    }

    // RC Over Threshold
    if (modified.RCOverThreshold != original.RCOverThreshold)
    {
        changes.Add("RCOverThreshold`"
                   + original.RCOverThreshold.ToString()
                   + "`"
                   + modified.RCOverThreshold.ToString().Replace("`", "-"));
    }

    // Ownership Status
    if (modified.OwnershipStatus != original.OwnershipStatus)
    {
        changes.Add("OwnershipStatus`"
                   + original.OwnershipStatus.ToString()
                   + "`"
                   + modified.OwnershipStatus.ToString().Replace("`", "-"));
    }

    // UOM Class ID
    if (modified.UOMClassID != original.UOMClassID)
    {
        changes.Add("UOMClassID`"
                   + original.UOMClassID.ToString()
                   + "`"
                   + modified.UOMClassID.ToString().Replace("`", "-"));
    }

    // Serial Number Mask
    if (modified.SNMask != original.SNMask)
    {
        changes.Add("SNMask`"
                   + original.SNMask.ToString()
                   + "`"
                   + modified.SNMask.ToString().Replace("`", "-"));
    }

    // Serial Number Mask Example
    if (modified.SNMaskExample != original.SNMaskExample)
    {
        changes.Add("SNMaskExample`"
                   + original.SNMaskExample.ToString()
                   + "`"
                   + modified.SNMaskExample.ToString().Replace("`", "-"));
    }

    // Serial Number Mask Suffix
    if (modified.SNMaskSuffix != original.SNMaskSuffix)
    {
        changes.Add("SNMaskSuffix`"
                   + original.SNMaskSuffix.ToString()
                   + "`"
                   + modified.SNMaskSuffix.ToString().Replace("`", "-"));
    }

    // Serial Number Mask Prefix
    if (modified.SNMaskPrefix != original.SNMaskPrefix)
    {
        changes.Add("SNMaskPrefix`"
                   + original.SNMaskPrefix.ToString()
                   + "`"
                   + modified.SNMaskPrefix.ToString().Replace("`", "-"));
    }

    // Serial Number Last Used Sequence
    if (modified.SNLastUsedSeq != original.SNLastUsedSeq)
    {
        changes.Add("SNLastUsedSeq`"
                   + original.SNLastUsedSeq.ToString()
                   + "`"
                   + modified.SNLastUsedSeq.ToString().Replace("`", "-"));
    }

    // Use Mask Sequence
    if (modified.UseMaskSeq != original.UseMaskSeq)
    {
        changes.Add("UseMaskSeq`"
                   + original.UseMaskSeq.ToString()
                   + "`"
                   + modified.UseMaskSeq.ToString().Replace("`", "-"));
    }

    // Net Weight UOM
    if (modified.NetWeightUOM != original.NetWeightUOM)
    {
        changes.Add("NetWeightUOM`"
                   + original.NetWeightUOM.ToString()
                   + "`"
                   + modified.NetWeightUOM.ToString().Replace("`", "-"));
    }

    // Net Volume UOM
    if (modified.NetVolumeUOM != original.NetVolumeUOM)
    {
        changes.Add("NetVolumeUOM`"
                   + original.NetVolumeUOM.ToString()
                   + "`"
                   + modified.NetVolumeUOM.ToString().Replace("`", "-"));
    }

    // Lot Batch
    if (modified.LotBatch != original.LotBatch)
    {
        changes.Add("LotBatch`"
                   + original.LotBatch.ToString()
                   + "`"
                   + modified.LotBatch.ToString().Replace("`", "-"));
    }

    // Lot Manufacturing Batch
    if (modified.LotMfgBatch != original.LotMfgBatch)
    {
        changes.Add("LotMfgBatch`"
                   + original.LotMfgBatch.ToString()
                   + "`"
                   + modified.LotMfgBatch.ToString().Replace("`", "-"));
    }

    // Lot Manufacturing Lot
    if (modified.LotMfgLot != original.LotMfgLot)
    {
        changes.Add("LotMfgLot`"
                   + original.LotMfgLot.ToString()
                   + "`"
                   + modified.LotMfgLot.ToString().Replace("`", "-"));
    }

    // Lot Heat
    if (modified.LotHeat != original.LotHeat)
    {
        changes.Add("LotHeat`"
                   + original.LotHeat.ToString()
                   + "`"
                   + modified.LotHeat.ToString().Replace("`", "-"));
    }

    // Lot Firmware
    if (modified.LotFirmware != original.LotFirmware)
    {
        changes.Add("LotFirmware`"
                   + original.LotFirmware.ToString()
                   + "`"
                   + modified.LotFirmware.ToString().Replace("`", "-"));
    }

    // Lot Before Date
    if (modified.LotBeforeDt != original.LotBeforeDt)
    {
        changes.Add("LotBeforeDt`"
                   + original.LotBeforeDt.ToString()
                   + "`"
                   + modified.LotBeforeDt.ToString().Replace("`", "-"));
    }

    // Lot Manufacturing Date
    if (modified.LotMfgDt != original.LotMfgDt)
    {
        changes.Add("LotMfgDt`"
                   + original.LotMfgDt.ToString()
                   + "`"
                   + modified.LotMfgDt.ToString().Replace("`", "-"));
    }

    // Lot Cure Date
    if (modified.LotCureDt != original.LotCureDt)
    {
        changes.Add("LotCureDt`"
                   + original.LotCureDt.ToString()
                   + "`"
                   + modified.LotCureDt.ToString().Replace("`", "-"));
    }

    // Lot Expiration Date
    if (modified.LotExpDt != original.LotExpDt)
    {
        changes.Add("LotExpDt`"
                   + original.LotExpDt.ToString()
                   + "`"
                   + modified.LotExpDt.ToString().Replace("`", "-"));
    }

    // Lot Prefix
    if (modified.LotPrefix != original.LotPrefix)
    {
        changes.Add("LotPrefix`"
                   + original.LotPrefix.ToString()
                   + "`"
                   + modified.LotPrefix.ToString().Replace("`", "-"));
    }

    // Lot Use Global Sequence
    if (modified.LotUseGlobalSeq != original.LotUseGlobalSeq)
    {
        changes.Add("LotUseGlobalSeq`"
                   + original.LotUseGlobalSeq.ToString()
                   + "`"
                   + modified.LotUseGlobalSeq.ToString().Replace("`", "-"));
    }

    // Lot Sequence ID
    if (modified.LotSeqID != original.LotSeqID)
    {
        changes.Add("LotSeqID`"
                   + original.LotSeqID.ToString()
                   + "`"
                   + modified.LotSeqID.ToString().Replace("`", "-"));
    }

    // Lot Next Number
    if (modified.LotNxtNum != original.LotNxtNum)
    {
        changes.Add("LotNxtNum`"
                   + original.LotNxtNum.ToString()
                   + "`"
                   + modified.LotNxtNum.ToString().Replace("`", "-"));
    }

    // Lot Digits
    if (modified.LotDigits != original.LotDigits)
    {
        changes.Add("LotDigits`"
                   + original.LotDigits.ToString()
                   + "`"
                   + modified.LotDigits.ToString().Replace("`", "-"));
    }

    // Lot Leading Zeros
    if (modified.LotLeadingZeros != original.LotLeadingZeros)
    {
        changes.Add("LotLeadingZeros`"
                   + original.LotLeadingZeros.ToString()
                   + "`"
                   + modified.LotLeadingZeros.ToString().Replace("`", "-"));
    }

    // Lot Append Date
    if (modified.LotAppendDate != original.LotAppendDate)
    {
        changes.Add("LotAppendDate`"
                   + original.LotAppendDate.ToString()
                   + "`"
                   + modified.LotAppendDate.ToString().Replace("`", "-"));
    }

    // Buy To Order
    if (modified.BuyToOrder != original.BuyToOrder)
    {
        changes.Add("BuyToOrder`"
                   + original.BuyToOrder.ToString()
                   + "`"
                   + modified.BuyToOrder.ToString().Replace("`", "-"));
    }

    // Drop Ship
    if (modified.DropShip != original.DropShip)
    {
        changes.Add("DropShip`"
                   + original.DropShip.ToString()
                   + "`"
                   + modified.DropShip.ToString().Replace("`", "-"));
    }

    // Is Configured
    if (modified.IsConfigured != original.IsConfigured)
    {
        changes.Add("IsConfigured`"
                   + original.IsConfigured.ToString()
                   + "`"
                   + modified.IsConfigured.ToString().Replace("`", "-"));
    }

    // External Configuration
    if (modified.ExtConfig != original.ExtConfig)
    {
        changes.Add("ExtConfig`"
                   + original.ExtConfig.ToString()
                   + "`"
                   + modified.ExtConfig.ToString().Replace("`", "-"));
    }

    // Reference Category
    if (modified.RefCategory != original.RefCategory)
    {
        changes.Add("RefCategory`"
                   + original.RefCategory.ToString()
                   + "`"
                   + modified.RefCategory.ToString().Replace("`", "-"));
    }

    // CSFCJ5
    if (modified.CSFCJ5 != original.CSFCJ5)
    {
        changes.Add("CSFCJ5`"
                   + original.CSFCJ5.ToString()
                   + "`"
                   + modified.CSFCJ5.ToString().Replace("`", "-"));
    }

    // CSFLMW
    if (modified.CSFLMW != original.CSFLMW)
    {
        changes.Add("CSFLMW`"
                   + original.CSFLMW.ToString()
                   + "`"
                   + modified.CSFLMW.ToString().Replace("`", "-"));
    }

    // Gross Weight
    if (modified.GrossWeight != original.GrossWeight)
    {
        changes.Add("GrossWeight`"
                   + original.GrossWeight.ToString()
                   + "`"
                   + modified.GrossWeight.ToString().Replace("`", "-"));
    }

    // Gross Weight UOM
    if (modified.GrossWeightUOM != original.GrossWeightUOM)
    {
        changes.Add("GrossWeightUOM`"
                   + original.GrossWeightUOM.ToString()
                   + "`"
                   + modified.GrossWeightUOM.ToString().Replace("`", "-"));
    }

    // Base Part Number
    if (modified.BasePartNum != original.BasePartNum)
    {
        changes.Add("BasePartNum`"
                   + original.BasePartNum.ToString()
                   + "`"
                   + modified.BasePartNum.ToString().Replace("`", "-"));
    }

    // FS Asset Class Code
    if (modified.FSAssetClassCode != original.FSAssetClassCode)
    {
        changes.Add("FSAssetClassCode`"
                   + original.FSAssetClassCode.ToString()
                   + "`"
                   + modified.FSAssetClassCode.ToString().Replace("`", "-"));
    }

    // FS Sales Unit Price
    if (modified.FSSalesUnitPrice != original.FSSalesUnitPrice)
    {
        changes.Add("FSSalesUnitPrice`"
                   + original.FSSalesUnitPrice.ToString()
                   + "`"
                   + modified.FSSalesUnitPrice.ToString().Replace("`", "-"));
    }

    // FS Price Per Code
    if (modified.FSPricePerCode != original.FSPricePerCode)
    {
        changes.Add("FSPricePerCode`"
                   + original.FSPricePerCode.ToString()
                   + "`"
                   + modified.FSPricePerCode.ToString().Replace("`", "-"));
    }

    // Receive Inspection Required
    if (modified.RcvInspectionReq != original.RcvInspectionReq)
    {
        changes.Add("RcvInspectionReq`"
                   + original.RcvInspectionReq.ToString()
                   + "`"
                   + modified.RcvInspectionReq.ToString().Replace("`", "-"));
    }

    // Estimate ID
    if (modified.EstimateID != original.EstimateID)
    {
        changes.Add("EstimateID`"
                   + original.EstimateID.ToString()
                   + "`"
                   + modified.EstimateID.ToString().Replace("`", "-"));
    }

    // Estimate or Plan
    if (modified.EstimateOrPlan != original.EstimateOrPlan)
    {
        changes.Add("EstimateOrPlan`"
                   + original.EstimateOrPlan.ToString()
                   + "`"
                   + modified.EstimateOrPlan.ToString().Replace("`", "-"));
    }

    // Different Price to Purchase UOM
    if (modified.DiffPrc2PrchUOM != original.DiffPrc2PrchUOM)
    {
        changes.Add("DiffPrc2PrchUOM`"
                   + original.DiffPrc2PrchUOM.ToString()
                   + "`"
                   + modified.DiffPrc2PrchUOM.ToString().Replace("`", "-"));
    }

    // Duplicate on Job Create
    if (modified.DupOnJobCrt != original.DupOnJobCrt)
    {
        changes.Add("DupOnJobCrt`"
                   + original.DupOnJobCrt.ToString()
                   + "`"
                   + modified.DupOnJobCrt.ToString().Replace("`", "-"));
    }

    // Pricing Factor
    if (modified.PricingFactor != original.PricingFactor)
    {
        changes.Add("PricingFactor`"
                   + original.PricingFactor.ToString()
                   + "`"
                   + modified.PricingFactor.ToString().Replace("`", "-"));
    }

    // Pricing UOM
    if (modified.PricingUOM != original.PricingUOM)
    {
        changes.Add("PricingUOM`"
                   + original.PricingUOM.ToString()
                   + "`"
                   + modified.PricingUOM.ToString().Replace("`", "-"));
    }

    // Mobile Part
    if (modified.MobilePart != original.MobilePart)
    {
        changes.Add("MobilePart`"
                   + original.MobilePart.ToString()
                   + "`"
                   + modified.MobilePart.ToString().Replace("`", "-"));
    }

    // AG Use Good Mark
    if (modified.AGUseGoodMark != original.AGUseGoodMark)
    {
        changes.Add("AGUseGoodMark`"
                   + original.AGUseGoodMark.ToString()
                   + "`"
                   + modified.AGUseGoodMark.ToString().Replace("`", "-"));
    }

    // AG Product Mark
    if (modified.AGProductMark != original.AGProductMark)
    {
        changes.Add("AGProductMark`"
                   + original.AGProductMark.ToString()
                   + "`"
                   + modified.AGProductMark.ToString().Replace("`", "-"));
    }

    // IS Region
    if (modified.ISRegion != original.ISRegion)
    {
        changes.Add("ISRegion`"
                   + original.ISRegion.ToString()
                   + "`"
                   + modified.ISRegion.ToString().Replace("`", "-"));
    }

    // IN Chapter ID
    if (modified.INChapterID != original.INChapterID)
    {
        changes.Add("INChapterID`"
                   + original.INChapterID.ToString()
                   + "`"
                   + modified.INChapterID.ToString().Replace("`", "-"));
    }

    // PE SUNAT Type
    if (modified.PESUNATType != original.PESUNATType)
    {
        changes.Add("PESUNATType`"
                   + original.PESUNATType.ToString()
                   + "`"
                   + modified.PESUNATType.ToString().Replace("`", "-"));
    }

    // DE Is Services
    if (modified.DEIsServices != original.DEIsServices)
    {
        changes.Add("DEIsServices`"
                   + original.DEIsServices.ToString()
                   + "`"
                   + modified.DEIsServices.ToString().Replace("`", "-"));
    }

    // DE Is Security Financial Derivative
    if (modified.DEIsSecurityFinancialDerivative != original.DEIsSecurityFinancialDerivative)
    {
        changes.Add("DEIsSecurityFinancialDerivative`"
                   + original.DEIsSecurityFinancialDerivative.ToString()
                   + "`"
                   + modified.DEIsSecurityFinancialDerivative.ToString().Replace("`", "-"));
    }

    // DE International Securities ID
    if (modified.DEInternationalSecuritiesID != original.DEInternationalSecuritiesID)
    {
        changes.Add("DEInternationalSecuritiesID`"
                   + original.DEInternationalSecuritiesID.ToString()
                   + "`"
                   + modified.DEInternationalSecuritiesID.ToString().Replace("`", "-"));
    }

    // Link to Contract
    if (modified.LinkToContract != original.LinkToContract)
    {
        changes.Add("LinkToContract`"
                   + original.LinkToContract.ToString()
                   + "`"
                   + modified.LinkToContract.ToString().Replace("`", "-"));
    }

    // DE Is Investment
    if (modified.DEIsInvestment != original.DEIsInvestment)
    {
        changes.Add("DEIsInvestment`"
                   + original.DEIsInvestment.ToString()
                   + "`"
                   + modified.DEIsInvestment.ToString().Replace("`", "-"));
    }

    // DE Pay Stat Code
    if (modified.DEPayStatCode != original.DEPayStatCode)
    {
        changes.Add("DEPayStatCode`"
                   + original.DEPayStatCode.ToString()
                   + "`"
                   + modified.DEPayStatCode.ToString().Replace("`", "-"));
    }

    // DE Denomination
    if (modified.DEDenomination != original.DEDenomination)
    {
        changes.Add("DEDenomination`"
                   + original.DEDenomination.ToString()
                   + "`"
                   + modified.DEDenomination.ToString().Replace("`", "-"));
    }

    // Track Inventory Attributes
    if (modified.TrackInventoryAttributes != original.TrackInventoryAttributes)
    {
        changes.Add("TrackInventoryAttributes`"
                   + original.TrackInventoryAttributes.ToString()
                   + "`"
                   + modified.TrackInventoryAttributes.ToString().Replace("`", "-"));
    }

    // Attribute Class ID
    if (modified.AttrClassID != original.AttrClassID)
    {
        changes.Add("AttrClassID`"
                   + original.AttrClassID.ToString()
                   + "`"
                   + modified.AttrClassID.ToString().Replace("`", "-"));
    }

    // Track Inventory By Revision
    if (modified.TrackInventoryByRevision != original.TrackInventoryByRevision)
    {
        changes.Add("TrackInventoryByRevision`"
                   + original.TrackInventoryByRevision.ToString()
                   + "`"
                   + modified.TrackInventoryByRevision.ToString().Replace("`", "-"));
    }

    // Created By
    if (modified.CreatedBy != original.CreatedBy)
    {
        changes.Add("CreatedBy`"
                   + original.CreatedBy.ToString()
                   + "`"
                   + modified.CreatedBy.ToString().Replace("`", "-"));
    }

    // Created On
    if (modified.CreatedOn != original.CreatedOn)
    {
        changes.Add("CreatedOn`"
                   + original.CreatedOn.ToString()
                   + "`"
                   + modified.CreatedOn.ToString().Replace("`", "-"));
    }

    // Changed On
    if (modified.ChangedOn != original.ChangedOn)
    {
        changes.Add("ChangedOn`"
                   + original.ChangedOn.ToString()
                   + "`"
                   + modified.ChangedOn.ToString().Replace("`", "-"));
    }

    // Send To FSA
    if (modified.SendToFSA != original.SendToFSA)
    {
        changes.Add("SendToFSA`"
                   + original.SendToFSA.ToString()
                   + "`"
                   + modified.SendToFSA.ToString().Replace("`", "-"));
    }

    // FSA Item
    if (modified.FSAItem != original.FSAItem)
    {
        changes.Add("FSAItem`"
                   + original.FSAItem.ToString()
                   + "`"
                   + modified.FSAItem.ToString().Replace("`", "-"));
    }

    // Is Services
    if (modified.IsServices != original.IsServices)
    {
        changes.Add("IsServices`"
                   + original.IsServices.ToString()
                   + "`"
                   + modified.IsServices.ToString().Replace("`", "-"));
    }

    // Saleable
    if (modified.Saleable != original.Saleable)
    {
        changes.Add("Saleable`"
                   + original.Saleable.ToString()
                   + "`"
                   + modified.Saleable.ToString().Replace("`", "-"));
    }

    // MX Expiration Date
    if (modified.MXExpirationDate != original.MXExpirationDate)
    {
        changes.Add("MXExpirationDate`"
                   + original.MXExpirationDate.ToString()
                   + "`"
                   + modified.MXExpirationDate.ToString().Replace("`", "-"));
    }

    // MX Medication Lot
    if (modified.MXMedicationLot != original.MXMedicationLot)
    {
        changes.Add("MXMedicationLot`"
                   + original.MXMedicationLot.ToString()
                   + "`"
                   + modified.MXMedicationLot.ToString().Replace("`", "-"));
    }

    // MX Pharmaceutical Form
    if (modified.MXPharmaceuticalForm != original.MXPharmaceuticalForm)
    {
        changes.Add("MXPharmaceuticalForm`"
                   + original.MXPharmaceuticalForm.ToString()
                   + "`"
                   + modified.MXPharmaceuticalForm.ToString().Replace("`", "-"));
    }

    // MX Keep Condition
    if (modified.MXKeepCondition != original.MXKeepCondition)
    {
        changes.Add("MXKeepCondition`"
                   + original.MXKeepCondition.ToString()
                   + "`"
                   + modified.MXKeepCondition.ToString().Replace("`", "-"));
    }

    // MX Sanitary Registration
    if (modified.MXSanitaryRegistration != original.MXSanitaryRegistration)
    {
        changes.Add("MXSanitaryRegistration`"
                   + original.MXSanitaryRegistration.ToString()
                   + "`"
                   + modified.MXSanitaryRegistration.ToString().Replace("`", "-"));
    }

    // MX CAS Number
    if (modified.MXCASNum != original.MXCASNum)
    {
        changes.Add("MXCASNum`"
                   + original.MXCASNum.ToString()
                   + "`"
                   + modified.MXCASNum.ToString().Replace("`", "-"));
    }

    // MX Import Sanitary Registration
    if (modified.MXImportSanitaryRegistration != original.MXImportSanitaryRegistration)
    {
        changes.Add("MXImportSanitaryRegistration`"
                   + original.MXImportSanitaryRegistration.ToString()
                   + "`"
                   + modified.MXImportSanitaryRegistration.ToString().Replace("`", "-"));
    }

    // MX Manufacturer Data
    if (modified.MXManufacturerData != original.MXManufacturerData)
    {
        changes.Add("MXManufacturerData`"
                   + original.MXManufacturerData.ToString()
                   + "`"
                   + modified.MXManufacturerData.ToString().Replace("`", "-"));
    }

    // MX Formulator Data
    if (modified.MXFormulatorData != original.MXFormulatorData)
    {
        changes.Add("MXFormulatorData`"
                   + original.MXFormulatorData.ToString()
                   + "`"
                   + modified.MXFormulatorData.ToString().Replace("`", "-"));
    }

    // MX Person Data
    if (modified.MXPersonData != original.MXPersonData)
    {
        changes.Add("MXPersonData`"
                   + original.MXPersonData.ToString()
                   + "`"
                   + modified.MXPersonData.ToString().Replace("`", "-"));
    }

    // MX Authorized Use
    if (modified.MXAuthorizedUse != original.MXAuthorizedUse)
    {
        changes.Add("MXAuthorizedUse`"
                   + original.MXAuthorizedUse.ToString()
                   + "`"
                   + modified.MXAuthorizedUse.ToString().Replace("`", "-"));
    }

    // MX Material Type
    if (modified.MXMaterialType != original.MXMaterialType)
    {
        changes.Add("MXMaterialType`"
                   + original.MXMaterialType.ToString()
                   + "`"
                   + modified.MXMaterialType.ToString().Replace("`", "-"));
    }

    // MX Material Type Description
    if (modified.MXMaterialTypeDescription != original.MXMaterialTypeDescription)
    {
        changes.Add("MXMaterialTypeDescription`"
                   + original.MXMaterialTypeDescription.ToString()
                   + "`"
                   + modified.MXMaterialTypeDescription.ToString().Replace("`", "-"));
    }



    // Part Length Width Height UOM
    if (modified.PartLengthWidthHeightUM != original.PartLengthWidthHeightUM)
    {
        changes.Add("PartLengthWidthHeightUM`"
                   + original.PartLengthWidthHeightUM.ToString()
                   + "`"
                   + modified.PartLengthWidthHeightUM.ToString().Replace("`", "-"));
    }

    // Diameter UOM
    if (modified.DiameterUM != original.DiameterUM)
    {
        changes.Add("DiameterUM`"
                   + original.DiameterUM.ToString()
                   + "`"
                   + modified.DiameterUM.ToString().Replace("`", "-"));
    }

    // Diameter Inside
    if (modified.DiameterInside != original.DiameterInside)
    {
        changes.Add("DiameterInside`"
                   + original.DiameterInside.ToString()
                   + "`"
                   + modified.DiameterInside.ToString().Replace("`", "-"));
    }

    // Diameter Outside
    if (modified.DiameterOutside != original.DiameterOutside)
    {
        changes.Add("DiameterOutside`"
                   + original.DiameterOutside.ToString()
                   + "`"
                   + modified.DiameterOutside.ToString().Replace("`", "-"));
    }

    // Thickness UOM
    if (modified.ThicknessUM != original.ThicknessUM)
    {
        changes.Add("ThicknessUM`"
                   + original.ThicknessUM.ToString()
                   + "`"
                   + modified.ThicknessUM.ToString().Replace("`", "-"));
    }

    // Thickness
    if (modified.Thickness != original.Thickness)
    {
        changes.Add("Thickness`"
                   + original.Thickness.ToString()
                   + "`"
                   + modified.Thickness.ToString().Replace("`", "-"));
    }

    // Thickness Max
    if (modified.ThicknessMax != original.ThicknessMax)
    {
        changes.Add("ThicknessMax`"
                   + original.ThicknessMax.ToString()
                   + "`"
                   + modified.ThicknessMax.ToString().Replace("`", "-"));
    }

    // Durometer
    if (modified.Durometer != original.Durometer)
    {
        changes.Add("Durometer`"
                   + original.Durometer.ToString()
                   + "`"
                   + modified.Durometer.ToString().Replace("`", "-"));
    }

    // Specification
    if (modified.Specification != original.Specification)
    {
        changes.Add("Specification`"
                   + original.Specification.ToString()
                   + "`"
                   + modified.Specification.ToString().Replace("`", "-"));
    }

    // Engineering Alert
    if (modified.EngineeringAlert != original.EngineeringAlert)
    {
        changes.Add("EngineeringAlert`"
                   + original.EngineeringAlert.ToString()
                   + "`"
                   + modified.EngineeringAlert.ToString().Replace("`", "-"));
    }

    // Condition
    if (modified.Condition != original.Condition)
    {
        changes.Add("Condition`"
                   + original.Condition.ToString()
                   + "`"
                   + modified.Condition.ToString().Replace("`", "-"));
    }

    // Is Compliant
    if (modified.IsCompliant != original.IsCompliant)
    {
        changes.Add("IsCompliant`"
                   + original.IsCompliant.ToString()
                   + "`"
                   + modified.IsCompliant.ToString().Replace("`", "-"));
    }

    // Is Restricted
    if (modified.IsRestricted != original.IsRestricted)
    {
        changes.Add("IsRestricted`"
                   + original.IsRestricted.ToString()
                   + "`"
                   + modified.IsRestricted.ToString().Replace("`", "-"));
    }

    // Is Safety Item
    if (modified.IsSafetyItem != original.IsSafetyItem)
    {
        changes.Add("IsSafetyItem`"
                   + original.IsSafetyItem.ToString()
                   + "`"
                   + modified.IsSafetyItem.ToString().Replace("`", "-"));
    }

    // Commercial Brand
    if (modified.CommercialBrand != original.CommercialBrand)
    {
        changes.Add("CommercialBrand`"
                   + original.CommercialBrand.ToString()
                   + "`"
                   + modified.CommercialBrand.ToString().Replace("`", "-"));
    }

    // Commercial Sub Brand
    if (modified.CommercialSubBrand != original.CommercialSubBrand)
    {
        changes.Add("CommercialSubBrand`"
                   + original.CommercialSubBrand.ToString()
                   + "`"
                   + modified.CommercialSubBrand.ToString().Replace("`", "-"));
    }

    // Commercial Category
    if (modified.CommercialCategory != original.CommercialCategory)
    {
        changes.Add("CommercialCategory`"
                   + original.CommercialCategory.ToString()
                   + "`"
                   + modified.CommercialCategory.ToString().Replace("`", "-"));
    }

    // Commercial Sub Category
    if (modified.CommercialSubCategory != original.CommercialSubCategory)
    {
        changes.Add("CommercialSubCategory`"
                   + original.CommercialSubCategory.ToString()
                   + "`"
                   + modified.CommercialSubCategory.ToString().Replace("`", "-"));
    }

    // Commercial Style
    if (modified.CommercialStyle != original.CommercialStyle)
    {
        changes.Add("CommercialStyle`"
                   + original.CommercialStyle.ToString()
                   + "`"
                   + modified.CommercialStyle.ToString().Replace("`", "-"));
    }

    // Commercial Size 1
    if (modified.CommercialSize1 != original.CommercialSize1)
    {
        changes.Add("CommercialSize1`"
                   + original.CommercialSize1.ToString()
                   + "`"
                   + modified.CommercialSize1.ToString().Replace("`", "-"));
    }

    // Commercial Size 2
    if (modified.CommercialSize2 != original.CommercialSize2)
    {
        changes.Add("CommercialSize2`"
                   + original.CommercialSize2.ToString()
                   + "`"
                   + modified.CommercialSize2.ToString().Replace("`", "-"));
    }

    // Commercial Color
    if (modified.CommercialColor != original.CommercialColor)
    {
        changes.Add("CommercialColor`"
                   + original.CommercialColor.ToString()
                   + "`"
                   + modified.CommercialColor.ToString().Replace("`", "-"));
    }

    // Is Gift Card
    if (modified.IsGiftCard != original.IsGiftCard)
    {
        changes.Add("IsGiftCard`"
                   + original.IsGiftCard.ToString()
                   + "`"
                   + modified.IsGiftCard.ToString().Replace("`", "-"));
    }

    // Photo File
    if (modified.PhotoFile != original.PhotoFile)
    {
        changes.Add("PhotoFile`"
                   + original.PhotoFile.ToString()
                   + "`"
                   + modified.PhotoFile.ToString().Replace("`", "-"));
    }

    // Part Photo Exists
    if (modified.PartPhotoExists != original.PartPhotoExists)
    {
        changes.Add("PartPhotoExists`"
                   + original.PartPhotoExists.ToString()
                   + "`"
                   + modified.PartPhotoExists.ToString().Replace("`", "-"));
    }

    // Comment Text
    if (modified.CommentText != original.CommentText)
    {
        changes.Add("CommentText`"
                   + original.CommentText.ToString()
                   + "`"
                   + modified.CommentText.ToString().Replace("`", "-"));
    }

    // Part Specific Packing UOM
    if (modified.PartSpecificPackingUOM != original.PartSpecificPackingUOM)
    {
        changes.Add("PartSpecificPackingUOM`"
                   + original.PartSpecificPackingUOM.ToString()
                   + "`"
                   + modified.PartSpecificPackingUOM.ToString().Replace("`", "-"));
    }

    // Image ID
    if (modified.ImageID != original.ImageID)
    {
        changes.Add("ImageID`"
                   + original.ImageID.ToString()
                   + "`"
                   + modified.ImageID.ToString().Replace("`", "-"));
    }

    // CN Specification
    if (modified.CNSpecification != original.CNSpecification)
    {
        changes.Add("CNSpecification`"
                   + original.CNSpecification.ToString()
                   + "`"
                   + modified.CNSpecification.ToString().Replace("`", "-"));
    }

    // Sync To External CRM
    if (modified.SyncToExternalCRM != original.SyncToExternalCRM)
    {
        changes.Add("SyncToExternalCRM`"
                   + original.SyncToExternalCRM.ToString()
                   + "`"
                   + modified.SyncToExternalCRM.ToString().Replace("`", "-"));
    }

    // External CRM Part ID
    if (modified.ExternalCRMPartID != original.ExternalCRMPartID)
    {
        changes.Add("ExternalCRMPartID`"
                   + original.ExternalCRMPartID.ToString()
                   + "`"
                   + modified.ExternalCRMPartID.ToString().Replace("`", "-"));
    }

    // External CRM Last Sync
    if (modified.ExternalCRMLastSync != original.ExternalCRMLastSync)
    {
        changes.Add("ExternalCRMLastSync`"
                   + original.ExternalCRMLastSync.ToString()
                   + "`"
                   + modified.ExternalCRMLastSync.ToString().Replace("`", "-"));
    }

    // External CRM Sync Required
    if (modified.ExternalCRMSyncRequired != original.ExternalCRMSyncRequired)
    {
        changes.Add("ExternalCRMSyncRequired`"
                   + original.ExternalCRMSyncRequired.ToString()
                   + "`"
                   + modified.ExternalCRMSyncRequired.ToString().Replace("`", "-"));
    }

    // PE SUNAT Type Code
    if (modified.PESUNATTypeCode != original.PESUNATTypeCode)
    {
        changes.Add("PESUNATTypeCode`"
                   + original.PESUNATTypeCode.ToString()
                   + "`"
                   + modified.PESUNATTypeCode.ToString().Replace("`", "-"));
    }

    // PE SUNAT UOM Code
    if (modified.PESUNATUOMCode != original.PESUNATUOMCode)
    {
        changes.Add("PESUNATUOMCode`"
                   + original.PESUNATUOMCode.ToString()
                   + "`"
                   + modified.PESUNATUOMCode.ToString().Replace("`", "-"));
    }

    // CN Code Version
    if (modified.CNCodeVersion != original.CNCodeVersion)
    {
        changes.Add("CNCodeVersion`"
                   + original.CNCodeVersion.ToString()
                   + "`"
                   + modified.CNCodeVersion.ToString().Replace("`", "-"));
    }

    // CN Tax Category Code
    if (modified.CNTaxCategoryCode != original.CNTaxCategoryCode)
    {
        changes.Add("CNTaxCategoryCode`"
                   + original.CNTaxCategoryCode.ToString()
                   + "`"
                   + modified.CNTaxCategoryCode.ToString().Replace("`", "-"));
    }

    // CN Has Preferential Treatment
    if (modified.CNHasPreferentialTreatment != original.CNHasPreferentialTreatment)
    {
        changes.Add("CNHasPreferentialTreatment`"
                   + original.CNHasPreferentialTreatment.ToString()
                   + "`"
                   + modified.CNHasPreferentialTreatment.ToString().Replace("`", "-"));
    }

    // CN Preferential Treatment Content
    if (modified.CNPreferentialTreatmentContent != original.CNPreferentialTreatmentContent)
    {
        changes.Add("CNPreferentialTreatmentContent`"
                   + original.CNPreferentialTreatmentContent.ToString()
                   + "`"
                   + modified.CNPreferentialTreatmentContent.ToString().Replace("`", "-"));
    }

    // CN Zero Tax Rate Mark
    if (modified.CNZeroTaxRateMark != original.CNZeroTaxRateMark)
    {
        changes.Add("CNZeroTaxRateMark`"
                   + original.CNZeroTaxRateMark.ToString()
                   + "`"
                   + modified.CNZeroTaxRateMark.ToString().Replace("`", "-"));
    }

    // Sub Level Code
    if (modified.SubLevelCode != original.SubLevelCode)
    {
        changes.Add("SubLevelCode`"
                   + original.SubLevelCode.ToString()
                   + "`"
                   + modified.SubLevelCode.ToString().Replace("`", "-"));
    }

    // Attribute Batch
    if (modified.AttBatch != original.AttBatch)
    {
        changes.Add("AttBatch`"
                   + original.AttBatch.ToString()
                   + "`"
                   + modified.AttBatch.ToString().Replace("`", "-"));
    }

    // Attribute Manufacturing Batch
    if (modified.AttMfgBatch != original.AttMfgBatch)
    {
        changes.Add("AttMfgBatch`"
                   + original.AttMfgBatch.ToString()
                   + "`"
                   + modified.AttMfgBatch.ToString().Replace("`", "-"));
    }

    // Attribute Manufacturing Lot
    if (modified.AttMfgLot != original.AttMfgLot)
    {
        changes.Add("AttMfgLot`"
                   + original.AttMfgLot.ToString()
                   + "`"
                   + modified.AttMfgLot.ToString().Replace("`", "-"));
    }

    // Attribute Heat
    if (modified.AttHeat != original.AttHeat)
    {
        changes.Add("AttHeat`"
                   + original.AttHeat.ToString()
                   + "`"
                   + modified.AttHeat.ToString().Replace("`", "-"));
    }

    // Attribute Firmware
    if (modified.AttFirmware != original.AttFirmware)
    {
        changes.Add("AttFirmware`"
                   + original.AttFirmware.ToString()
                   + "`"
                   + modified.AttFirmware.ToString().Replace("`", "-"));
    }

    // Attribute Before Date
    if (modified.AttBeforeDt != original.AttBeforeDt)
    {
        changes.Add("AttBeforeDt`"
                   + original.AttBeforeDt.ToString()
                   + "`"
                   + modified.AttBeforeDt.ToString().Replace("`", "-"));
    }

    // Attribute Manufacturing Date
    if (modified.AttMfgDt != original.AttMfgDt)
    {
        changes.Add("AttMfgDt`"
                   + original.AttMfgDt.ToString()
                   + "`"
                   + modified.AttMfgDt.ToString().Replace("`", "-"));
    }

    // Attribute Cure Date
    if (modified.AttCureDt != original.AttCureDt)
    {
        changes.Add("AttCureDt`"
                   + original.AttCureDt.ToString()
                   + "`"
                   + modified.AttCureDt.ToString().Replace("`", "-"));
    }

    // Attribute Expiration Date
    if (modified.AttExpDt != original.AttExpDt)
    {
        changes.Add("AttExpDt`"
                   + original.AttExpDt.ToString()
                   + "`"
                   + modified.AttExpDt.ToString().Replace("`", "-"));
    }

    // Defer Manual Entry
    if (modified.DeferManualEntry != original.DeferManualEntry)
    {
        changes.Add("DeferManualEntry`"
                   + original.DeferManualEntry.ToString()
                   + "`"
                   + modified.DeferManualEntry.ToString().Replace("`", "-"));
    }

    // Defer Purchase Receipt
    if (modified.DeferPurchaseReceipt != original.DeferPurchaseReceipt)
    {
        changes.Add("DeferPurchaseReceipt`"
                   + original.DeferPurchaseReceipt.ToString()
                   + "`"
                   + modified.DeferPurchaseReceipt.ToString().Replace("`", "-"));
    }

    // Defer Job Receipt
    if (modified.DeferJobReceipt != original.DeferJobReceipt)
    {
        changes.Add("DeferJobReceipt`"
                   + original.DeferJobReceipt.ToString()
                   + "`"
                   + modified.DeferJobReceipt.ToString().Replace("`", "-"));
    }

    // Defer Inspection
    if (modified.DeferInspection != original.DeferInspection)
    {
        changes.Add("DeferInspection`"
                   + original.DeferInspection.ToString()
                   + "`"
                   + modified.DeferInspection.ToString().Replace("`", "-"));
    }

    // Defer Quantity Adjustment
    if (modified.DeferQtyAdjustment != original.DeferQtyAdjustment)
    {
        changes.Add("DeferQtyAdjustment`"
                   + original.DeferQtyAdjustment.ToString()
                   + "`"
                   + modified.DeferQtyAdjustment.ToString().Replace("`", "-"));
    }

    // Defer Inventory Move
    if (modified.DeferInventoryMove != original.DeferInventoryMove)
    {
        changes.Add("DeferInventoryMove`"
                   + original.DeferInventoryMove.ToString()
                   + "`"
                   + modified.DeferInventoryMove.ToString().Replace("`", "-"));
    }

    // Defer Shipments
    if (modified.DeferShipments != original.DeferShipments)
    {
        changes.Add("DeferShipments`"
                   + original.DeferShipments.ToString()
                   + "`"
                   + modified.DeferShipments.ToString().Replace("`", "-"));
    }

    // Defer Inventory Counts
    if (modified.DeferInventoryCounts != original.DeferInventoryCounts)
    {
        changes.Add("DeferInventoryCounts`"
                   + original.DeferInventoryCounts.ToString()
                   + "`"
                   + modified.DeferInventoryCounts.ToString().Replace("`", "-"));
    }

    // Defer Asset Disposal
    if (modified.DeferAssetDisposal != original.DeferAssetDisposal)
    {
        changes.Add("DeferAssetDisposal`"
                   + original.DeferAssetDisposal.ToString()
                   + "`"
                   + modified.DeferAssetDisposal.ToString().Replace("`", "-"));
    }

    // Defer Return Materials
    if (modified.DeferReturnMaterials != original.DeferReturnMaterials)
    {
        changes.Add("DeferReturnMaterials`"
                   + original.DeferReturnMaterials.ToString()
                   + "`"
                   + modified.DeferReturnMaterials.ToString().Replace("`", "-"));
    }

    // MX Product Service Code
    if (modified.MXProdServCode != original.MXProdServCode)
    {
        changes.Add("MXProdServCode`"
                   + original.MXProdServCode.ToString()
                   + "`"
                   + modified.MXProdServCode.ToString().Replace("`", "-"));
    }

    // MX Customs Duty
    if (modified.MXCustomsDuty != original.MXCustomsDuty)
    {
        changes.Add("MXCustomsDuty`"
                   + original.MXCustomsDuty.ToString()
                   + "`"
                   + modified.MXCustomsDuty.ToString().Replace("`", "-"));
    }

    // External MES Sync Required
    if (modified.ExternalMESSyncRequired != original.ExternalMESSyncRequired)
    {
        changes.Add("ExternalMESSyncRequired`"
                   + original.ExternalMESSyncRequired.ToString()
                   + "`"
                   + modified.ExternalMESSyncRequired.ToString().Replace("`", "-"));
    }

    // External MES Last Sync
    if (modified.ExternalMESLastSync != original.ExternalMESLastSync)
    {
        changes.Add("ExternalMESLastSync`"
                   + original.ExternalMESLastSync.ToString()
                   + "`"
                   + modified.ExternalMESLastSync.ToString().Replace("`", "-"));
    }

    // FSA Equipment
    if (modified.FSAEquipment != original.FSAEquipment)
    {
        changes.Add("FSAEquipment`"
                   + original.FSAEquipment.ToString()
                   + "`"
                   + modified.FSAEquipment.ToString().Replace("`", "-"));
    }

    // BOL Class
    if (modified.BOLClass != original.BOLClass)
    {
        changes.Add("BOLClass`"
                   + original.BOLClass.ToString()
                   + "`"
                   + modified.BOLClass.ToString().Replace("`", "-"));
    }

    // Fair Market Value
    if (modified.FairMarketValue != original.FairMarketValue)
    {
        changes.Add("FairMarketValue`"
                   + original.FairMarketValue.ToString()
                   + "`"
                   + modified.FairMarketValue.ToString().Replace("`", "-"));
    }

    // SAFT Product Category
    if (modified.SAFTProdCategory != original.SAFTProdCategory)
    {
        changes.Add("SAFTProdCategory`"
                   + original.SAFTProdCategory.ToString()
                   + "`"
                   + modified.SAFTProdCategory.ToString().Replace("`", "-"));
    }

    // Location ID Number Required
    if (modified.LocationIDNumReq != original.LocationIDNumReq)
    {
        changes.Add("LocationIDNumReq`"
                   + original.LocationIDNumReq.ToString()
                   + "`"
                   + modified.LocationIDNumReq.ToString().Replace("`", "-"));
    }

    // Location Track Inventory
    if (modified.LocationTrackInv != original.LocationTrackInv)
    {
        changes.Add("LocationTrackInv`"
                   + original.LocationTrackInv.ToString()
                   + "`"
                   + modified.LocationTrackInv.ToString().Replace("`", "-"));
    }

    // Location Material View
    if (modified.LocationMtlView != original.LocationMtlView)
    {
        changes.Add("LocationMtlView`"
                   + original.LocationMtlView.ToString()
                   + "`"
                   + modified.LocationMtlView.ToString().Replace("`", "-"));
    }

    // LCNRV Reporting
    if (modified.LCNRVReporting != original.LCNRVReporting)
    {
        changes.Add("LCNRVReporting`"
                   + original.LCNRVReporting.ToString()
                   + "`"
                   + modified.LCNRVReporting.ToString().Replace("`", "-"));
    }

    // LCNRV Estimated Unit Price
    if (modified.LCNRVEstimatedUnitPrice != original.LCNRVEstimatedUnitPrice)
    {
        changes.Add("LCNRVEstimatedUnitPrice`"
                   + original.LCNRVEstimatedUnitPrice.ToString()
                   + "`"
                   + modified.LCNRVEstimatedUnitPrice.ToString().Replace("`", "-"));
    }

    // MX Customs UOM From
    if (modified.MXCustomsUMFrom != original.MXCustomsUMFrom)
    {
        changes.Add("MXCustomsUMFrom`"
                   + original.MXCustomsUMFrom.ToString()
                   + "`"
                   + modified.MXCustomsUMFrom.ToString().Replace("`", "-"));
    }

    // Location Format ID
    if (modified.LocationFormatID != original.LocationFormatID)
    {
        changes.Add("LocationFormatID`"
                   + original.LocationFormatID.ToString()
                   + "`"
                   + modified.LocationFormatID.ToString().Replace("`", "-"));
    }

    // PE Detr Good Service Code
    if (modified.PEDetrGoodServiceCode != original.PEDetrGoodServiceCode)
    {
        changes.Add("PEDetrGoodServiceCode`"
                   + original.PEDetrGoodServiceCode.ToString()
                   + "`"
                   + modified.PEDetrGoodServiceCode.ToString().Replace("`", "-"));
    }

    // PE Product Service Code
    if (modified.PEProductServiceCode != original.PEProductServiceCode)
    {
        changes.Add("PEProductServiceCode`"
                   + original.PEProductServiceCode.ToString()
                   + "`"
                   + modified.PEProductServiceCode.ToString().Replace("`", "-"));
    }

    // Dual UOM Class ID
    if (modified.DualUOMClassID != original.DualUOMClassID)
    {
        changes.Add("DualUOMClassID`"
                   + original.DualUOMClassID.ToString()
                   + "`"
                   + modified.DualUOMClassID.ToString().Replace("`", "-"));
    }

    // CN Product Name
    if (modified.CNProductName != original.CNProductName)
    {
        changes.Add("CNProductName`"
                   + original.CNProductName.ToString()
                   + "`"
                   + modified.CNProductName.ToString().Replace("`", "-"));
    }

    // CN Weight
    if (modified.CNWeight != original.CNWeight)
    {
        changes.Add("CNWeight`"
                   + original.CNWeight.ToString()
                   + "`"
                   + modified.CNWeight.ToString().Replace("`", "-"));
    }

    // CN Weight UOM
    if (modified.CNWeightUOM != original.CNWeightUOM)
    {
        changes.Add("CNWeightUOM`"
                   + original.CNWeightUOM.ToString()
                   + "`"
                   + modified.CNWeightUOM.ToString().Replace("`", "-"));
    }

    // CN Bonded
    if (modified.CNBonded != original.CNBonded)
    {
        changes.Add("CNBonded`"
                   + original.CNBonded.ToString()
                   + "`"
                   + modified.CNBonded.ToString().Replace("`", "-"));
    }

    // Default Attribute Set ID
    if (modified.DefaultAttributeSetID != original.DefaultAttributeSetID)
    {
        changes.Add("DefaultAttributeSetID`"
                   + original.DefaultAttributeSetID.ToString()
                   + "`"
                   + modified.DefaultAttributeSetID.ToString().Replace("`", "-"));
    }

    // Attribute IS Original Country
    if (modified.AttISOrigCountry != original.AttISOrigCountry)
    {
        changes.Add("AttISOrigCountry`"
                   + original.AttISOrigCountry.ToString()
                   + "`"
                   + modified.AttISOrigCountry.ToString().Replace("`", "-"));
    }

    // External Scheme ID
    if (modified.ExternalSchemeID != original.ExternalSchemeID)
    {
        changes.Add("ExternalSchemeID`"
                   + original.ExternalSchemeID.ToString()
                   + "`"
                   + modified.ExternalSchemeID.ToString().Replace("`", "-"));
    }

    // External ID
    if (modified.ExternalID != original.ExternalID)
    {
        changes.Add("ExternalID`"
                   + original.ExternalID.ToString()
                   + "`"
                   + modified.ExternalID.ToString().Replace("`", "-"));
    }

    // Commodity Scheme ID
    if (modified.CommoditySchemeID != original.CommoditySchemeID)
    {
        changes.Add("CommoditySchemeID`"
                   + original.CommoditySchemeID.ToString()
                   + "`"
                   + modified.CommoditySchemeID.ToString().Replace("`", "-"));
    }

    // Commodity Scheme Version
    if (modified.CommoditySchemeVersion != original.CommoditySchemeVersion)
    {
        changes.Add("CommoditySchemeVersion`"
                   + original.CommoditySchemeVersion.ToString()
                   + "`"
                   + modified.CommoditySchemeVersion.ToString().Replace("`", "-"));
    }

    // Planning By Revision
    if (modified.PlanningByRevision != original.PlanningByRevision)
    {
        changes.Add("PlanningByRevision`"
                   + original.PlanningByRevision.ToString()
                   + "`"
                   + modified.PlanningByRevision.ToString().Replace("`", "-"));
    }

    // Receive Inspection Required Part
    if (modified.RcvInspectionReqPart != original.RcvInspectionReqPart)
    {
        changes.Add("RcvInspectionReqPart`"
                   + original.RcvInspectionReqPart.ToString()
                   + "`"
                   + modified.RcvInspectionReqPart.ToString().Replace("`", "-"));
    }

    // FSM Send To
    if (modified.FSMSendTo != original.FSMSendTo)
    {
        changes.Add("FSMSendTo`"
                   + original.FSMSendTo.ToString()
                   + "`"
                   + modified.FSMSendTo.ToString().Replace("`", "-"));
    }

    // FSM Part Type
    if (modified.FSMPartType != original.FSMPartType)
    {
        changes.Add("FSMPartType`"
                   + original.FSMPartType.ToString()
                   + "`"
                   + modified.FSMPartType.ToString().Replace("`", "-"));
    }

    // MX Product Class
    if (modified.MXProductClass != original.MXProductClass)
    {
        changes.Add("MXProductClass`"
                   + original.MXProductClass.ToString()
                   + "`"
                   + modified.MXProductClass.ToString().Replace("`", "-"));
    }

    // MX Active Ingredient Name
    if (modified.MXActiveIngredientName != original.MXActiveIngredientName)
    {
        changes.Add("MXActiveIngredientName`"
                   + original.MXActiveIngredientName.ToString()
                   + "`"
                   + modified.MXActiveIngredientName.ToString().Replace("`", "-"));
    }

    // MX Chemical Name
    if (modified.MXChemicalName != original.MXChemicalName)
    {
        changes.Add("MXChemicalName`"
                   + original.MXChemicalName.ToString()
                   + "`"
                   + modified.MXChemicalName.ToString().Replace("`", "-"));
    }

    // MX Generic Product Name
    if (modified.MXGenericProductName != original.MXGenericProductName)
    {
        changes.Add("MXGenericProductName`"
                   + original.MXGenericProductName.ToString()
                   + "`"
                   + modified.MXGenericProductName.ToString().Replace("`", "-"));
    }

    // MX Trademark
    if (modified.MXTrademark != original.MXTrademark)
    {
        changes.Add("MXTrademark`"
                   + original.MXTrademark.ToString()
                   + "`"
                   + modified.MXTrademark.ToString().Replace("`", "-"));
    }

    // MX Manufacturer Company
    if (modified.MXManufacturerCompany != original.MXManufacturerCompany)
    {
        changes.Add("MXManufacturerCompany`"
                   + original.MXManufacturerCompany.ToString()
                   + "`"
                   + modified.MXManufacturerCompany.ToString().Replace("`", "-"));
    }

    // Carbon Material Burden Rate
    if (modified.CarbonMtlBurRate != original.CarbonMtlBurRate)
    {
        changes.Add("CarbonMtlBurRate`"
                   + original.CarbonMtlBurRate.ToString()
                   + "`"
                   + modified.CarbonMtlBurRate.ToString().Replace("`", "-"));
    }

    // FSM Override Product Code
    if (modified.FSMOverrideProdCode != original.FSMOverrideProdCode)
    {
        changes.Add("FSMOverrideProdCode`"
                   + original.FSMOverrideProdCode.ToString()
                   + "`"
                   + modified.FSMOverrideProdCode.ToString().Replace("`", "-"));
    }

    // =================================================================
    // Process and Split Changes for Output
    // =================================================================

    if (changes.Count > 0)
    {
        try
        {
            string allChanges = string.Join("`~`", changes);
            test2 = $"Debug: Created allChanges string, length={allChanges.Length.ToString()}";

            // Split changes into chunks if needed (max 1000 characters per chunk)
            if (allChanges.Length <= 1000)
            {
                changesMade = allChanges;
                callFunc = true;
                test2 = "Debug: Single chunk assigned to changesMade";
            }
            else
            {
                // Split into 1000-character chunks with overflow handling
                const int maxChunkSize = 1000;
                int totalLength = allChanges.Length;

                // Clear all changesMade variables first
                changesMade = "";
                changesMade2 = "";
                changesMade3 = "";
                changesMade4 = "";
                changesMade5 = "";
                changesMade6 = "";
                changesMade7 = "";
                changesMade8 = "";
                changesMade9 = "";
                changesMade10 = "";

                // Assign chunks based on position - each exactly 1000 chars (or remaining)
                if (totalLength > 0)
                {
                    changesMade = allChanges.Substring(0, Math.Min(maxChunkSize, totalLength));
                    callFunc = true;
                }
                if (totalLength > 1000)
                {
                    changesMade2 = allChanges.Substring(1000, Math.Min(maxChunkSize, totalLength - 1000));
                }
                if (totalLength > 2000)
                {
                    changesMade3 = allChanges.Substring(2000, Math.Min(maxChunkSize, totalLength - 2000));
                }
                if (totalLength > 3000)
                {
                    changesMade4 = allChanges.Substring(3000, Math.Min(maxChunkSize, totalLength - 3000));
                }
                if (totalLength > 4000)
                {
                    changesMade5 = allChanges.Substring(4000, Math.Min(maxChunkSize, totalLength - 4000));
                }
                if (totalLength > 5000)
                {
                    changesMade6 = allChanges.Substring(5000, Math.Min(maxChunkSize, totalLength - 5000));
                }
                if (totalLength > 6000)
                {
                    changesMade7 = allChanges.Substring(6000, Math.Min(maxChunkSize, totalLength - 6000));
                }
                if (totalLength > 7000)
                {
                    changesMade8 = allChanges.Substring(7000, Math.Min(maxChunkSize, totalLength - 7000));
                }
                if (totalLength > 8000)
                {
                    changesMade9 = allChanges.Substring(8000, Math.Min(maxChunkSize, totalLength - 8000));
                }
                if (totalLength > 9000)
                {
                    changesMade10 = allChanges.Substring(9000, Math.Min(maxChunkSize, totalLength - 9000));
                }

                test2 = $"Debug: Split {totalLength.ToString()} chars into chunks successfully";
            }
        }
        catch (System.Exception ex)
        {
            test2 = $"Debug: Exception in splitting logic: {ex.Message} - Type: {ex.GetType().Name}";
            changesMade = "Error in splitting logic";
            callFunc = true;
        }
    }
}
