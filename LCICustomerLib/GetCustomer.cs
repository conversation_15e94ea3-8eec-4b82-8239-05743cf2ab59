/*
 * GetCustomer - Epicor Function
 *
 * DESCRIPTION:
 * This function searches for customers by name (partial or complete match) and returns
 * customer information in a structured DataSet. This is a simplified version of 
 * GetCustomerWithShipTo that only returns customer data without ShipTo information.
 *
 * INPUT PARAMETERS:
 * - inputCustomerName (string): Customer name to search for. Can be:
 *   • Complete name: "ABC Company Inc"
 *   • Partial name: "ABC" (will find "ABC Company", "ABC Industries", etc.)
 *   • Case-insensitive: "abc company" will match "ABC Company"
 *
 * OUTPUT PARAMETERS:
 * - outputDataSet (System.Data.DataSet): Contains one DataTable with customer information
 *
 * DATASET STRUCTURE:
 *
 * Table: "Customer" - Contains customer information
 * ┌─────────────┬──────────┬─────────────────────────────────────────┐
 * │ Column Name │ Data Type│ Description                             │
 * ├─────────────┼──────────┼─────────────────────────────────────────┤
 * │ CustID      │ string   │ Customer ID (primary identifier)        │
 * │ Name        │ string   │ Customer company name                   │
 * │ CustNum     │ int      │ Customer number (internal ID)           │
 * │ Address1    │ string   │ Primary address line                    │
 * │ Address2    │ string   │ Secondary address line                  │
 * │ Address3    │ string   │ Third address line                      │
 * │ City        │ string   │ City                                    │
 * │ State       │ string   │ State/Province                          │
 * │ Zip         │ string   │ ZIP/Postal code                         │
 * │ Country     │ string   │ Country                                 │
 * │ PhoneNum    │ string   │ Primary phone number                    │
 * │ FaxNum      │ string   │ Fax number                              │
 * │ EMailAddress│ string   │ Email address                           │
 * │ Active      │ bool     │ Customer active status (true/false)     │
 * └─────────────┴──────────┴─────────────────────────────────────────┘
 *
 * USAGE EXAMPLES:
 *
 * Example 1 - Search for exact customer name:
 * Input: inputCustomerName = "Acme Corporation"
 * Result: Returns one customer record if found
 *
 * Example 2 - Search for partial customer name:
 * Input: inputCustomerName = "Acme"
 * Result: Returns all customers with "Acme" in their name:
 *         - "Acme Corporation"
 *         - "Acme Industries"
 *         - "Super Acme Company"
 *
 * Example 3 - Case-insensitive search:
 * Input: inputCustomerName = "acme corp"
 * Result: Finds "Acme Corporation", "ACME CORP", etc.
 *
 * BEHAVIOR NOTES:
 * • If no customers match the search criteria, returns empty DataSet with table structure intact
 * • All string fields are null-safe (empty string returned if null)
 * • Multiple customers can be returned in a single result set
 */

// Create DataSet to hold customer information
var dataSet = new System.Data.DataSet("CustomerData");

// Create Customer DataTable
var customerTable = new System.Data.DataTable("Customer");
customerTable.Columns.Add("CustID", typeof(string));
customerTable.Columns.Add("Name", typeof(string));
customerTable.Columns.Add("CustNum", typeof(int));
customerTable.Columns.Add("Address1", typeof(string));
customerTable.Columns.Add("Address2", typeof(string));
customerTable.Columns.Add("Address3", typeof(string));
customerTable.Columns.Add("City", typeof(string));
customerTable.Columns.Add("State", typeof(string));
customerTable.Columns.Add("Zip", typeof(string));
customerTable.Columns.Add("Country", typeof(string));
customerTable.Columns.Add("PhoneNum", typeof(string));
customerTable.Columns.Add("FaxNum", typeof(string));
customerTable.Columns.Add("EMailAddress", typeof(string));
customerTable.Columns.Add("Active", typeof(bool));

// Add table to dataset
dataSet.Tables.Add(customerTable);

// Search for customers with names containing the input (case-insensitive)
var customers = Db.Customer.Where(cust => cust.Name.ToLower().Contains(inputCustomerName.ToLower())).ToList();

if (customers.Count == 0)
{
    // Return empty dataset if no customers found
    outputDataSet = dataSet;
    return;
}

// Process each matching customer
foreach (var customer in customers)
{
    // Add customer row
    var customerRow = customerTable.NewRow();
    customerRow["CustID"] = customer.CustID ?? "";
    customerRow["Name"] = customer.Name ?? "";
    customerRow["CustNum"] = customer.CustNum;
    customerRow["Address1"] = customer.Address1 ?? "";
    customerRow["Address2"] = customer.Address2 ?? "";
    customerRow["Address3"] = customer.Address3 ?? "";
    customerRow["City"] = customer.City ?? "";
    customerRow["State"] = customer.State ?? "";
    customerRow["Zip"] = customer.Zip ?? "";
    customerRow["Country"] = customer.Country ?? "";
    customerRow["PhoneNum"] = customer.PhoneNum ?? "";
    customerRow["FaxNum"] = customer.FaxNum ?? "";
    customerRow["EMailAddress"] = customer.EMailAddress ?? "";
    customerRow["Active"] = !customer.Inactive; // Inactive field is inverted
    
    customerTable.Rows.Add(customerRow);
}

// Return the populated dataset
outputDataSet = dataSet;
