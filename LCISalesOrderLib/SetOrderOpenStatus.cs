/*
 * =====================================================
 * LCI Sales Order Library - Set Order Open/Close Status
 * =====================================================
 *
 * Purpose: Opens or closes a sales order by setting the OrderHed.OpenOrder field
 *
 * Parameters:
 *   - SalesOrderNum (int): Sales order number to update
 *   - setToOpen (bool): true to open order, false to close order
 *
 * Returns:
 *   - result (string): "OK - Sales order opened/closed" if successful, error message if failed
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Retrieve the sales order header
 *   2. Update the OpenOrder field (true = open, false = closed)
 *   3. Save changes via Epicor service
 *
 * Note: OrderHed.OpenOrder controls order status in Epicor
 *
 * Usage Example:
 *   SalesOrderNum = 12345;
 *   setToOpen = false;  // Close the order
 *   // Execute function
 *   // result will be "OK - Sales order closed" or error message
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = "Error";                      // Default to error state

// Execute order status update using Epicor service
this.CallService<Erp.Contracts.SalesOrderSvcContract>(svc =>
{
    try
    {
        // Retrieve the complete sales order dataset
        var ts = svc.GetByID(SalesOrderNum);

        // Verify sales order exists and has header data
        if (ts != null && ts.OrderHed.Count > 0)
        {
            try
            {
                // Get the order header record
                var orderHedRow = ts.OrderHed.FirstOrDefault();

                if (orderHedRow != null)
                {
                    // Mark header row for update (required by Epicor)
                    orderHedRow.RowMod = "U";

                    // Update the OpenOrder field to control order status
                    // true = order is open (can be modified/shipped)
                    // false = order is closed (locked from changes)
                    orderHedRow.OpenOrder = setToOpen;

                    // Commit the status change to Epicor
                    svc.Update(ref ts);

                    // Success - generate descriptive result message
                    string status = setToOpen ? "opened" : "closed";
                    result = "OK - Sales order " + status;
                }
                else
                {
                    // Header record not found in dataset
                    result = "Error: Order header not found";
                }
            }
            catch (Exception ex)
            {
                // Status update operation failed
                result = "Error: Failed to update sales order status - " + ex.Message;
            }
        }
        else
        {
            // Sales order not found or empty dataset
            result = "Error: Sales order not found";
        }
    }
    catch (Exception ex)
    {
        // Failed to retrieve sales order from Epicor
        result = "Error: Failed to retrieve sales order - " + ex.Message;
    }
});
