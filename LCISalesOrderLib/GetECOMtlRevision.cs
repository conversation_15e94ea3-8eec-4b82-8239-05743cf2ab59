/*
 * =====================================================
 * LCI Sales Order Library - Get ECO Material Revision
 * =====================================================
 *
 * Purpose: Retrieves the material revision number from ECOMtl table based on specific criteria
 *
 * Parameters:
 *   - PartNum (string): Part number to search for
 *   - MtlSeq (int): Material sequence number
 *   - GroupID (string): Group ID to match
 *
 * Returns:
 *   - result (string): MtlRevisionNum if found, error message if not found
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Query ECOMtl table with provided parameters
 *   2. Return the MtlRevisionNum field if record found
 *   3. Return error message if no matching record found
 *
 * Usage Example:
 *   PartNum = "ABC123";
 *   MtlSeq = 10;
 *   GroupID = "GRP001";
 *   // Execute function
 *   // result will be revision number or error message
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = "Error: Material revision not found";  // Default to error state

try
{
    // Query ECOMtl table for the specific material record
    var ecoMaterial = Db.ECOMtl
        .FirstOrDefault(m => m.Company == companyID &&
                            m.PartNum == PartNum &&
                            m.MtlSeq == MtlSeq &&
                            m.GroupID == GroupID);

    // Check if material record was found
    if (ecoMaterial != null)
    {
        // Return the material revision number
        result = ecoMaterial.MtlRevisionNum ?? "";  // Handle potential null values
    }
    else
    {
        // No matching record found - provide detailed error message
        result = "Error: No ECOMtl record found for PartNum='" + PartNum + 
                "', MtlSeq=" + MtlSeq + ", GroupID='" + GroupID + "'";
    }
}
catch (Exception ex)
{
    // Database query failed - return error with exception details
    result = "Error: Failed to query ECOMtl table - " + ex.Message;
}
