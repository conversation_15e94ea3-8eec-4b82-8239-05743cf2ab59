/*
 * =====================================================
 * LCI Sales Order Library - Check for Specific Part
 * =====================================================
 *
 * Purpose: Checks if a specific part number exists in any line of a sales order
 *
 * Parameters:
 *   - SalesOrderNum (int): Sales order number to search in
 *   - partNum (string): Part number to search for
 *
 * Returns:
 *   - result (bool): true if part found, false if not found
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Usage Example:
 *   SalesOrderNum = 12345;
 *   partNum = "ABC123";
 *   // Execute function
 *   // result will be true/false
 */

// Initialize company ID (hardcoded for LCI)
string companyID = "162250";
result = false;

// Search for the specified part number in any line of the sales order
var orderLine = Db.OrderDtl
    .FirstOrDefault(d => d.Company == companyID &&
                         d.OrderNum == SalesOrderNum &&
                         d.PartNum == partNum);

// Set result based on whether the part was found
if (orderLine != null)
{
    result = true;  // Part found in sales order
}