/*
 * =====================================================
 * LCI Sales Order Library - Get Price From Quote
 * =====================================================
 *
 * Purpose: Updates sales order line prices for part "5865000" using prices from linked quotes
 *
 * Parameters:
 *   - SalesOrderNum (int): Sales order number to update
 *
 * Returns:
 *   - result (string): "OK" if successful, error message if failed
 *
 * Company: Hardcoded to 162250 (LCI)
 * Part Number: Hardcoded to "5865000" (specific LCI part)
 *
 * Process:
 *   1. Find all order lines with part "5865000"
 *   2. For each line, get the quote price from the linked quote
 *   3. Update all lines in a single Epicor service call
 *
 * Usage Example:
 *   SalesOrderNum = 12345;
 *   // Execute function
 *   // result will be "OK" or error message
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = "Error";                      // Default to error state
int updatedLinesCount = 0;             // Track how many lines were updated

// Find ALL order lines with the specific part number "5865000"
var orderLines = Db.OrderDtl
    .Where(d => d.Company == companyID &&
                d.OrderNum == SalesOrderNum &&
                d.PartNum == "5865000")
    .ToList();

// Dictionary to store pending price updates: OrderLine -> NewPrice
var lineUpdates = new Dictionary<int, decimal>();

// Process order lines if any were found
if (orderLines.Any())
{
    // ===== PHASE 1: Collect all price updates from quotes =====
    foreach (var orderLine in orderLines)
    {
        // Only process lines that have a valid quote reference
        if (orderLine.QuoteNum != 0)
        {
            // Find the corresponding quote line detail
            var quoteLine = Db.QuoteDtl
                .FirstOrDefault(qd => qd.Company == companyID &&
                                      qd.QuoteNum == orderLine.QuoteNum &&
                                      qd.QuoteLine == orderLine.QuoteLine);

            // If quote line found, store the price update for later processing
            if (quoteLine != null)
            {
                lineUpdates[orderLine.OrderLine] = quoteLine.DocExpUnitPrice;
            }
        }
    }

    // ===== PHASE 2: Apply all price updates via Epicor service =====
    if (lineUpdates.Any())
    {
        // Use Epicor Sales Order service to update prices
        this.CallService<Erp.Contracts.SalesOrderSvcContract>(svc =>
        {
            try
            {
                var ts = new Erp.Tablesets.SalesOrderTableset();

                try
                {
                    // Retrieve the complete sales order dataset
                    ts = svc.GetByID(SalesOrderNum);

                    // Apply price updates to each order line
                    foreach (var update in lineUpdates)
                    {
                        // Find the specific order line to update
                        var orderDtlRow = ts.OrderDtl.FirstOrDefault(od => od.OrderLine == update.Key);

                        if (orderDtlRow != null)
                        {
                            // Mark row for update (required by Epicor)
                            orderDtlRow.RowMod = "U";

                            // Update both document and base currency prices
                            orderDtlRow.DocUnitPrice = update.Value;  // Document currency price
                            orderDtlRow.UnitPrice = update.Value;     // Base currency price

                            updatedLinesCount++;
                        }
                    }

                    // Commit all changes to Epicor in a single transaction
                    svc.Update(ref ts);

                    // Success - all price updates completed
                    result = "OK";
                }
                catch (Exception ex)
                {
                    result = "Error: Sales order not found or update failed - " + ex.Message;
                }
            }
            catch (Exception ex)
            {
                result = "Error: Failed to update sales order - " + ex.Message;
            }
        });
    }
    else
    {
        // No valid quote prices found to update
        result = "Error: No lines found with valid quote prices to update";
    }
}
else
{
    // No order lines found with the specified part number
    result = "Error: No order lines found with part number 5865000";
}