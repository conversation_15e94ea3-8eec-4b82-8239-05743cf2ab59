/*
 * =====================================================
 * LCI Sales Order Library - Get Picked Lines Count Statistics
 * =====================================================
 *
 * Purpose: Returns comprehensive statistics on picked lines and upcoming due dates
 *
 * Parameters: None (queries all relevant lines)
 *
 * Returns:
 *   - result (System.Data.DataSet): DataSet containing statistics table with counts
 *   - errorMessage (string): Error message if query fails, empty string if successful
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Statistics Included:
 *   1. TodaysPickedLines - Lines fully completed today (based on *-CUS transactions)
 *   2. YesterdaysPickedLines - Lines fully completed yesterday (based on *-CUS transactions)
 *   3. ThisWeeksPickedLines - Lines fully completed this week (based on *-CUS transactions)
 *   4. TodaysDueLines - Open lines with NeedByDate = today
 *   5. TomorrowsDueLines - Open lines with NeedByDate = tomorrow
 *   6. ThisWeeksDueLines - Open lines with NeedByDate = this week (includes today/tomorrow)
 *   7. OverdueLines - Open lines overdue (due before today)
 *   8. OverduePercentage - Percentage of open lines with dates that are overdue
 *
 * Tables Returned:
 *   1. "Statistics" - Main statistics table with overall counts
 *   2. "UserStatistics" - Per-user breakdown of lines picked (today, yesterday, this week)
 *   3. "Debug" - Comprehensive debugging information (if enabled)
 *
 * Note: Categories are NOT mutually exclusive - yesterday counts towards week total, etc.
 *       A line counts as "1" only when fully completed (total shipped qty >= order line qty)
 *       Picking counts are based on PartTran transactions ending in "-CUS" (all customer transactions),
 *       with quantity validation to ensure only fully completed lines are counted
 *       All due date statistics count individual order lines, not orders
 *       Kit components are counted individually (future enhancement)
 *
 * Usage Example:
 *   // Execute function
 *   // result DataSet will contain one table with 6 rows of statistics
 *   // errorMessage will be empty string if successful
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = new DataSet("PickedLinesStatistics");  // Initialize result DataSet
errorMessage = "";                     // Initialize error message as empty (success)

try
{
    // Create statistics table
    DataTable statsTable = new DataTable("Statistics");
    statsTable.Columns.Add("Category", typeof(string));
    statsTable.Columns.Add("Count", typeof(decimal));
    statsTable.Columns.Add("Description", typeof(string));

    // Get date ranges for filtering - calculate all dates upfront to avoid LINQ translation issues
    DateTime today = DateTime.Today;
    DateTime yesterday = DateTime.Today.AddDays(-1);
    DateTime tomorrow = DateTime.Today.AddDays(1);

    // Fix week calculation - Monday = start of week (more business-appropriate)
    int daysFromMonday = ((int)DateTime.Today.DayOfWeek + 6) % 7; // Monday = 0, Tuesday = 1, etc.
    DateTime weekStart = DateTime.Today.AddDays(-daysFromMonday);  // Start of this week (Monday)
    DateTime weekEnd = weekStart.AddDays(7);  // End of this week (next Monday)

    DateTime dayAfterTomorrow = DateTime.Today.AddDays(2);
    DateTime monthStart = DateTime.Today.AddDays(-30);
    DateTime monthEnd = DateTime.Today.AddDays(30);

    // ===== PICKED LINES STATISTICS =====
    // Using PartTran table to track actual picking/shipping transactions (all *-CUS types)
    // *-CUS = Any transaction type ending in "-CUS" represents customer fulfillment
    // Each OrderNum/OrderLine combination is counted only once per day to prevent double counting
    // Only count lines where the total shipped quantity >= order line quantity (fully completed)

    // 1. Today's picked lines - count distinct order lines that were fully completed today
    var todaysPickedLines = (from pt in Db.PartTran
                            join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                            where pt.Company == companyID &&
                                  pt.TranType.EndsWith("-CUS") &&
                                  pt.OrderNum > 0 &&
                                  pt.OrderLine > 0 &&
                                  pt.TranDate.HasValue &&
                                  pt.TranDate.Value >= today &&
                                  pt.TranDate.Value < tomorrow
                            group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty } into g
                            where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                            select new { g.Key.OrderNum, g.Key.OrderLine })
                           .Distinct()
                           .Count();

    // 2. Yesterday's picked lines - count distinct order lines that were fully completed yesterday
    var yesterdaysPickedLines = (from pt in Db.PartTran
                                join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                                where pt.Company == companyID &&
                                      pt.TranType.EndsWith("-CUS") &&
                                      pt.OrderNum > 0 &&
                                      pt.OrderLine > 0 &&
                                      pt.TranDate.HasValue &&
                                      pt.TranDate.Value >= yesterday &&
                                      pt.TranDate.Value < today
                                group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty } into g
                                where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                                select new { g.Key.OrderNum, g.Key.OrderLine })
                               .Distinct()
                               .Count();

    // 3. This week's picked lines - count distinct order lines that were fully completed this week
    var thisWeeksPickedLines = (from pt in Db.PartTran
                               join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                               where pt.Company == companyID &&
                                     pt.TranType.EndsWith("-CUS") &&
                                     pt.OrderNum > 0 &&
                                     pt.OrderLine > 0 &&
                                     pt.TranDate.HasValue &&
                                     pt.TranDate.Value >= weekStart &&
                                     pt.TranDate.Value < weekEnd
                               group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty } into g
                               where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                               select new { g.Key.OrderNum, g.Key.OrderLine })
                              .Distinct()
                              .Count();

    // ===== DUE DATE STATISTICS =====

    // 4. Today's due lines (open lines with NeedByDate = today)
    var todaysDueLines = (from oh in Db.OrderHed
                         join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                         where oh.Company == companyID &&
                               oh.OpenOrder == true &&
                               od.OpenLine == true &&
                               od.NeedByDate.HasValue &&
                               od.NeedByDate.Value >= today &&
                               od.NeedByDate.Value < tomorrow
                         select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                        .Distinct()
                        .Count();

    // 5. Tomorrow's due lines
    var tomorrowsDueLines = (from oh in Db.OrderHed
                            join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                            where oh.Company == companyID &&
                                  oh.OpenOrder == true &&
                                  od.OpenLine == true &&
                                  od.NeedByDate.HasValue &&
                                  od.NeedByDate.Value >= tomorrow &&
                                  od.NeedByDate.Value < dayAfterTomorrow
                            select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                           .Distinct()
                           .Count();

    // 6. This week's due lines (includes today and tomorrow)
    var thisWeeksDueLines = (from oh in Db.OrderHed
                            join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                            where oh.Company == companyID &&
                                  oh.OpenOrder == true &&
                                  od.OpenLine == true &&
                                  od.NeedByDate.HasValue &&
                                  od.NeedByDate.Value >= weekStart &&
                                  od.NeedByDate.Value < weekEnd
                            select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                           .Distinct()
                           .Count();

    // ===== OVERDUE STATISTICS =====

    // Calculate overdue lines (due before today)
    var overdueDueLines = (from oh in Db.OrderHed
                          join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                          where oh.Company == companyID &&
                                oh.OpenOrder == true &&
                                od.OpenLine == true &&
                                od.NeedByDate.HasValue &&
                                od.NeedByDate.Value < today
                          select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                         .Distinct()
                         .Count();

    // Calculate percentage of overdue lines (overdue / total lines with dates)
    var totalLinesWithDates = (from oh in Db.OrderHed
                              join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                              where oh.Company == companyID &&
                                    oh.OpenOrder == true &&
                                    od.OpenLine == true &&
                                    od.NeedByDate.HasValue
                              select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                             .Distinct()
                             .Count();

    decimal overduePercentage = totalLinesWithDates > 0 ? (decimal)overdueDueLines / totalLinesWithDates : 0;

    // Add statistics to table
    statsTable.Rows.Add("TodaysPickedLines", todaysPickedLines, "Lines fully completed today (shipped qty >= order qty)");
    statsTable.Rows.Add("YesterdaysPickedLines", yesterdaysPickedLines, "Lines fully completed yesterday (shipped qty >= order qty)");
    statsTable.Rows.Add("ThisWeeksPickedLines", thisWeeksPickedLines, "Lines fully completed this week (shipped qty >= order qty)");
    statsTable.Rows.Add("TodaysDueLines", todaysDueLines, "Open lines due today");
    statsTable.Rows.Add("TomorrowsDueLines", tomorrowsDueLines, "Open lines due tomorrow");
    statsTable.Rows.Add("ThisWeeksDueLines", thisWeeksDueLines, "Open lines due this week (includes today/tomorrow)");
    statsTable.Rows.Add("OverdueLines", overdueDueLines, "Open lines overdue (due before today)");
    statsTable.Rows.Add("OverduePercentage", Math.Round(overduePercentage, 1), "Percentage of open lines with dates that are overdue");

    // Add statistics table to result DataSet
    result.Tables.Add(statsTable);

    // ===== PER-USER BREAKDOWN TABLE =====
    // Create per-user breakdown table showing lines picked by each user
    DataTable userStatsTable = new DataTable("UserStatistics");
    userStatsTable.Columns.Add("UserID", typeof(string));
    userStatsTable.Columns.Add("UserName", typeof(string));
    userStatsTable.Columns.Add("TodaysPickedLines", typeof(int));
    userStatsTable.Columns.Add("YesterdaysPickedLines", typeof(int));
    userStatsTable.Columns.Add("ThisWeeksPickedLines", typeof(int));

    // Get user statistics for today
    var todayUserStats = (from pt in Db.PartTran
                         join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                         where pt.Company == companyID &&
                               pt.TranType.EndsWith("-CUS") &&
                               pt.OrderNum > 0 &&
                               pt.OrderLine > 0 &&
                               pt.TranDate.HasValue &&
                               pt.TranDate.Value >= today &&
                               pt.TranDate.Value < tomorrow &&
                               !string.IsNullOrEmpty(pt.EntryPerson)
                         group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty, pt.EntryPerson } into g
                         where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                         group g by g.Key.EntryPerson into userGroup
                         select new {
                             UserID = userGroup.Key,
                             Count = userGroup.Select(g => new { g.Key.OrderNum, g.Key.OrderLine }).Distinct().Count()
                         }).ToList();

    // Get user statistics for yesterday
    var yesterdayUserStats = (from pt in Db.PartTran
                             join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                             where pt.Company == companyID &&
                                   pt.TranType.EndsWith("-CUS") &&
                                   pt.OrderNum > 0 &&
                                   pt.OrderLine > 0 &&
                                   pt.TranDate.HasValue &&
                                   pt.TranDate.Value >= yesterday &&
                                   pt.TranDate.Value < today &&
                                   !string.IsNullOrEmpty(pt.EntryPerson)
                             group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty, pt.EntryPerson } into g
                             where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                             group g by g.Key.EntryPerson into userGroup
                             select new {
                                 UserID = userGroup.Key,
                                 Count = userGroup.Select(g => new { g.Key.OrderNum, g.Key.OrderLine }).Distinct().Count()
                             }).ToList();

    // Get user statistics for this week
    var weekUserStats = (from pt in Db.PartTran
                        join od in Db.OrderDtl on new { pt.Company, pt.OrderNum, pt.OrderLine } equals new { od.Company, od.OrderNum, od.OrderLine }
                        where pt.Company == companyID &&
                              pt.TranType.EndsWith("-CUS") &&
                              pt.OrderNum > 0 &&
                              pt.OrderLine > 0 &&
                              pt.TranDate.HasValue &&
                              pt.TranDate.Value >= weekStart &&
                              pt.TranDate.Value < weekEnd &&
                              !string.IsNullOrEmpty(pt.EntryPerson)
                        group pt by new { pt.OrderNum, pt.OrderLine, od.OrderQty, pt.EntryPerson } into g
                        where g.Sum(pt => pt.TranQty) >= g.Key.OrderQty
                        group g by g.Key.EntryPerson into userGroup
                        select new {
                            UserID = userGroup.Key,
                            Count = userGroup.Select(g => new { g.Key.OrderNum, g.Key.OrderLine }).Distinct().Count()
                        }).ToList();

    // Get all unique users from all time periods
    var allUsers = todayUserStats.Select(u => u.UserID)
                  .Union(yesterdayUserStats.Select(u => u.UserID))
                  .Union(weekUserStats.Select(u => u.UserID))
                  .Distinct()
                  .ToList();

    // Populate user statistics table
    foreach (var userID in allUsers)
    {
        var todayCount = todayUserStats.FirstOrDefault(u => u.UserID == userID)?.Count ?? 0;
        var yesterdayCount = yesterdayUserStats.FirstOrDefault(u => u.UserID == userID)?.Count ?? 0;
        var weekCount = weekUserStats.FirstOrDefault(u => u.UserID == userID)?.Count ?? 0;

        userStatsTable.Rows.Add(userID, userID, todayCount, yesterdayCount, weekCount);
    }

    // Add user statistics table to result DataSet
    result.Tables.Add(userStatsTable);

    // ===== DEBUG TABLE =====
    // Create comprehensive debug table to diagnose issues
    DataTable debugTable = new DataTable("Debug");
    debugTable.Columns.Add("DebugCategory", typeof(string));
    debugTable.Columns.Add("DebugValue", typeof(string));
    debugTable.Columns.Add("DebugDescription", typeof(string));

    // Date range debugging
    debugTable.Rows.Add("DateRange", today.ToString("yyyy-MM-dd"), "Today's date");
    debugTable.Rows.Add("DateRange", yesterday.ToString("yyyy-MM-dd"), "Yesterday's date");
    debugTable.Rows.Add("DateRange", tomorrow.ToString("yyyy-MM-dd"), "Tomorrow's date");
    debugTable.Rows.Add("DateRange", weekStart.ToString("yyyy-MM-dd"), "Week start date");
    debugTable.Rows.Add("DateRange", weekEnd.ToString("yyyy-MM-dd"), "Week end date");
    debugTable.Rows.Add("DateRange", companyID, "Company ID being used");

    // PartTran table debugging
    var totalPartTranRecords = Db.PartTran.Where(pt => pt.Company == companyID).Count();
    debugTable.Rows.Add("PartTran", totalPartTranRecords.ToString(), "Total PartTran records for company");

    var allCusRecords = Db.PartTran.Where(pt => pt.Company == companyID && pt.TranType.EndsWith("-CUS")).Count();
    debugTable.Rows.Add("PartTran", allCusRecords.ToString(), "Total *-CUS transaction records");

    var allCusWithOrderNum = Db.PartTran.Where(pt => pt.Company == companyID && pt.TranType.EndsWith("-CUS") && pt.OrderNum > 0).Count();
    debugTable.Rows.Add("PartTran", allCusWithOrderNum.ToString(), "*-CUS records with OrderNum > 0");

    var allCusWithOrderLine = Db.PartTran.Where(pt => pt.Company == companyID && pt.TranType.EndsWith("-CUS") && pt.OrderNum > 0 && pt.OrderLine > 0).Count();
    debugTable.Rows.Add("PartTran", allCusWithOrderLine.ToString(), "*-CUS records with OrderNum > 0 and OrderLine > 0");

    var allCusWithDate = Db.PartTran.Where(pt => pt.Company == companyID && pt.TranType.EndsWith("-CUS") && pt.OrderNum > 0 && pt.OrderLine > 0 && pt.TranDate.HasValue).Count();
    debugTable.Rows.Add("PartTran", allCusWithDate.ToString(), "*-CUS records with valid dates");

    // Check for different transaction types that might be used for picking
    var allTranTypes = Db.PartTran.Where(pt => pt.Company == companyID && pt.OrderNum > 0)
        .Select(pt => pt.TranType).Distinct().Take(20).ToList();
    debugTable.Rows.Add("TranTypes", string.Join(", ", allTranTypes), "All transaction types with order numbers (first 20)");

    // Sample recent PartTran records
    var recentPartTrans = Db.PartTran.Where(pt => pt.Company == companyID && pt.OrderNum > 0)
        .OrderByDescending(pt => pt.TranDate).Take(5).ToList();
    for (int i = 0; i < recentPartTrans.Count; i++)
    {
        var pt = recentPartTrans[i];
        debugTable.Rows.Add("RecentTrans",
            $"Order:{pt.OrderNum} Line:{pt.OrderLine} Type:{pt.TranType} Date:{pt.TranDate?.ToString("yyyy-MM-dd")}",
            $"Recent PartTran record #{i + 1}");
    }

    // OrderHed/OrderDtl debugging for due dates
    var totalOrderHed = Db.OrderHed.Where(oh => oh.Company == companyID).Count();
    debugTable.Rows.Add("Orders", totalOrderHed.ToString(), "Total OrderHed records for company");

    var openOrderHed = Db.OrderHed.Where(oh => oh.Company == companyID && oh.OpenOrder == true).Count();
    debugTable.Rows.Add("Orders", openOrderHed.ToString(), "Open OrderHed records");

    var totalOrderDtl = Db.OrderDtl.Where(od => od.Company == companyID).Count();
    debugTable.Rows.Add("Orders", totalOrderDtl.ToString(), "Total OrderDtl records for company");

    var openOrderDtl = Db.OrderDtl.Where(od => od.Company == companyID && od.OpenLine == true).Count();
    debugTable.Rows.Add("Orders", openOrderDtl.ToString(), "Open OrderDtl records");

    var orderDtlWithNeedBy = Db.OrderDtl.Where(od => od.Company == companyID && od.OpenLine == true && od.NeedByDate.HasValue).Count();
    debugTable.Rows.Add("Orders", orderDtlWithNeedBy.ToString(), "Open OrderDtl records with NeedByDate");

    // Sample recent orders with need by dates
    var recentOrders = (from oh in Db.OrderHed
                       join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                       where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true && od.NeedByDate.HasValue
                       orderby od.NeedByDate descending
                       select new { oh.OrderNum, od.OrderLine, od.NeedByDate })
                       .Take(5).ToList();

    for (int i = 0; i < recentOrders.Count; i++)
    {
        var order = recentOrders[i];
        debugTable.Rows.Add("RecentOrders",
            $"Order:{order.OrderNum} Line:{order.OrderLine} NeedBy:{order.NeedByDate?.ToString("yyyy-MM-dd")}",
            $"Recent open order with NeedByDate #{i + 1}");
    }

    // Check if there are any orders due in a wider date range
    var ordersThisMonth = (from oh in Db.OrderHed
                          join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                          where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                                od.NeedByDate.HasValue && od.NeedByDate.Value >= monthStart && od.NeedByDate.Value <= monthEnd
                          select new { oh.OrderNum, od.OrderLine })
                          .Distinct().Count();
    debugTable.Rows.Add("Orders", ordersThisMonth.ToString(), "Open orders due within +/- 30 days of today");

    // Additional due date debugging
    var ordersToday = (from oh in Db.OrderHed
                      join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                      where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                            od.NeedByDate.HasValue && od.NeedByDate.Value >= today && od.NeedByDate.Value < tomorrow
                      select new { oh.OrderNum, od.OrderLine, od.NeedByDate })
                      .Take(10).ToList();

    for (int i = 0; i < ordersToday.Count; i++)
    {
        var order = ordersToday[i];
        debugTable.Rows.Add("TodayDue",
            $"Order:{order.OrderNum} Line:{order.OrderLine} NeedBy:{order.NeedByDate?.ToString("yyyy-MM-dd")}",
            $"Order due today #{i + 1}");
    }

    var ordersTomorrow = (from oh in Db.OrderHed
                         join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                         where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                               od.NeedByDate.HasValue && od.NeedByDate.Value >= tomorrow && od.NeedByDate.Value < dayAfterTomorrow
                         select new { oh.OrderNum, od.OrderLine, od.NeedByDate })
                         .Take(10).ToList();

    for (int i = 0; i < ordersTomorrow.Count; i++)
    {
        var order = ordersTomorrow[i];
        debugTable.Rows.Add("TomorrowDue",
            $"Order:{order.OrderNum} Line:{order.OrderLine} NeedBy:{order.NeedByDate?.ToString("yyyy-MM-dd")}",
            $"Order due tomorrow #{i + 1}");
    }

    // Check for orders due this week but not today/tomorrow
    var ordersThisWeekOnly = (from oh in Db.OrderHed
                             join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                             where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                                   od.NeedByDate.HasValue && od.NeedByDate.Value >= weekStart && od.NeedByDate.Value < weekEnd &&
                                   !(od.NeedByDate.Value >= today && od.NeedByDate.Value < dayAfterTomorrow)
                             select new { oh.OrderNum, od.OrderLine, od.NeedByDate })
                             .Take(10).ToList();

    for (int i = 0; i < ordersThisWeekOnly.Count; i++)
    {
        var order = ordersThisWeekOnly[i];
        debugTable.Rows.Add("WeekDue",
            $"Order:{order.OrderNum} Line:{order.OrderLine} NeedBy:{order.NeedByDate?.ToString("yyyy-MM-dd")}",
            $"Order due this week (not today/tomorrow) #{i + 1}");
    }

    // ===== DUE DATE DEBUGGING =====
    // Verify the math: today + tomorrow should be <= this week
    debugTable.Rows.Add("DueDateMath", $"Today({todaysDueLines}) + Tomorrow({tomorrowsDueLines}) = {todaysDueLines + tomorrowsDueLines}", "Today + Tomorrow should be <= This Week");
    debugTable.Rows.Add("DueDateMath", $"This Week Total: {thisWeeksDueLines}", "This week total for comparison");

    // Check if we're missing orders due to date format or null issues
    var ordersWithNullDates = Db.OrderDtl.Where(od => od.Company == companyID && od.OpenLine == true && !od.NeedByDate.HasValue).Count();
    debugTable.Rows.Add("DueDateDebug", ordersWithNullDates.ToString(), "Open order lines with NULL NeedByDate");

    // Check orders due in the next 7 days to see if there's a pattern
    DateTime next7Days = today.AddDays(7);
    var ordersNext7Days = (from oh in Db.OrderHed
                          join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                          where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                                od.NeedByDate.HasValue && od.NeedByDate.Value >= today && od.NeedByDate.Value < next7Days
                          select new { oh.OrderNum, od.OrderLine })
                          .Distinct().Count();
    debugTable.Rows.Add("DueDateDebug", ordersNext7Days.ToString(), "Open orders due in next 7 days");

    // Check orders due in the past (overdue)
    var overdueOrders = (from oh in Db.OrderHed
                        join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                        where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                              od.NeedByDate.HasValue && od.NeedByDate.Value < today
                        select new { oh.OrderNum, od.OrderLine })
                        .Distinct().Count();
    debugTable.Rows.Add("DueDateDebug", overdueOrders.ToString(), "Overdue orders (due before today)");

    // Sample of orders due in different time ranges
    var sampleOrdersNext30Days = (from oh in Db.OrderHed
                                 join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                                 where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                                       od.NeedByDate.HasValue && od.NeedByDate.Value >= today && od.NeedByDate.Value <= monthEnd
                                 orderby od.NeedByDate
                                 select new { oh.OrderNum, od.OrderLine, od.NeedByDate, od.OrderQty })
                                 .Take(20).ToList();

    for (int i = 0; i < Math.Min(10, sampleOrdersNext30Days.Count); i++)
    {
        var order = sampleOrdersNext30Days[i];
        debugTable.Rows.Add("Next30DaysSample",
            $"Order:{order.OrderNum} Line:{order.OrderLine} Due:{order.NeedByDate?.ToString("yyyy-MM-dd")} Qty:{order.OrderQty}",
            $"Sample order due in next 30 days #{i + 1}");
    }

    // Check if there are orders with different date patterns
    var dateRangeBreakdown = (from oh in Db.OrderHed
                             join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                             where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true && od.NeedByDate.HasValue
                             group od by new {
                                 Range = od.NeedByDate.Value < today ? "Overdue" :
                                        od.NeedByDate.Value >= today && od.NeedByDate.Value < tomorrow ? "Today" :
                                        od.NeedByDate.Value >= tomorrow && od.NeedByDate.Value < dayAfterTomorrow ? "Tomorrow" :
                                        od.NeedByDate.Value >= weekStart && od.NeedByDate.Value < weekEnd ? "This Week" :
                                        od.NeedByDate.Value >= next7Days && od.NeedByDate.Value < monthEnd ? "Next 30 Days" :
                                        "Future"
                             } into g
                             select new { g.Key.Range, Count = g.Count() })
                             .ToList();

    foreach (var range in dateRangeBreakdown.OrderBy(r => r.Range))
    {
        debugTable.Rows.Add("DateRangeBreakdown", $"{range.Range}: {range.Count}", "Orders by due date range");
    }

    // ===== KIT ANALYSIS =====
    // Check for kit-related information to understand kit structure

    // Look for KIT-CUS transactions to understand kit handling
    var kitCusTransactions = Db.PartTran
        .Where(pt => pt.Company == companyID && pt.TranType == "KIT-CUS" && pt.OrderNum > 0)
        .Take(10)
        .Select(pt => new { pt.OrderNum, pt.OrderLine, pt.PartNum, pt.TranQty, pt.TranDate })
        .ToList();

    debugTable.Rows.Add("KitAnalysis", kitCusTransactions.Count.ToString(), "Sample KIT-CUS transactions found");

    for (int i = 0; i < Math.Min(5, kitCusTransactions.Count); i++)
    {
        var kit = kitCusTransactions[i];
        debugTable.Rows.Add("KitSample",
            $"Order:{kit.OrderNum} Line:{kit.OrderLine} Part:{kit.PartNum} Qty:{kit.TranQty} Date:{kit.TranDate?.ToString("yyyy-MM-dd")}",
            $"Sample KIT-CUS transaction #{i + 1}");
    }

    // Check for parts that might be kits by looking at Part table
    var potentialKitParts = Db.Part
        .Where(p => p.Company == companyID && (p.TypeCode == "K" || p.PartNum.Contains("KIT")))
        .Take(10)
        .Select(p => new { p.PartNum, p.TypeCode, p.PartDescription })
        .ToList();

    debugTable.Rows.Add("KitAnalysis", potentialKitParts.Count.ToString(), "Potential kit parts found (TypeCode='K' or contains 'KIT')");

    for (int i = 0; i < Math.Min(3, potentialKitParts.Count); i++)
    {
        var kit = potentialKitParts[i];
        debugTable.Rows.Add("KitParts",
            $"Part:{kit.PartNum} Type:{kit.TypeCode} Desc:{kit.PartDescription}",
            $"Potential kit part #{i + 1}");
    }

    // Check for any tables that might contain kit component information
    try
    {
        // Try to find PartMtl records (Bill of Materials) which might show kit components
        var bomRecords = Db.PartMtl
            .Where(pm => pm.Company == companyID)
            .Take(5)
            .Select(pm => new { pm.PartNum, pm.MtlPartNum, pm.QtyPer })
            .ToList();

        debugTable.Rows.Add("KitAnalysis", bomRecords.Count.ToString(), "PartMtl (BOM) records found - may indicate kit components");

        for (int i = 0; i < Math.Min(3, bomRecords.Count); i++)
        {
            var bom = bomRecords[i];
            debugTable.Rows.Add("BOMSample",
                $"Parent:{bom.PartNum} Component:{bom.MtlPartNum} QtyPer:{bom.QtyPer}",
                $"Sample BOM record #{i + 1}");
        }
    }
    catch
    {
        debugTable.Rows.Add("KitAnalysis", "0", "PartMtl table not accessible or doesn't exist");
    }

    // Additional insights
    debugTable.Rows.Add("DueDateInsights", $"Total Open Lines: {openOrderDtl}", "Total open order lines");
    debugTable.Rows.Add("DueDateInsights", $"Lines with NeedByDate: {orderDtlWithNeedBy}", "Lines that have due dates");
    debugTable.Rows.Add("DueDateInsights", $"Lines without NeedByDate: {ordersWithNullDates}", "Lines missing due dates");
    debugTable.Rows.Add("DueDateInsights", $"Overdue Percentage: {(overdueOrders * 1.0 / orderDtlWithNeedBy):F1}", "Percentage of dated orders that are overdue (as decimal)");

    // Sample overdue orders to understand the pattern
    var sampleOverdueOrders = (from oh in Db.OrderHed
                              join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                              where oh.Company == companyID && oh.OpenOrder == true && od.OpenLine == true &&
                                    od.NeedByDate.HasValue && od.NeedByDate.Value < today
                              orderby od.NeedByDate descending
                              select new { oh.OrderNum, od.OrderLine, od.NeedByDate, od.OrderQty })
                              .Take(5).ToList();

    for (int i = 0; i < sampleOverdueOrders.Count; i++)
    {
        var order = sampleOverdueOrders[i];
        int daysOverdue = (today - order.NeedByDate.Value).Days;
        debugTable.Rows.Add("OverdueSample",
            $"Order:{order.OrderNum} Line:{order.OrderLine} Due:{order.NeedByDate?.ToString("yyyy-MM-dd")} ({daysOverdue} days overdue)",
            $"Sample overdue order #{i + 1}");
    }

    // ===== TRANSACTION ANALYSIS =====
    // Check transaction counts by date to verify the new *-CUS approach

    // Count all customer transactions (*-CUS) by date with distinct OrderNum/OrderLine to prevent double counting
    var todayAllCustomerTrans = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType.EndsWith("-CUS") &&
                    pt.OrderNum > 0 && pt.OrderLine > 0 &&
                    pt.TranDate.HasValue && pt.TranDate.Value >= today && pt.TranDate.Value < tomorrow)
        .Select(pt => new { OrderNum = pt.OrderNum, OrderLine = pt.OrderLine })
        .Distinct().Count();

    var yesterdayAllCustomerTrans = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType.EndsWith("-CUS") &&
                    pt.OrderNum > 0 && pt.OrderLine > 0 &&
                    pt.TranDate.HasValue && pt.TranDate.Value >= yesterday && pt.TranDate.Value < today)
        .Select(pt => new { OrderNum = pt.OrderNum, OrderLine = pt.OrderLine })
        .Distinct().Count();

    debugTable.Rows.Add("TransAnalysis", $"Today All *-CUS Trans (Any Qty): {todayAllCustomerTrans}", "All *-CUS transaction types today (any quantity)");
    debugTable.Rows.Add("TransAnalysis", $"Yesterday All *-CUS Trans (Any Qty): {yesterdayAllCustomerTrans}", "All *-CUS transaction types yesterday (any quantity)");
    debugTable.Rows.Add("TransAnalysis", $"Today Fully Completed Lines: {todaysPickedLines}", "Today's fully completed lines (shipped qty >= order qty)");
    debugTable.Rows.Add("TransAnalysis", $"Yesterday Fully Completed Lines: {yesterdaysPickedLines}", "Yesterday's fully completed lines (shipped qty >= order qty)");

    // Check transaction counts by type for today and yesterday
    var todayByType = Db.PartTran
        .Where(pt => pt.Company == companyID && pt.OrderNum > 0 && pt.OrderLine > 0 &&
                    pt.TranDate.HasValue && pt.TranDate.Value >= today && pt.TranDate.Value < tomorrow)
        .GroupBy(pt => pt.TranType)
        .Select(g => new { TranType = g.Key, Count = g.Select(pt => new { pt.OrderNum, pt.OrderLine }).Distinct().Count() })
        .ToList();

    var yesterdayByType = Db.PartTran
        .Where(pt => pt.Company == companyID && pt.OrderNum > 0 && pt.OrderLine > 0 &&
                    pt.TranDate.HasValue && pt.TranDate.Value >= yesterday && pt.TranDate.Value < today)
        .GroupBy(pt => pt.TranType)
        .Select(g => new { TranType = g.Key, Count = g.Select(pt => new { pt.OrderNum, pt.OrderLine }).Distinct().Count() })
        .ToList();

    foreach (var typeCount in todayByType.OrderByDescending(tc => tc.Count))
    {
        debugTable.Rows.Add("TodayByType", $"{typeCount.TranType}: {typeCount.Count}", $"Transaction type breakdown for today");
    }

    foreach (var typeCount in yesterdayByType.OrderByDescending(tc => tc.Count))
    {
        debugTable.Rows.Add("YesterdayByType", $"{typeCount.TranType}: {typeCount.Count}", $"Transaction type breakdown for yesterday");
    }

    result.Tables.Add(debugTable);
}
catch (Exception ex)
{
    // Set error message and return empty DataSet
    errorMessage = "Error: Failed to generate picked lines statistics - " + ex.Message;
    result = new DataSet("PickedLinesStatistics");
}
