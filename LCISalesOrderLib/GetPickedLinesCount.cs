/*
 * =====================================================
 * LCI Sales Order Library - Get Picked Lines Count Statistics
 * =====================================================
 *
 * Purpose: Returns comprehensive statistics on picked lines and upcoming due dates
 *
 * Parameters: None (queries all relevant lines)
 *
 * Returns:
 *   - result (System.Data.DataSet): DataSet containing statistics table with counts
 *   - errorMessage (string): Error message if query fails, empty string if successful
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Statistics Included:
 *   1. TodaysPickedLines - Lines picked today (based on STK-PLT transactions)
 *   2. YesterdaysPickedLines - Lines picked yesterday (based on STK-PLT transactions)
 *   3. ThisWeeksPickedLines - Lines picked this week (based on STK-PLT transactions)
 *   4. TodaysDueLines - Open lines with NeedByDate = today
 *   5. TomorrowsDueLines - Open lines with NeedByDate = tomorrow
 *   6. ThisWeeksDueLines - Open lines with NeedByDate = this week (excluding today/tomorrow)
 *
 * Note: Categories are NOT mutually exclusive - yesterday counts towards week total, etc.
 *       A line counts as "1" regardless of quantity
 *       Picking counts are based on PartTran STK-PLT transactions to capture all picked lines,
 *       including those that may have been shipped after picking
 *
 * Usage Example:
 *   // Execute function
 *   // result DataSet will contain one table with 6 rows of statistics
 *   // errorMessage will be empty string if successful
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = new DataSet("PickedLinesStatistics");  // Initialize result DataSet
errorMessage = "";                     // Initialize error message as empty (success)

try
{
    // Create statistics table
    DataTable statsTable = new DataTable("Statistics");
    statsTable.Columns.Add("Category", typeof(string));
    statsTable.Columns.Add("Count", typeof(int));
    statsTable.Columns.Add("Description", typeof(string));

    // Get date ranges for filtering
    DateTime today = DateTime.Today;
    DateTime yesterday = today.AddDays(-1);
    DateTime tomorrow = today.AddDays(1);
    DateTime weekStart = today.AddDays(-(int)today.DayOfWeek);  // Start of this week (Sunday)
    DateTime weekEnd = weekStart.AddDays(7);                   // End of this week

    // ===== PICKED LINES STATISTICS =====
    // Using PartTran table to track actual picking transactions (STK-PLT type)
    // This captures all lines that were picked today, including those that may have been shipped after picking

    // 1. Today's picked lines - count distinct order lines that had STK-PLT transactions today
    var todaysPickedLines = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType == "STK-PLT" &&
                    pt.OrderNum > 0 &&
                    pt.OrderLine > 0 &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= today &&
                    pt.TranDate.Value < tomorrow)
        .Select(pt => new { OrderNum = pt.OrderNum, OrderLine = pt.OrderLine })
        .Distinct()
        .Count();

    // 2. Yesterday's picked lines - count distinct order lines that had STK-PLT transactions yesterday
    var yesterdaysPickedLines = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType == "STK-PLT" &&
                    pt.OrderNum > 0 &&
                    pt.OrderLine > 0 &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= yesterday &&
                    pt.TranDate.Value < today)
        .Select(pt => new { OrderNum = pt.OrderNum, OrderLine = pt.OrderLine })
        .Distinct()
        .Count();

    // 3. This week's picked lines - count distinct order lines that had STK-PLT transactions this week
    var thisWeeksPickedLines = Db.PartTran
        .Where(pt => pt.Company == companyID &&
                    pt.TranType == "STK-PLT" &&
                    pt.OrderNum > 0 &&
                    pt.OrderLine > 0 &&
                    pt.TranDate.HasValue &&
                    pt.TranDate.Value >= weekStart &&
                    pt.TranDate.Value < weekEnd)
        .Select(pt => new { OrderNum = pt.OrderNum, OrderLine = pt.OrderLine })
        .Distinct()
        .Count();

    // ===== DUE DATE STATISTICS =====

    // 4. Today's due lines (open lines with NeedByDate = today)
    var todaysDueLines = (from oh in Db.OrderHed
                         join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                         where oh.Company == companyID &&
                               oh.OpenOrder == true &&
                               od.OpenLine == true &&
                               od.NeedByDate.HasValue &&
                               od.NeedByDate.Value >= today &&
                               od.NeedByDate.Value < tomorrow
                         select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                        .Distinct()
                        .Count();

    // 5. Tomorrow's due lines
    DateTime dayAfterTomorrow = tomorrow.AddDays(1);
    var tomorrowsDueLines = (from oh in Db.OrderHed
                            join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                            where oh.Company == companyID &&
                                  oh.OpenOrder == true &&
                                  od.OpenLine == true &&
                                  od.NeedByDate.HasValue &&
                                  od.NeedByDate.Value >= tomorrow &&
                                  od.NeedByDate.Value < dayAfterTomorrow
                            select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                           .Distinct()
                           .Count();

    // 6. This week's due lines (includes today and tomorrow)
    var thisWeeksDueLines = (from oh in Db.OrderHed
                            join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                            where oh.Company == companyID &&
                                  oh.OpenOrder == true &&
                                  od.OpenLine == true &&
                                  od.NeedByDate.HasValue &&
                                  od.NeedByDate.Value >= weekStart &&
                                  od.NeedByDate.Value < weekEnd
                            select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                           .Distinct()
                           .Count();

    // Add statistics to table
    statsTable.Rows.Add("TodaysPickedLines", todaysPickedLines, "Lines picked today (STK-PLT transactions)");
    statsTable.Rows.Add("YesterdaysPickedLines", yesterdaysPickedLines, "Lines picked yesterday (STK-PLT transactions)");
    statsTable.Rows.Add("ThisWeeksPickedLines", thisWeeksPickedLines, "Lines picked this week (STK-PLT transactions, includes today/yesterday)");
    statsTable.Rows.Add("TodaysDueLines", todaysDueLines, "Open lines due today");
    statsTable.Rows.Add("TomorrowsDueLines", tomorrowsDueLines, "Open lines due tomorrow");
    statsTable.Rows.Add("ThisWeeksDueLines", thisWeeksDueLines, "Open lines due this week (includes today/tomorrow)");

    // Add table to result DataSet
    result.Tables.Add(statsTable);
}
catch (Exception ex)
{
    // Set error message and return empty DataSet
    errorMessage = "Error: Failed to generate picked lines statistics - " + ex.Message;
    result = new DataSet("PickedLinesStatistics");
}
