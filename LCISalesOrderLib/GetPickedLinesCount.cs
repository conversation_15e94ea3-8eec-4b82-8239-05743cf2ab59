/*
 * =====================================================
 * LCI Sales Order Library - Get Picked Lines Count Statistics
 * =====================================================
 *
 * Purpose: Returns comprehensive statistics on picked lines and upcoming due dates
 *
 * Parameters: None (queries all relevant lines)
 *
 * Returns:
 *   - result (System.Data.DataSet): DataSet containing statistics table with counts
 *   - errorMessage (string): Error message if query fails, empty string if successful
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Statistics Included:
 *   1. TodaysPickedLines - Lines fully picked today
 *   2. YesterdaysPickedLines - Lines fully picked yesterday
 *   3. ThisWeeksPickedLines - Lines fully picked this week (excluding today/yesterday)
 *   4. TodaysDueLines - Open lines with NeedByDate = today
 *   5. TomorrowsDueLines - Open lines with NeedByDate = tomorrow
 *   6. ThisWeeksDueLines - Open lines with NeedByDate = this week (excluding today/tomorrow)
 *
 * Note: Categories are NOT mutually exclusive - yesterday counts towards week total, etc.
 *       A line counts as "1" regardless of quantity
 *
 * Usage Example:
 *   // Execute function
 *   // result DataSet will contain one table with 6 rows of statistics
 *   // errorMessage will be empty string if successful
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = new DataSet("PickedLinesStatistics");  // Initialize result DataSet
errorMessage = "";                     // Initialize error message as empty (success)

try
{
    // Create statistics table
    DataTable statsTable = new DataTable("Statistics");
    statsTable.Columns.Add("Category", typeof(string));
    statsTable.Columns.Add("Count", typeof(int));
    statsTable.Columns.Add("Description", typeof(string));

    // Get date ranges for filtering
    DateTime today = DateTime.Today;
    DateTime yesterday = today.AddDays(-1);
    DateTime tomorrow = today.AddDays(1);
    DateTime weekStart = today.AddDays(-(int)today.DayOfWeek);  // Start of this week (Sunday)
    DateTime weekEnd = weekStart.AddDays(7);                   // End of this week

    // ===== PICKED LINES STATISTICS =====

    // 1. Today's picked lines
    var todaysPickedLines = Db.PartAlloc
        .Where(pa => pa.Company == companyID &&
                    pa.PickedQty > 0 &&
                    pa.PickedQty >= pa.AllocatedQty &&
                    pa.CreateDate.HasValue &&
                    pa.CreateDate.Value >= today &&
                    pa.CreateDate.Value < tomorrow)
        .Select(pa => new { OrderNum = pa.OrderNum, OrderLine = pa.OrderLine })
        .Distinct()
        .Count();

    // 2. Yesterday's picked lines
    var yesterdaysPickedLines = Db.PartAlloc
        .Where(pa => pa.Company == companyID &&
                    pa.PickedQty > 0 &&
                    pa.PickedQty >= pa.AllocatedQty &&
                    pa.CreateDate.HasValue &&
                    pa.CreateDate.Value >= yesterday &&
                    pa.CreateDate.Value < today)
        .Select(pa => new { OrderNum = pa.OrderNum, OrderLine = pa.OrderLine })
        .Distinct()
        .Count();

    // 3. This week's picked lines (includes today and yesterday)
    var thisWeeksPickedLines = Db.PartAlloc
        .Where(pa => pa.Company == companyID &&
                    pa.PickedQty > 0 &&
                    pa.PickedQty >= pa.AllocatedQty &&
                    pa.CreateDate.HasValue &&
                    pa.CreateDate.Value >= weekStart &&
                    pa.CreateDate.Value < weekEnd)  // Include all days in the week
        .Select(pa => new { OrderNum = pa.OrderNum, OrderLine = pa.OrderLine })
        .Distinct()
        .Count();

    // ===== DUE DATE STATISTICS =====

    // 4. Today's due lines (open lines with NeedByDate = today)
    var todaysDueLines = (from oh in Db.OrderHed
                         join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                         where oh.Company == companyID &&
                               oh.OpenOrder == true &&
                               od.OpenLine == true &&
                               od.NeedByDate.HasValue &&
                               od.NeedByDate.Value >= today &&
                               od.NeedByDate.Value < tomorrow
                         select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                        .Distinct()
                        .Count();

    // 5. Tomorrow's due lines
    DateTime dayAfterTomorrow = tomorrow.AddDays(1);
    var tomorrowsDueLines = (from oh in Db.OrderHed
                            join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                            where oh.Company == companyID &&
                                  oh.OpenOrder == true &&
                                  od.OpenLine == true &&
                                  od.NeedByDate.HasValue &&
                                  od.NeedByDate.Value >= tomorrow &&
                                  od.NeedByDate.Value < dayAfterTomorrow
                            select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                           .Distinct()
                           .Count();

    // 6. This week's due lines (includes today and tomorrow)
    var thisWeeksDueLines = (from oh in Db.OrderHed
                            join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                            where oh.Company == companyID &&
                                  oh.OpenOrder == true &&
                                  od.OpenLine == true &&
                                  od.NeedByDate.HasValue &&
                                  od.NeedByDate.Value >= weekStart &&
                                  od.NeedByDate.Value < weekEnd
                            select new { OrderNum = od.OrderNum, OrderLine = od.OrderLine })
                           .Distinct()
                           .Count();

    // Add statistics to table
    statsTable.Rows.Add("TodaysPickedLines", todaysPickedLines, "Lines fully picked today");
    statsTable.Rows.Add("YesterdaysPickedLines", yesterdaysPickedLines, "Lines fully picked yesterday");
    statsTable.Rows.Add("ThisWeeksPickedLines", thisWeeksPickedLines, "Lines fully picked this week (includes today/yesterday)");
    statsTable.Rows.Add("TodaysDueLines", todaysDueLines, "Open lines due today");
    statsTable.Rows.Add("TomorrowsDueLines", tomorrowsDueLines, "Open lines due tomorrow");
    statsTable.Rows.Add("ThisWeeksDueLines", thisWeeksDueLines, "Open lines due this week (includes today/tomorrow)");

    // Add table to result DataSet
    result.Tables.Add(statsTable);
}
catch (Exception ex)
{
    // Set error message and return empty DataSet
    errorMessage = "Error: Failed to generate picked lines statistics - " + ex.Message;
    result = new DataSet("PickedLinesStatistics");
}
