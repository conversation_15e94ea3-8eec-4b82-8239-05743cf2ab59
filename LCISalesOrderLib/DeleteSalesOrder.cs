/*
 * =====================================================
 * LCI Sales Order Library - Delete Sales Order
 * =====================================================
 *
 * Purpose: Completely deletes a sales order and all related records
 *
 * Parameters:
 *   - SalesOrderNum (int): Sales order number to delete
 *
 * Returns:
 *   - result (string): "OK" if successful, error message if failed
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Retrieve the complete sales order dataset
 *   2. Mark all related records for deletion (header, lines, releases, charges)
 *   3. Execute deletion via Epicor service
 *
 * Warning: This is a destructive operation that cannot be undone!
 *
 * Usage Example:
 *   SalesOrderNum = 12345;
 *   // Execute function
 *   // result will be "OK" or error message
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = "Error";                      // Default to error state

// Execute sales order deletion using Epicor service
this.CallService<Erp.Contracts.SalesOrderSvcContract>(svc =>
{
    try
    {
        // Retrieve the complete sales order dataset for deletion
        var ts = svc.GetByID(SalesOrderNum);

        // Verify sales order exists and has data
        if (ts != null && ts.OrderHed.Count > 0)
        {
            try
            {
                // ===== PHASE 1: Mark Order Header for Deletion =====
                var orderHedRow = ts.OrderHed.FirstOrDefault();
                if (orderHedRow != null)
                {
                    orderHedRow.RowMod = "D";  // Mark header for deletion
                }

                // ===== PHASE 2: Mark All Order Detail Lines for Deletion =====
                foreach (var orderDtlRow in ts.OrderDtl)
                {
                    orderDtlRow.RowMod = "D";  // Mark each line for deletion
                }

                // ===== PHASE 3: Mark All Order Releases for Deletion =====
                foreach (var orderRelRow in ts.OrderRel)
                {
                    orderRelRow.RowMod = "D";  // Mark each release for deletion
                }

                // ===== PHASE 4: Mark All Miscellaneous Charges for Deletion =====
                foreach (var orderMscRow in ts.OrderMsc)
                {
                    orderMscRow.RowMod = "D";  // Mark each charge for deletion
                }

                // ===== PHASE 5: Execute Deletion in Epicor =====
                svc.Update(ref ts);  // Commit all deletions in single transaction

                // Success - sales order completely deleted
                result = "OK";
            }
            catch (Exception ex)
            {
                // Deletion operation failed
                result = "Error: Failed to delete sales order - " + ex.Message;
            }
        }
        else
        {
            // Sales order not found or empty dataset
            result = "Error: Sales order not found";
        }
    }
    catch (Exception ex)
    {
        // Failed to retrieve sales order from Epicor
        result = "Error: Failed to retrieve sales order - " + ex.Message;
    }
});
