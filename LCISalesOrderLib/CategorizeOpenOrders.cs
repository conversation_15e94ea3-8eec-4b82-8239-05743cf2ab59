/*
 * =====================================================
 * LCI Order Library - Categorize Open Orders
 * =====================================================
 *
 * Purpose: Categorizes all open, fulfillable orders into different fulfillment status categories
 *
 * Parameters: None (queries all open orders)
 *
 * Returns:
 *   - result (System.Data.DataSet): DataSet containing 7 separate tables for each order category
 *   - errorMessage (string): Error message if categorization fails, empty string if successful
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Categories (mutually exclusive):
 *   1. GeneralOpenFulfillable - Orders that are open and ready to fulfill
 *   2. CurrentlyBeingPicked - Orders currently in picking process
 *   3. AlreadyPicked - Orders that have been picked but not shipped
 *   4. BackOrder - Orders with insufficient inventory (not install orders)
 *   5. PickingForInstalls - Install orders currently being picked
 *   6. PickedForInstalls - Install orders that have been picked
 *   7. BackOrderInstalls - Install orders that are backordered
 *
 * Process:
 *   1. Query all open orders from OrderHed/OrderDtl with customer information
 *   2. Check for install orders (parts containing 'Install' in description/part number)
 *   3. Determine picking status from PartAlloc and picking records
 *   4. Check inventory availability for backorder determination
 *   5. Categorize orders ensuring no duplicates across categories
 *   6. Return separate DataSets for each category
 *
 * Note: Orders are categorized in priority order - once placed in a category, 
 *       they won't appear in subsequent categories
 *
 * Usage Example:
 *   // Execute function
 *   // result DataSet will contain 7 tables, one for each order category
 */

// Initialize variables
string companyID = "162250";  // LCI company ID
result = new DataSet("CategorizedOrders");  // Initialize result DataSet
errorMessage = "";  // Initialize error message as empty (success)

try
{
    // Create all category tables upfront
    var categoryTables = new Dictionary<string, DataTable>();
    
    string[] categoryNames = {
        "GeneralOpenFulfillable",
        "CurrentlyBeingPicked", 
        "AlreadyPicked",
        "BackOrder",
        "PickingForInstalls",
        "PickedForInstalls",
        "BackOrderInstalls"
    };
    
    // Create DataTable structure for order information
    foreach (string categoryName in categoryNames)
    {
        DataTable table = new DataTable(categoryName);
        
        // Add only the requested columns
        table.Columns.Add("OrderNum", typeof(int));
        table.Columns.Add("CustomerName", typeof(string));
        table.Columns.Add("JobNum", typeof(string));
        table.Columns.Add("OrderDate", typeof(DateTime));
        table.Columns.Add("NeedByDate", typeof(DateTime));
        table.Columns.Add("ShipByDate", typeof(DateTime));
        
        categoryTables[categoryName] = table;
        result.Tables.Add(table);
    }
    
    // Query all open orders with details, customer information, and job numbers
    var openOrders = (from oh in Db.OrderHed
                     join od in Db.OrderDtl on new { oh.Company, oh.OrderNum } equals new { od.Company, od.OrderNum }
                     join cust in Db.Customer on new { oh.Company, oh.CustNum } equals new { cust.Company, cust.CustNum }
                     join jp in Db.JobProd on new { od.Company, od.OrderNum, od.OrderLine } equals new { jp.Company, jp.OrderNum, jp.OrderLine } into jpGroup
                     from jp in jpGroup.DefaultIfEmpty()
                     join jh in Db.JobHead on new { jp.Company, jp.JobNum } equals new { jh.Company, jh.JobNum } into jhGroup
                     from jh in jhGroup.DefaultIfEmpty()
                     where oh.Company == companyID
                        && oh.OpenOrder == true
                        && od.OpenLine == true
                        && od.OrderQty > 0  // Has quantity to fulfill
                     select new {
                         Company = oh.Company,
                         OrderNum = oh.OrderNum,
                         OrderLine = od.OrderLine,
                         PartNum = od.PartNum ?? "",
                         LineDesc = od.LineDesc ?? "",
                         OrderQty = od.OrderQty,
                         CustomerName = cust.Name ?? "",
                         JobNum = jh.JobNum ?? "",  // Get JobNum from JobHead via JobProd connection
                         OrderDate = oh.OrderDate,
                         NeedByDate = od.NeedByDate,
                         ShipByDate = oh.RequestDate
                     }).ToList();

    // Filter out fully shipped orders by checking ShipDtl
    var unshippedOrders = openOrders.Where(order =>
    {
        // Calculate total shipped quantity for this order line
        // Use OurInventoryShipQty, but if it's 0, use OurJobShipQty instead (never add together)
        var shipments = Db.ShipDtl
            .Where(sd => sd.Company == order.Company &&
                        sd.OrderNum == order.OrderNum &&
                        sd.OrderLine == order.OrderLine)
            .ToList();

        decimal totalShipped = 0m;
        foreach (var shipment in shipments)
        {
            // Use OurInventoryShipQty if > 0, otherwise use OurJobShipQty
            decimal shipQty = shipment.OurInventoryShipQty > 0 ?
                             shipment.OurInventoryShipQty :
                             shipment.OurJobShipQty;
            totalShipped += shipQty;
        }

        // Only include if not fully shipped
        return totalShipped < order.OrderQty;
    }).ToList();

    // Update openOrders with filtered results
    openOrders = unshippedOrders;

    // Track processed orders to ensure no duplicates (now at order level, not line level)
    var processedOrders = new HashSet<int>();
    
    // Group by order number to work at order level
    var orderGroups = openOrders.GroupBy(o => o.OrderNum);
    
    foreach (var orderGroup in orderGroups)
    {
        int orderNum = orderGroup.Key;
        
        // Skip if already processed
        if (processedOrders.Contains(orderNum))
            continue;
            
        // Get the first order record for basic order info
        var orderInfo = orderGroup.First();
        
        // Check if any line in this order is an install order
        bool isInstallOrder = orderGroup.Any(o => 
            o.PartNum.ToUpper().Contains("INSTALL") || 
            o.LineDesc.ToUpper().Contains("INSTALL"));
        
        // Get overall order picking status inline based on your specifications
        string overallPickingStatus = "NOT_ALLOCATED";
        try
        {
            // Check PartAlloc for all lines in this order
            var allocations = Db.PartAlloc
                .Where(pa => pa.Company == orderInfo.Company && pa.OrderNum == orderNum)
                .ToList();

            if (allocations.Any())
            {
                // Calculate total quantities for the entire order
                decimal totalOrderQty = orderGroup.Sum(o => o.OrderQty);
                decimal totalAllocatedQty = allocations.Sum(a => a.AllocatedQty);
                decimal totalPickedQty = allocations.Sum(a => a.PickedQty);
                decimal totalPickingQty = allocations.Where(a => a.PickedQty > 0 && a.PickedQty < a.AllocatedQty).Sum(a => a.PickedQty);

                // Check if order is currently being picked (any items in picking state)
                bool anyCurrentlyPicking = allocations.Any(a => a.PickedQty > 0 && a.PickedQty < a.AllocatedQty);

                // Check if all allocated quantities are fully picked
                bool allAllocatedPicked = allocations.All(a => a.PickedQty >= a.AllocatedQty);

                // Check if all order quantity is allocated and picked
                bool fullyPickedForOrder = (totalPickedQty >= totalOrderQty) && allAllocatedPicked;

                if (anyCurrentlyPicking)
                {
                    overallPickingStatus = "PICKING";  // Currently being picked
                }
                else if (fullyPickedForOrder)
                {
                    overallPickingStatus = "PICKED";   // Fully picked
                }
                else if (totalAllocatedQty < totalOrderQty)
                {
                    overallPickingStatus = "BACKORDER"; // Not enough allocated for full order
                }
                else
                {
                    overallPickingStatus = "ALLOCATED"; // Allocated but not picked yet
                }
            }
        }
        catch
        {
            overallPickingStatus = "UNKNOWN";
        }

        // Check if order has backorders inline
        bool hasBackorders = false;
        try
        {
            foreach (var line in orderGroup)
            {
                // Calculate remaining quantity (for open lines, this is typically the full OrderQty)
                decimal remainingQty = line.OrderQty;

                // Get available inventory for this part inline
                decimal availableQty = 0m;
                try
                {
                    var totalAvailable = Db.PartBin
                        .Where(pb => pb.Company == orderInfo.Company && pb.PartNum == line.PartNum)
                        .Sum(pb => (decimal?)(pb.OnhandQty - pb.AllocatedQty));

                    availableQty = totalAvailable ?? 0m;
                }
                catch
                {
                    availableQty = 0m;
                }

                if (remainingQty > availableQty)
                {
                    hasBackorders = true;
                    break;
                }
            }
        }
        catch
        {
            hasBackorders = false;
        }

        // Determine category inline (in priority order)
        string targetCategory = "GeneralOpenFulfillable";  // Default fallback

        if (isInstallOrder)
        {
            // Install order categories
            if (overallPickingStatus == "PICKED")
                targetCategory = "PickedForInstalls";
            else if (overallPickingStatus == "PICKING")
                targetCategory = "PickingForInstalls";
            else if (overallPickingStatus == "BACKORDER" || hasBackorders)
                targetCategory = "BackOrderInstalls";
            else
                targetCategory = "GeneralOpenFulfillable";  // Install orders ready to pick
        }
        else
        {
            // Regular order categories
            if (overallPickingStatus == "PICKED")
                targetCategory = "AlreadyPicked";
            else if (overallPickingStatus == "PICKING")
                targetCategory = "CurrentlyBeingPicked";
            else if (overallPickingStatus == "BACKORDER" || hasBackorders)
                targetCategory = "BackOrder";
            else
                targetCategory = "GeneralOpenFulfillable";
        }
        
        // Add to appropriate category table
        if (categoryTables.ContainsKey(targetCategory))
        {
            DataRow row = categoryTables[targetCategory].NewRow();

            // Populate order row inline
            row["OrderNum"] = orderInfo.OrderNum;
            row["CustomerName"] = orderInfo.CustomerName ?? "";
            row["JobNum"] = orderInfo.JobNum ?? "";
            row["OrderDate"] = orderInfo.OrderDate ?? (object)DBNull.Value;
            row["NeedByDate"] = orderInfo.NeedByDate ?? (object)DBNull.Value;
            row["ShipByDate"] = orderInfo.ShipByDate ?? (object)DBNull.Value;

            categoryTables[targetCategory].Rows.Add(row);

            processedOrders.Add(orderNum);
        }
    }
}
catch (Exception ex)
{
    // Set error message parameter and return empty DataSet
    errorMessage = "Error: Failed to categorize orders - " + ex.Message;
    result = new DataSet("CategorizedOrders");  // Return empty DataSet on error
}
