using System;
using System.Linq;

/*
 * =====================================================
 * LCI AR Invoice Library - Get Invoice Total
 * =====================================================
 * 
 * Purpose: Retrieves the total amount of an AR Invoice
 * 
 * Parameters:
 *   - InvoiceNum (int): AR Invoice number to get total for
 * 
 * Returns:
 *   - result (decimal): Invoice total amount, or 0 if not found
 * 
 * Company: Hardcoded to 162250 (LCI)
 * 
 * Usage Example:
 *   InvoiceNum = 12345;
 *   // Execute function
 *   // result will be the invoice total amount
 */

// Initialize company ID (hardcoded for LCI)
string companyID = "162250";
result = 0.0m;  // Default to zero

// Search for the AR Invoice and get its total
var invoice = Db.InvcHead
    .FirstOrDefault(i => i.Company == companyID &&
                         i.InvoiceNum == InvoiceNum);

// Get the invoice total if found
if (invoice != null)
{
    // Use DocInvoiceAmt for document currency total
    result = invoice.DocInvoiceAmt;
}
