/*
 * =====================================================
 * LCI AR Invoice Library - Delete AR Invoice
 * =====================================================
 * 
 * Purpose: Completely deletes an AR Invoice and all related records
 * 
 * Parameters:
 *   - InvoiceNum (int): AR Invoice number to delete
 * 
 * Returns:
 *   - result (string): "OK" if successful, error message if failed
 * 
 * Company: Hardcoded to 162250 (LCI)
 * 
 * Process:
 *   1. Retrieve the complete AR Invoice dataset
 *   2. Mark all related records for deletion (header, lines, taxes, misc charges)
 *   3. Execute deletion via Epicor ARInvoice service
 * 
 * Warning: This is a destructive operation that cannot be undone!
 * 
 * Usage Example:
 *   InvoiceNum = 12345;
 *   // Execute function
 *   // result will be "OK" or error message
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = "Error";                      // Default to error state

// Execute AR Invoice deletion using Epicor service
this.CallService<Erp.Contracts.ARInvoiceSvcContract>(svc =>
{
    try
    {
        // Retrieve the complete AR Invoice dataset for deletion
        var ts = svc.GetByID(InvoiceNum);
        
        // Verify AR Invoice exists and has data
        if (ts != null && ts.InvcHead.Count > 0)
        {
            try
            {
                // ===== PHASE 1: Mark Invoice Header for Deletion =====
                var invoiceHedRow = ts.InvcHead.FirstOrDefault();
                if (invoiceHedRow != null)
                {
                    invoiceHedRow.RowMod = "D";  // Mark header for deletion
                }
                
                // ===== PHASE 2: Mark All Invoice Detail Lines for Deletion =====
                foreach (var invoiceDtlRow in ts.InvcDtl)
                {
                    invoiceDtlRow.RowMod = "D";  // Mark each line for deletion
                }
                
                // ===== PHASE 3: Mark All Invoice Tax Records for Deletion =====
                foreach (var invoiceTaxRow in ts.InvcTax)
                {
                    invoiceTaxRow.RowMod = "D";  // Mark each tax record for deletion
                }
                
                // ===== PHASE 4: Mark All Invoice Miscellaneous Charges for Deletion =====
                foreach (var invoiceMscRow in ts.InvcMisc)
                {
                    invoiceMscRow.RowMod = "D";  // Mark each misc charge for deletion
                }
                
                // ===== PHASE 5: Execute Deletion in Epicor =====
                // Note: Cash receipt applications are handled by a separate service
                // and would need to be removed via CashRec service if required
                svc.Update(ref ts);  // Commit all deletions in single transaction
                
                // Success - AR Invoice completely deleted
                result = "OK";
            }
            catch (Exception ex)
            {
                // Deletion operation failed
                result = "Error: Failed to delete AR Invoice - " + ex.Message;
            }
        }
        else
        {
            // AR Invoice not found or empty dataset
            result = "Error: AR Invoice not found";
        }
    }
    catch (Exception ex)
    {
        // Failed to retrieve AR Invoice from Epicor
        result = "Error: Failed to retrieve AR Invoice - " + ex.Message;
    }
});
