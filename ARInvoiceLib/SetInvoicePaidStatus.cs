using System;
using System.Linq;

/*
 * =====================================================
 * LCI AR Invoice Library - Set Invoice Paid Status
 * =====================================================
 * 
 * Purpose: Marks an AR Invoice as paid or unpaid by updating the OpenInvoice field
 * 
 * Parameters:
 *   - InvoiceNum (int): AR Invoice number to update
 *   - setToPaid (bool): true to mark as paid (closed), false to mark as unpaid (open)
 * 
 * Returns:
 *   - result (string): "OK - Invoice marked as paid/unpaid" if successful, error message if failed
 * 
 * Company: Hardcoded to 162250 (LCI)
 * 
 * Process:
 *   1. Find the AR Invoice in the database directly
 *   2. Update the OpenInvoice field (false = paid/closed, true = unpaid/open)
 *   3. Save changes via Db.SaveChanges()
 * 
 * Note: InvcHead.OpenInvoice controls invoice payment status in Epicor
 * 
 * Usage Example:
 *   InvoiceNum = 12345;
 *   setToPaid = true;  // Mark invoice as paid
 *   // Execute function
 *   // result will be "OK - Invoice marked as paid" or error message
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = "Error";                      // Default to error state

// Execute invoice status update using direct database access
try
{
    // Find the invoice directly in the database
    var invoice = Db.InvcHead
        .FirstOrDefault(i => i.Company == companyID &&
                             i.InvoiceNum == InvoiceNum);

    if (invoice == null)
    {
        result = "Error: AR Invoice not found";
        return;
    }

    // Update the OpenInvoice field to control payment status
    // false = invoice is paid/closed (no balance due)
    // true = invoice is unpaid/open (has balance due)
    invoice.OpenInvoice = !setToPaid;  // Invert because OpenInvoice means unpaid

    // Save changes to database
    try
    {
        Db.SaveChanges();
    }
    catch (Exception saveEx)
    {
        result = "Error: Failed to save changes - " + saveEx.Message;
        return;
    }

    // Success - generate descriptive result message
    string status = setToPaid ? "paid" : "unpaid";
    result = "OK - Invoice marked as " + status;
}
catch (Exception ex)
{
    // Direct database operation failed
    result = "Error: Failed to update invoice status - " + ex.Message;
}
