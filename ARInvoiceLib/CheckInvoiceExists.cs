using System;
using System.Linq;

/*
 * =====================================================
 * LCI AR Invoice Library - Check Invoice Exists
 * =====================================================
 * 
 * Purpose: Checks if an AR Invoice exists in the system
 * 
 * Parameters:
 *   - InvoiceNum (int): AR Invoice number to check
 * 
 * Returns:
 *   - result (bool): true if invoice exists, false if not found
 * 
 * Company: Hardcoded to 162250 (LCI)
 * 
 * Usage Example:
 *   InvoiceNum = 12345;
 *   // Execute function
 *   // result will be true/false
 */

// Initialize company ID (hardcoded for LCI)
string companyID = "162250";
result = false;

// Search for the AR Invoice in the database
var invoice = Db.InvcHead
    .FirstOrDefault(i => i.Company == companyID &&
                         i.InvoiceNum == InvoiceNum);

// Set result based on whether the invoice was found
if (invoice != null)
{
    result = true;  // Invoice found in system
}
