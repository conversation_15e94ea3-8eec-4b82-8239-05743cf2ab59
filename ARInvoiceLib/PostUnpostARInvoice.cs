using System;
using System.Linq;

/*
 * =====================================================
 * LCI AR Invoice Library - Post/Unpost AR Invoice
 * =====================================================
 *
 * Purpose: Posts or unposts an AR Invoice using direct database access
 *
 * Parameters:
 *   - InvoiceNum (int): AR Invoice number to post/unpost
 *   - postInvoice (bool): true to post invoice, false to unpost invoice
 *   - GroupID (string): Posting group ID required by Epicor for posting operations
 *
 * Returns:
 *   - result (string): "OK - Invoice posted/unposted" if successful, error message if failed
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Find the AR Invoice in the database directly
 *   2. Validate invoice exists and is in correct state for operation
 *   3. Update the Posted field and GroupID directly in database
 *   4. Save changes via Db.SaveChanges()
 *
 * Notes:
 *   - Uses direct database access instead of service calls
 *   - Posted invoices create GL entries and cannot be modified
 *   - Unposted invoices remove GL entries and allow modifications
 *   - Invoices with applied payments cannot be unposted
 *   - GroupID is required for both posting and unposting operations
 *
 * Usage Example:
 *   InvoiceNum = 12345;
 *   postInvoice = false;  // Unpost the invoice
 *   GroupID = "MANUAL";   // Use appropriate group ID for your system
 *   // Execute function
 *   // result will be "OK - Invoice unposted" or error message
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = "Error";                      // Default to error state

// Execute invoice posting/unposting using direct database access
try
{
    // Find the invoice directly in the database
    var invoice = Db.InvcHead
        .FirstOrDefault(i => i.Company == companyID &&
                             i.InvoiceNum == InvoiceNum);

    if (invoice == null)
    {
        result = "Error: AR Invoice not found";
        return;
    }

    // Check current posting status
    bool currentlyPosted = invoice.Posted;

    // Validate operation is needed and allowed
    if (postInvoice && currentlyPosted)
    {
        result = "Error: Invoice is already posted";
        return;
    }

    if (!postInvoice && !currentlyPosted)
    {
        result = "Error: Invoice is already unposted";
        return;
    }

    // Additional validation for unposting
    if (!postInvoice && currentlyPosted)
    {
        // Check if invoice has payments applied (cannot unpost if payments exist)
        if (invoice.InvoiceAmt != invoice.InvoiceBal)
        {
            result = "Error: Cannot unpost invoice with applied payments";
            return;
        }
    }

    // Validate GroupID parameter
    if (string.IsNullOrEmpty(GroupID))
    {
        result = "Error: GroupID parameter is required for posting operations";
        return;
    }

    // Update the posting status and GroupID directly in the database
    invoice.Posted = postInvoice;
    invoice.GroupID = GroupID;

    // Save changes to database
    try
    {
        // Use the correct save method for this Epicor environment
        Db.SaveChanges();
    }
    catch (Exception saveEx)
    {
        result = "Error: Failed to save changes - " + saveEx.Message;
        return;
    }

    // Success - generate descriptive result message
    string status = postInvoice ? "posted" : "unposted";
    result = "OK - Invoice " + status;
}
catch (Exception ex)
{
    // Direct database operation failed
    string operation = postInvoice ? "post" : "unpost";
    result = "Error: Failed to " + operation + " AR Invoice - " + ex.Message;
}
