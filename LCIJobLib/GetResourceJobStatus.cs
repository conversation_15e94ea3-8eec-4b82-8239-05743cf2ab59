/*
 * =====================================================
 * LCI Job Library - Get Resource Job Status
 * =====================================================
 *
 * Purpose: Shows what jobs are currently logged into/being worked on for each resource per site
 *          and returns the associated next job for the machine as scheduled in Epicor
 *
 * Parameters:
 *   - siteFilter (string): Optional site filter - Plant/Site ID from JobHead (null or empty for all sites)
 *
 * Returns:
 *   - result (System.Data.DataSet): DataSet containing three tables:
 *     1. CurrentJobs - Jobs currently being worked on
 *     2. NextJobs - Next scheduled job for each resource
 *     3. ResourceSummary - Combined view per resource
 *   - errorMessage (string): Error information if any issues occur
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Query active labor transactions to find current jobs being worked on
 *   2. Operation-centric approach: Find all valid operations for next jobs
 *   3. Valid operation criteria:
 *      - Operation is incomplete (OpComplete = false)
 *      - Not currently being worked on (not in active labor transactions)
 *      - ALL previous operations in the same job are complete
 *   4. Use engineering routing from JobOpDtl table for ResourceID assignments
 *   5. Use Epicor scheduling (SchedPriority, ScheduledStartTime) to determine order
 *   5. Build comprehensive DataSet with current and next job information
 *
 * Tables Used:
 *   - Resource: Resource master data
 *   - LaborDtl: Labor detail transactions (ActiveTrans = 1 for current jobs)
 *   - JobHead: Job header information
 *   - JobOper: Job operation details and scheduling
 *   - JobOpDtl: Job operation detail with individual ResourceID assignments (engineering routing)
 *   - Part: Part master data
 *   - EmpBasic: Employee information
 *
 * Usage Example:
 *   siteFilter = null;  // All sites, or "MAIN" for specific Plant/Site ID from jobs
 *   // Execute function
 *   // result DataSet will contain CurrentJobs, NextJobs, and ResourceSummary tables
 *   // errorMessage will contain any error details
 *
 * Note: Resources are site-independent. Site information comes from the JobHead.Plant field.
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = new DataSet("ResourceJobStatus");  // Initialize result DataSet
errorMessage = "";                     // Initialize error message

try
{
    // =================================================================
    // Create DataSet Structure
    // =================================================================
    
    // Define specific resources that get their own tables
    var specificResources = new List<string> { "Tornos1", "Tornos2", "Tornos3", "Tornos4", "Citizen1", "Miyano1", "Nakamura1", "ManualLathe", "BandSaw1" };

    // Define machining resource groups (we'll exclude these from assembly)
    var machiningResourceGroups = new List<string> { "PRTorTws", "CITIZEN", "MIYANO", "NAKAMURA", "MANLATHE", "PRNakmur", "PRTwsGrp", "PRMiyano", "PRMlgGrp" };

    // Create specific resource tables
    var resourceTables = new Dictionary<string, DataTable>();
    foreach (var resourceId in specificResources)
    {
        var table = new DataTable($"CurrentJobs_{resourceId}");
        table.Columns.Add("Resource_ResourceID", typeof(string));
        table.Columns.Add("EmpBasic_Name", typeof(string));
        table.Columns.Add("JobHead_JobNum", typeof(string));
        table.Columns.Add("Part_PartDescription", typeof(string));
        table.Columns.Add("JobOper_Completion", typeof(string));
        resourceTables[resourceId] = table;
    }

    // Create general CurrentJobs table for all other resources
    DataTable currentJobsTable = new DataTable("CurrentJobs");
    currentJobsTable.Columns.Add("Resource_ResourceID", typeof(string));
    currentJobsTable.Columns.Add("EmpBasic_Name", typeof(string));
    currentJobsTable.Columns.Add("JobHead_JobNum", typeof(string));
    currentJobsTable.Columns.Add("Part_PartDescription", typeof(string));
    currentJobsTable.Columns.Add("JobOper_Completion", typeof(string));
    
    // Create specific NextJobs resource tables
    var nextJobsResourceTables = new Dictionary<string, DataTable>();
    foreach (var resourceId in specificResources)
    {
        var table = new DataTable($"NextJobs_{resourceId}");
        table.Columns.Add("JobHead_Plant", typeof(string));
        table.Columns.Add("Resource_ResourceID", typeof(string));
        table.Columns.Add("Resource_Description", typeof(string));
        table.Columns.Add("JobOper_JobNum", typeof(string));
        table.Columns.Add("JobHead_PartNum", typeof(string));
        table.Columns.Add("Part_PartDescription", typeof(string));
        table.Columns.Add("JobOper_AssemblySeq", typeof(int));
        table.Columns.Add("JobOper_OprSeq", typeof(int));
        table.Columns.Add("JobOper_OpDesc", typeof(string));
        table.Columns.Add("JobOper_StartDate", typeof(DateTime));
        table.Columns.Add("JobOper_DueDate", typeof(DateTime));
        table.Columns.Add("JobOper_EstimatedHours", typeof(decimal));
        table.Columns.Add("JobOper_EstSetHours", typeof(decimal));
        table.Columns.Add("JobOper_EstProdHours", typeof(decimal));
        table.Columns.Add("JobOpDtl_ResourceGrpID", typeof(string));
        nextJobsResourceTables[resourceId] = table;
    }

    // Create general NextJobs table for all other resources
    DataTable nextJobsTable = new DataTable("NextJobs");
    nextJobsTable.Columns.Add("JobHead_Plant", typeof(string));
    nextJobsTable.Columns.Add("Resource_ResourceID", typeof(string));
    nextJobsTable.Columns.Add("Resource_Description", typeof(string));
    nextJobsTable.Columns.Add("JobOper_JobNum", typeof(string));
    nextJobsTable.Columns.Add("JobHead_PartNum", typeof(string));
    nextJobsTable.Columns.Add("Part_PartDescription", typeof(string));
    nextJobsTable.Columns.Add("JobOper_AssemblySeq", typeof(int));
    nextJobsTable.Columns.Add("JobOper_OprSeq", typeof(int));
    nextJobsTable.Columns.Add("JobOper_OpDesc", typeof(string));
    nextJobsTable.Columns.Add("JobOper_StartDate", typeof(DateTime));
    nextJobsTable.Columns.Add("JobOper_DueDate", typeof(DateTime));
    nextJobsTable.Columns.Add("JobOper_EstimatedHours", typeof(decimal));
    nextJobsTable.Columns.Add("JobOper_EstSetHours", typeof(decimal));
    nextJobsTable.Columns.Add("JobOper_EstProdHours", typeof(decimal));
    nextJobsTable.Columns.Add("JobOpDtl_ResourceGrpID", typeof(string));
    
    // Create ResourceSummary table structure
    DataTable summaryTable = new DataTable("ResourceSummary");
    summaryTable.Columns.Add("Resource_Plant", typeof(string));
    summaryTable.Columns.Add("Resource_ResourceID", typeof(string));
    summaryTable.Columns.Add("Resource_Description", typeof(string));
    summaryTable.Columns.Add("Current_JobNum", typeof(string));
    summaryTable.Columns.Add("Current_PartNum", typeof(string));
    summaryTable.Columns.Add("Current_Status", typeof(string));
    summaryTable.Columns.Add("Next_JobNum", typeof(string));
    summaryTable.Columns.Add("Next_PartNum", typeof(string));
    summaryTable.Columns.Add("Next_ScheduledStart", typeof(DateTime));
    
    // =================================================================
    // Query Current Active Jobs
    // =================================================================
    
    // Get current jobs being worked on (active labor transactions)
    var currentJobs = (from r in Db.Resource
                      join ld in Db.LaborDtl on r.ResourceID equals ld.ResourceID into ldGroup
                      from ld in ldGroup.DefaultIfEmpty()
                      join jh in Db.JobHead on ld.JobNum equals jh.JobNum into jhGroup
                      from jh in jhGroup.DefaultIfEmpty()
                      join jo in Db.JobOper on new { ld.JobNum, ld.AssemblySeq, ld.OprSeq } 
                                              equals new { jo.JobNum, jo.AssemblySeq, jo.OprSeq } into joGroup
                      from jo in joGroup.DefaultIfEmpty()
                      join p in Db.Part on jh.PartNum equals p.PartNum into pGroup
                      from p in pGroup.DefaultIfEmpty()
                      join emp in Db.EmpBasic on ld.EmployeeNum equals emp.EmpID into empGroup
                      from emp in empGroup.DefaultIfEmpty()
                      where r.Company == companyID
                        && r.Inactive == false
                        && (ld == null || ld.ActiveTrans == true)
                        && (string.IsNullOrEmpty(siteFilter) || jh == null || jh.Plant == siteFilter)
                      select new
                      {
                          Site = jh != null ? jh.Plant ?? "" : "",
                          ResourceID = r.ResourceID ?? "",
                          ResourceDescription = r.Description ?? "",
                          JobNum = jh != null ? jh.JobNum : "",
                          PartNum = jh != null ? jh.PartNum : "",
                          PartDescription = p != null ? p.PartDescription : "",
                          AssemblySeq = ld != null ? ld.AssemblySeq : 0,
                          OprSeq = ld != null ? ld.OprSeq : 0,
                          OperationDescription = jo != null ? jo.OpDesc : "",
                          StartTime = ld != null && ld.ClockInDate.HasValue ? ld.ClockInDate : (DateTime?)null,
                          EmployeeID = ld != null ? ld.EmployeeNum : "",
                          EmployeeName = emp != null ? emp.Name : "",
                          Status = ld != null && ld.ActiveTrans == true ? "Active" : "Available",
                          QtyCompleted = jo != null ? jo.QtyCompleted : 0,
                          RunQty = jo != null ? jo.RunQty : 0
                      }).ToList();

    // Get current assembly jobs (based on BAQ structure - LaborDtl -> JobOpDtl -> ResourceGroup)
    var currentAssemblyJobs = (from ld in Db.LaborDtl
                              join jod in Db.JobOpDtl on new { ld.Company, ld.JobNum, ld.OprSeq }
                                                        equals new { jod.Company, jod.JobNum, jod.OprSeq }
                              join rg in Db.ResourceGroup on new { jod.Company, jod.ResourceGrpID }
                                                            equals new { rg.Company, rg.ResourceGrpID }
                              join jo in Db.JobOper on new { jod.Company, jod.JobNum, jod.AssemblySeq, jod.OprSeq }
                                                      equals new { jo.Company, jo.JobNum, jo.AssemblySeq, jo.OprSeq }
                              join ja in Db.JobAsmbl on new { jo.Company, jo.JobNum, jo.AssemblySeq }
                                                       equals new { ja.Company, ja.JobNum, ja.AssemblySeq }
                              join emp in Db.EmpBasic on new { ld.Company, ld.EmployeeNum }
                                                        equals new { emp.Company, EmployeeNum = emp.EmpID }
                              where ld.ActiveTrans == true
                                && ld.Company == companyID
                                && !machiningResourceGroups.Contains(rg.ResourceGrpID)
                                && (string.IsNullOrEmpty(siteFilter) || rg.Plant == siteFilter)
                                && !string.IsNullOrEmpty(ld.JobNum)
                                && !string.IsNullOrEmpty(emp.Name)
                              select new
                              {
                                  Site = rg.Plant ?? "",
                                  ResourceID = rg.ResourceGrpID,
                                  ResourceDescription = rg.ResourceGrpID ?? "",
                                  JobNum = ld.JobNum,
                                  PartNum = ja.PartNum ?? "",
                                  PartDescription = ja.Description ?? "",
                                  AssemblySeq = ld.AssemblySeq,
                                  OprSeq = ld.OprSeq,
                                  OperationDescription = jod.OpDtlDesc ?? "",
                                  StartTime = ld.ClockInDate,
                                  EmployeeID = ld.EmployeeNum ?? "",
                                  EmployeeName = emp.Name ?? "",
                                  Status = "Active",
                                  QtyCompleted = jo.QtyCompleted,
                                  RunQty = jo.RunQty
                              }).ToList();
    
    // Populate CurrentJobs tables (specific resources and general)
    foreach (var job in currentJobs)
    {
        // Skip if JobNum or EmployeeName are empty or null
        if (string.IsNullOrEmpty(job.JobNum) || string.IsNullOrEmpty(job.EmployeeName))
            continue;

        DataRow row;
        DataTable targetTable;

        // Check if this resource has its own specific table
        if (resourceTables.ContainsKey(job.ResourceID))
        {
            targetTable = resourceTables[job.ResourceID];
        }
        else
        {
            targetTable = currentJobsTable;
        }

        row = targetTable.NewRow();
        row["Resource_ResourceID"] = job.ResourceID;
        row["EmpBasic_Name"] = job.EmployeeName;
        row["JobHead_JobNum"] = job.JobNum;
        row["Part_PartDescription"] = job.PartDescription;
        row["JobOper_Completion"] = $"{job.QtyCompleted:0.##} / {job.RunQty:0.##}";
        targetTable.Rows.Add(row);
    }
    
    // =================================================================
    // Query Next Scheduled Jobs - Debug Version
    // =================================================================

    // Let's start simple and add filters one by one to see where we lose data

    // Step 1: Just get all JobOpDtl records with basic joins
    var allJobOpDtl = (from jod in Db.JobOpDtl
                      join jo in Db.JobOper on new { jod.JobNum, jod.AssemblySeq, jod.OprSeq }
                                              equals new { jo.JobNum, jo.AssemblySeq, jo.OprSeq }
                      join jh in Db.JobHead on jo.JobNum equals jh.JobNum
                      where jh.Company == companyID
                      select new { jod, jo, jh }).ToList();

    // Step 2: Filter for open jobs and incomplete operations
    var allIncompleteOps = allJobOpDtl.Where(x => x.jh.JobClosed == false && x.jo.OpComplete == false).ToList();

    // Step 2a: Try to get operations scheduled from today forward first
    var futureScheduledOps = allIncompleteOps.Where(x => x.jo.StartDate != null && x.jo.StartDate >= DateTime.Today).ToList();

    // Step 2b: If no future scheduled operations, fall back to all incomplete operations
    var incompleteOps = futureScheduledOps.Any() ? futureScheduledOps : allIncompleteOps;

    // Step 3: Filter by site if specified
    var siteFilteredOps = string.IsNullOrEmpty(siteFilter)
        ? incompleteOps
        : incompleteOps.Where(x => x.jh.Plant == siteFilter).ToList();

    // Step 4: Remove operations with active labor transactions
    var availableOps = siteFilteredOps.Where(x =>
        !Db.LaborDtl.Any(ld => ld.JobNum == x.jo.JobNum
                            && ld.AssemblySeq == x.jo.AssemblySeq
                            && ld.OprSeq == x.jo.OprSeq
                            && ld.ActiveTrans == true)).ToList();

    // Step 5: Remove operations where previous operations are not complete
    var readyOps = availableOps.Where(x =>
        !Db.JobOper.Any(prevOp => prevOp.JobNum == x.jo.JobNum
                               && prevOp.AssemblySeq == x.jo.AssemblySeq
                               && prevOp.OprSeq < x.jo.OprSeq
                               && prevOp.OpComplete == false)).ToList();

    // Step 6: Create the final result using ResourceGrpID to find actual resources
    var finalNextJobs = new List<dynamic>();
    foreach (var op in readyOps)
    {
        // Skip if ResourceGrpID is null or empty
        if (string.IsNullOrEmpty(op.jod.ResourceGrpID))
            continue;

        try
        {
            var part = Db.Part.FirstOrDefault(p => p.PartNum == op.jh.PartNum);

            // Find all resources in this resource group
            var resourcesInGroup = Db.Resource.Where(r => r.Company == companyID
                                                       && r.ResourceGrpID == op.jod.ResourceGrpID
                                                       && r.Inactive == false).ToList();

            // Create a next job entry for each resource in the group
            foreach (var resource in resourcesInGroup)
            {
                finalNextJobs.Add(new
                {
                    Site = op.jh.Plant ?? "",
                    ResourceID = resource.ResourceID,
                    ResourceDescription = resource.Description ?? "",
                    JobNum = op.jo.JobNum ?? "",
                    PartNum = op.jh.PartNum ?? "",
                    PartDescription = part?.PartDescription ?? "",
                    AssemblySeq = op.jo.AssemblySeq,
                    OprSeq = op.jo.OprSeq,
                    OperationDescription = op.jo.OpDesc ?? "",
                    ScheduledStartTime = op.jo.StartDate,
                    ScheduledEndTime = op.jo.DueDate,
                    EstimatedHours = op.jo.EstSetHours + op.jo.EstProdHours,
                    EstSetHours = op.jo.EstSetHours,
                    EstProdHours = op.jo.EstProdHours,
                    SchedPriority = op.jh.SchedPriority,
                    ResourceGrpID = op.jod.ResourceGrpID
                });
            }
        }
        catch (Exception ex)
        {
            // Skip this record if there's an error, but continue processing others
            continue;
        }
    }

    // Step 7: Get one job per resource, ordered by Epicor scheduling
    var groupedNextJobs = finalNextJobs.Where(x => !string.IsNullOrEmpty(x.ResourceID))
                                      .GroupBy(x => x.ResourceID)
                                      .Select(g => g.OrderBy(x => x.SchedPriority)
                                                   .ThenBy(x => x.ScheduledStartTime)
                                                   .ThenBy(x => x.JobNum)
                                                   .ThenBy(x => x.OprSeq)
                                                   .First())
                                      .ToList();

    finalNextJobs = groupedNextJobs;

    // =================================================================
    // Create Debug Table
    // =================================================================
    var debugTable = new DataTable("Debug");
    debugTable.Columns.Add("Step", typeof(string));
    debugTable.Columns.Add("Description", typeof(string));
    debugTable.Columns.Add("Count", typeof(int));

    debugTable.Rows.Add("1", "All JobOpDtl records with basic joins", allJobOpDtl.Count);
    debugTable.Rows.Add("2a", "All incomplete operations", allIncompleteOps.Count);
    debugTable.Rows.Add("2b", "Future scheduled operations", futureScheduledOps.Count);
    debugTable.Rows.Add("2c", "Selected operations (future or fallback)", incompleteOps.Count);
    debugTable.Rows.Add("3", "After site filter", siteFilteredOps.Count);
    debugTable.Rows.Add("4", "After removing active labor transactions", availableOps.Count);
    debugTable.Rows.Add("5", "After checking previous operations complete", readyOps.Count);
    debugTable.Rows.Add("5a", "Ready ops with non-empty ResourceGrpID", readyOps.Count(x => !string.IsNullOrEmpty(x.jod.ResourceGrpID)));
    debugTable.Rows.Add("6", "Final next jobs created", finalNextJobs.Count);
    debugTable.Rows.Add("7", "After grouping by ResourceID", groupedNextJobs.Count);
    
    // Populate NextJobs tables (specific resources and general)
    foreach (var job in finalNextJobs)
    {
        DataRow row;
        DataTable targetTable;

        // Check if this resource has its own specific table
        if (nextJobsResourceTables.ContainsKey(job.ResourceID))
        {
            targetTable = nextJobsResourceTables[job.ResourceID];
        }
        else
        {
            targetTable = nextJobsTable;
        }

        row = targetTable.NewRow();
        row["JobHead_Plant"] = job.Site;
        row["Resource_ResourceID"] = job.ResourceID;
        row["Resource_Description"] = job.ResourceDescription;
        row["JobOper_JobNum"] = job.JobNum;
        row["JobHead_PartNum"] = job.PartNum;
        row["Part_PartDescription"] = job.PartDescription;
        row["JobOper_AssemblySeq"] = job.AssemblySeq;
        row["JobOper_OprSeq"] = job.OprSeq;
        row["JobOper_OpDesc"] = job.OperationDescription;
        row["JobOper_StartDate"] = job.ScheduledStartTime ?? (object)DBNull.Value;
        row["JobOper_DueDate"] = job.ScheduledEndTime ?? (object)DBNull.Value;
        row["JobOper_EstimatedHours"] = job.EstimatedHours;
        row["JobOper_EstSetHours"] = job.EstSetHours;
        row["JobOper_EstProdHours"] = job.EstProdHours;
        row["JobOpDtl_ResourceGrpID"] = job.ResourceGrpID;
        targetTable.Rows.Add(row);
    }
    
    // =================================================================
    // Create Resource Summary
    // =================================================================
    
    // Get all unique resources and combine current/next job info
    var currentResources = currentJobs.Select(j => new { Site = j.Site, ResourceID = j.ResourceID, ResourceDescription = j.ResourceDescription }).ToList();
    var nextResources = finalNextJobs.Select(j => new { Site = j.Site, ResourceID = j.ResourceID, ResourceDescription = j.ResourceDescription }).ToList();

    var allResources = new List<dynamic>();
    allResources.AddRange(currentResources);
    allResources.AddRange(nextResources);

    var uniqueResources = allResources.GroupBy(r => new { r.Site, r.ResourceID })
                                     .Select(g => g.First())
                                     .OrderBy(r => r.Site)
                                     .ThenBy(r => r.ResourceID);

    foreach (var resource in uniqueResources)
    {
        var currentJob = currentJobs.FirstOrDefault(j => j.ResourceID == resource.ResourceID && j.Status == "Active");
        var nextJob = finalNextJobs.FirstOrDefault(j => j.ResourceID == resource.ResourceID);
        
        DataRow row = summaryTable.NewRow();
        row["Resource_Plant"] = resource.Site;
        row["Resource_ResourceID"] = resource.ResourceID;
        row["Resource_Description"] = resource.ResourceDescription;
        row["Current_JobNum"] = currentJob?.JobNum ?? "";
        row["Current_PartNum"] = currentJob?.PartNum ?? "";
        row["Current_Status"] = currentJob?.Status ?? "Available";
        row["Next_JobNum"] = nextJob?.JobNum ?? "";
        row["Next_PartNum"] = nextJob?.PartNum ?? "";
        row["Next_ScheduledStart"] = nextJob?.ScheduledStartTime ?? (object)DBNull.Value;
        summaryTable.Rows.Add(row);
    }
    
    // =================================================================
    // Create Consolidated Machining Tables
    // =================================================================

    // Create AllCurrentJobsMachining table (all specific resources combined)
    var allCurrentJobsMachining = new DataTable("AllCurrentJobsMachining");
    allCurrentJobsMachining.Columns.Add("Resource_ResourceID", typeof(string));
    allCurrentJobsMachining.Columns.Add("EmpBasic_Name", typeof(string));
    allCurrentJobsMachining.Columns.Add("JobHead_JobNum", typeof(string));
    allCurrentJobsMachining.Columns.Add("Part_PartDescription", typeof(string));
    allCurrentJobsMachining.Columns.Add("JobOper_Completion", typeof(string));

    // Add all current jobs from specific resources, sorted alphabetically by ResourceID
    var allCurrentMachiningJobs = currentJobs.Where(j => specificResources.Contains(j.ResourceID)
                                                       && !string.IsNullOrEmpty(j.JobNum)
                                                       && !string.IsNullOrEmpty(j.EmployeeName))
                                            .OrderBy(j => j.ResourceID)
                                            .ToList();

    foreach (var job in allCurrentMachiningJobs)
    {
        DataRow row = allCurrentJobsMachining.NewRow();
        row["Resource_ResourceID"] = job.ResourceID;
        row["EmpBasic_Name"] = job.EmployeeName;
        row["JobHead_JobNum"] = job.JobNum;
        row["Part_PartDescription"] = job.PartDescription;
        row["JobOper_Completion"] = $"{job.QtyCompleted:0.##} / {job.RunQty:0.##}";
        allCurrentJobsMachining.Rows.Add(row);
    }

    // Create AllNextJobsMachining table (all specific resources combined)
    var allNextJobsMachining = new DataTable("AllNextJobsMachining");
    allNextJobsMachining.Columns.Add("JobHead_Plant", typeof(string));
    allNextJobsMachining.Columns.Add("Resource_ResourceID", typeof(string));
    allNextJobsMachining.Columns.Add("Resource_Description", typeof(string));
    allNextJobsMachining.Columns.Add("JobOper_JobNum", typeof(string));
    allNextJobsMachining.Columns.Add("JobHead_PartNum", typeof(string));
    allNextJobsMachining.Columns.Add("Part_PartDescription", typeof(string));
    allNextJobsMachining.Columns.Add("JobOper_AssemblySeq", typeof(int));
    allNextJobsMachining.Columns.Add("JobOper_OprSeq", typeof(int));
    allNextJobsMachining.Columns.Add("JobOper_OpDesc", typeof(string));
    allNextJobsMachining.Columns.Add("JobOper_StartDate", typeof(DateTime));
    allNextJobsMachining.Columns.Add("JobOper_DueDate", typeof(DateTime));
    allNextJobsMachining.Columns.Add("JobOper_EstimatedHours", typeof(decimal));
    allNextJobsMachining.Columns.Add("JobOper_EstSetHours", typeof(decimal));
    allNextJobsMachining.Columns.Add("JobOper_EstProdHours", typeof(decimal));
    allNextJobsMachining.Columns.Add("JobOpDtl_ResourceGrpID", typeof(string));

    // Add all next jobs from specific resources, sorted alphabetically by ResourceID
    var allNextMachiningJobs = finalNextJobs.Where(j => specificResources.Contains(j.ResourceID))
                                           .OrderBy(j => j.ResourceID)
                                           .ToList();

    foreach (var job in allNextMachiningJobs)
    {
        DataRow row = allNextJobsMachining.NewRow();
        row["JobHead_Plant"] = job.Site;
        row["Resource_ResourceID"] = job.ResourceID;
        row["Resource_Description"] = job.ResourceDescription;
        row["JobOper_JobNum"] = job.JobNum;
        row["JobHead_PartNum"] = job.PartNum;
        row["Part_PartDescription"] = job.PartDescription;
        row["JobOper_AssemblySeq"] = job.AssemblySeq;
        row["JobOper_OprSeq"] = job.OprSeq;
        row["JobOper_OpDesc"] = job.OperationDescription;
        row["JobOper_StartDate"] = job.ScheduledStartTime ?? (object)DBNull.Value;
        row["JobOper_DueDate"] = job.ScheduledEndTime ?? (object)DBNull.Value;
        row["JobOper_EstimatedHours"] = job.EstimatedHours;
        row["JobOper_EstSetHours"] = job.EstSetHours;
        row["JobOper_EstProdHours"] = job.EstProdHours;
        row["JobOpDtl_ResourceGrpID"] = job.ResourceGrpID;
        allNextJobsMachining.Rows.Add(row);
    }

    // Add tables to DataSet
    result.Tables.Add(currentJobsTable);  // General current jobs

    // Add specific CurrentJobs resource tables
    foreach (var kvp in resourceTables)
    {
        result.Tables.Add(kvp.Value);
    }

    result.Tables.Add(nextJobsTable);  // General next jobs

    // Add specific NextJobs resource tables
    foreach (var kvp in nextJobsResourceTables)
    {
        result.Tables.Add(kvp.Value);
    }

    // =================================================================
    // Create Consolidated Assembly Tables (all non-machining resources)
    // =================================================================

    // Create AllCurrentJobsAssembly table (all non-specific resources combined)
    var allCurrentJobsAssembly = new DataTable("AllCurrentJobsAssembly");
    allCurrentJobsAssembly.Columns.Add("Resource_ResourceID", typeof(string));
    allCurrentJobsAssembly.Columns.Add("EmpBasic_Name", typeof(string));
    allCurrentJobsAssembly.Columns.Add("JobHead_JobNum", typeof(string));
    allCurrentJobsAssembly.Columns.Add("Part_PartDescription", typeof(string));
    allCurrentJobsAssembly.Columns.Add("JobOper_Completion", typeof(string));

    // Add all current assembly jobs (based on resource groups), sorted alphabetically by ResourceID
    var allCurrentAssemblyJobsSorted = currentAssemblyJobs.OrderBy(j => j.ResourceID).ToList();

    foreach (var job in allCurrentAssemblyJobsSorted)
    {
        DataRow row = allCurrentJobsAssembly.NewRow();
        row["Resource_ResourceID"] = job.ResourceID; // This is actually ResourceGrpID
        row["EmpBasic_Name"] = job.EmployeeName;
        row["JobHead_JobNum"] = job.JobNum;
        row["Part_PartDescription"] = job.PartDescription;
        row["JobOper_Completion"] = $"{job.QtyCompleted:0.##} / {job.RunQty:0.##}";
        allCurrentJobsAssembly.Rows.Add(row);
    }

    // Create AllNextJobsAssembly table (all non-specific resources combined)
    var allNextJobsAssembly = new DataTable("AllNextJobsAssembly");
    allNextJobsAssembly.Columns.Add("JobHead_Plant", typeof(string));
    allNextJobsAssembly.Columns.Add("Resource_ResourceID", typeof(string));
    allNextJobsAssembly.Columns.Add("Resource_Description", typeof(string));
    allNextJobsAssembly.Columns.Add("JobOper_JobNum", typeof(string));
    allNextJobsAssembly.Columns.Add("JobHead_PartNum", typeof(string));
    allNextJobsAssembly.Columns.Add("Part_PartDescription", typeof(string));
    allNextJobsAssembly.Columns.Add("JobOper_AssemblySeq", typeof(int));
    allNextJobsAssembly.Columns.Add("JobOper_OprSeq", typeof(int));
    allNextJobsAssembly.Columns.Add("JobOper_OpDesc", typeof(string));
    allNextJobsAssembly.Columns.Add("JobOper_StartDate", typeof(DateTime));
    allNextJobsAssembly.Columns.Add("JobOper_DueDate", typeof(DateTime));
    allNextJobsAssembly.Columns.Add("JobOper_EstimatedHours", typeof(decimal));
    allNextJobsAssembly.Columns.Add("JobOper_EstSetHours", typeof(decimal));
    allNextJobsAssembly.Columns.Add("JobOper_EstProdHours", typeof(decimal));
    allNextJobsAssembly.Columns.Add("JobOpDtl_ResourceGrpID", typeof(string));

    // Add all next jobs from non-machining resources, sorted alphabetically by ResourceID
    var allNextAssemblyJobs = finalNextJobs.Where(j => !specificResources.Contains(j.ResourceID)
                                                     && !machiningResourceGroups.Contains(j.ResourceGrpID))
                                          .OrderBy(j => j.ResourceID)
                                          .ToList();

    foreach (var job in allNextAssemblyJobs)
    {
        DataRow row = allNextJobsAssembly.NewRow();
        row["JobHead_Plant"] = job.Site;
        row["Resource_ResourceID"] = job.ResourceID;
        row["Resource_Description"] = job.ResourceDescription;
        row["JobOper_JobNum"] = job.JobNum;
        row["JobHead_PartNum"] = job.PartNum;
        row["Part_PartDescription"] = job.PartDescription;
        row["JobOper_AssemblySeq"] = job.AssemblySeq;
        row["JobOper_OprSeq"] = job.OprSeq;
        row["JobOper_OpDesc"] = job.OperationDescription;
        row["JobOper_StartDate"] = job.ScheduledStartTime ?? (object)DBNull.Value;
        row["JobOper_DueDate"] = job.ScheduledEndTime ?? (object)DBNull.Value;
        row["JobOper_EstimatedHours"] = job.EstimatedHours;
        row["JobOper_EstSetHours"] = job.EstSetHours;
        row["JobOper_EstProdHours"] = job.EstProdHours;
        row["JobOpDtl_ResourceGrpID"] = job.ResourceGrpID;
        allNextJobsAssembly.Rows.Add(row);
    }

    // Add consolidated machining tables
    result.Tables.Add(allCurrentJobsMachining);
    result.Tables.Add(allNextJobsMachining);

    // Add consolidated assembly tables
    result.Tables.Add(allCurrentJobsAssembly);
    result.Tables.Add(allNextJobsAssembly);
    result.Tables.Add(summaryTable);
    result.Tables.Add(debugTable);
}
catch (Exception ex)
{
    // Handle any errors and provide detailed error information
    errorMessage = "Error retrieving resource job status: " + ex.Message;
    
    // Create error table in DataSet
    DataTable errorTable = new DataTable("Error");
    errorTable.Columns.Add("ErrorMessage", typeof(string));
    errorTable.Columns.Add("ErrorDetails", typeof(string));
    
    DataRow errorRow = errorTable.NewRow();
    errorRow["ErrorMessage"] = "Error retrieving resource job status";
    errorRow["ErrorDetails"] = ex.Message;
    errorTable.Rows.Add(errorRow);
    
    result.Tables.Clear();
    result.Tables.Add(errorTable);
}